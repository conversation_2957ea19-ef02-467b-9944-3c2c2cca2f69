#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الطريقة الأصلية لإرسال الهوت سبوت
"""

import sys
import os
import json
import traceback

def test_original_method():
    """اختبار الطريقة الأصلية لإرسال الهوت سبوت"""
    print("🎯 اختبار الطريقة الأصلية لإرسال الهوت سبوت")
    print("=" * 70)
    
    try:
        # قراءة إعدادات الاتصال
        config_file = "config/mikrotik_settings.json"
        if not os.path.exists(config_file):
            print("❌ ملف الإعدادات غير موجود")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        # فك ترميز كلمة المرور
        def decrypt_password(encrypted_password):
            try:
                if not encrypted_password:
                    return ""
                import base64
                decoded = base64.b64decode(encrypted_password.encode('utf-8')).decode('utf-8')
                return decoded
            except Exception as e:
                return encrypted_password
        
        decrypted_password = decrypt_password(settings.get('api_password', ''))
        
        print("📋 إعدادات الاتصال:")
        print(f"   🌐 IP: {settings.get('api_ip', 'غير محدد')}")
        print(f"   👤 Username: {settings.get('api_username', 'غير محدد')}")
        
        # التحقق من المكتبة المطلوبة
        try:
            import routeros_api
            print("✅ مكتبة routeros_api متوفرة")
        except ImportError:
            print("❌ مكتبة routeros_api غير متوفرة")
            return False
        
        # محاولة الاتصال
        print("\n🔗 الاتصال بـ MikroTik...")
        
        try:
            api_connection = routeros_api.RouterOsApiPool(
                host=settings['api_ip'],
                username=settings['api_username'],
                password=decrypted_password,
                port=int(settings.get('api_port', 8728)),
                plaintext_login=True
            )
            
            api = api_connection.get_api()
            
            # اختبار الاتصال
            identity = api.get_resource('/system/identity').get()
            router_name = identity[0].get('name', 'غير معروف') if identity else 'غير معروف'
            print(f"✅ نجح الاتصال مع MikroTik: {router_name}")
            
            # اختبار قراءة مستخدمي الهوت سبوت بالطريقة الأصلية
            print("\n📊 اختبار قراءة مستخدمي الهوت سبوت بالطريقة الأصلية...")
            try:
                existing_users = api.get_resource('/ip/hotspot/user').get()
                existing_usernames = {user['name'] for user in existing_users}
                print(f"   ✅ تم قراءة {len(existing_users)} مستخدم بنجاح!")
                
                if existing_users:
                    print("   👥 أول 3 مستخدمين:")
                    for i, user in enumerate(existing_users[:3]):
                        name = user.get('name', 'غير معروف')
                        profile = user.get('profile', 'غير محدد')
                        print(f"      {i+1}. {name} - {profile}")
                
            except Exception as read_error:
                print(f"   ❌ خطأ في قراءة المستخدمين: {str(read_error)}")
                print(f"   📍 تفاصيل: {traceback.format_exc()}")
                return False
            
            # اختبار إنشاء مستخدم جديد بالطريقة الأصلية
            print("\n🧪 اختبار إنشاء مستخدم جديد بالطريقة الأصلية...")
            
            from datetime import datetime
            timestamp = datetime.now().strftime("%H%M%S")
            
            # بيانات المستخدم الاختبار (مطابقة للطريقة الأصلية)
            cred_username = f"original_test_{timestamp}"
            server = "hotspot1"  # اسم الخادم
            limit_bytes = "1"
            limit_unit = "GB"
            days = "7"
            email_template = "@test.com"
            
            # استخدام الطريقة الأصلية بالضبط
            params = {'name': cred_username, 'password': "test123", 'profile': "default",
                      'comment': "اختبار الطريقة الأصلية", 'server': server}
            if limit_bytes:
                params['limit-bytes-total'] = str(int(float(limit_bytes) * (1073741824 if limit_unit == "GB" else 1048576)))
            if days:
                params['email'] = f"{days}{email_template}"
            
            print(f"   📝 بيانات المستخدم الاختبار (الطريقة الأصلية):")
            for key, value in params.items():
                print(f"      • {key}: '{value}'")
            
            # محاولة إضافة المستخدم
            try:
                print("   🔄 محاولة إنشاء المستخدم بالطريقة الأصلية...")
                result = api.get_resource('/ip/hotspot/user').add(**params)
                print(f"   ✅ تم إنشاء المستخدم بنجاح بالطريقة الأصلية!")
                
                # محاولة قراءة المستخدم المُنشأ
                try:
                    print("   📖 اختبار قراءة المستخدم المُنشأ...")
                    updated_users = api.get_resource('/ip/hotspot/user').get()
                    found = False
                    for user in updated_users:
                        if user.get('name') == cred_username:
                            found = True
                            print(f"   ✅ تم العثور على المستخدم المُنشأ:")
                            print(f"      Name: {user.get('name', 'N/A')}")
                            print(f"      Profile: {user.get('profile', 'N/A')}")
                            print(f"      Comment: {user.get('comment', 'N/A')}")
                            print(f"      Server: {user.get('server', 'N/A')}")
                            print(f"      Email: {user.get('email', 'N/A')}")
                            break
                    
                    if not found:
                        print("   ⚠️ لم يتم العثور على المستخدم المُنشأ")
                        
                except Exception as read_new_error:
                    print(f"   ❌ خطأ في قراءة المستخدم المُنشأ: {str(read_new_error)}")
                
                # محاولة حذف المستخدم الاختبار
                try:
                    print("   🗑️ محاولة حذف المستخدم الاختبار...")
                    # البحث عن المستخدم وحذفه
                    test_users = api.get_resource('/ip/hotspot/user').get(name=cred_username)
                    if test_users:
                        for user_to_delete in test_users:
                            user_id = user_to_delete.get('.id')
                            if user_id:
                                api.get_resource('/ip/hotspot/user').remove(user_id)
                                print("   ✅ تم حذف المستخدم الاختبار بنجاح!")
                                break
                    else:
                        print("   ⚠️ لم يتم العثور على المستخدم للحذف")
                except Exception as delete_error:
                    print(f"   ⚠️ خطأ في حذف المستخدم: {str(delete_error)}")
                
            except Exception as create_error:
                print(f"   ❌ فشل في إنشاء المستخدم: {str(create_error)}")
                print(f"   📍 تفاصيل: {traceback.format_exc()}")
                return False
            
            # إغلاق الاتصال
            api_connection.disconnect()
            print("\n🔌 تم قطع الاتصال بنجاح")
            
            print("\n🎉 اختبار الطريقة الأصلية نجح بالكامل!")
            print("\n📋 النتائج:")
            print("   ✅ الاتصال بـ MikroTik يعمل")
            print("   ✅ قراءة مستخدمي الهوت سبوت تعمل بالطريقة الأصلية")
            print("   ✅ إنشاء مستخدم جديد يعمل بالطريقة الأصلية")
            print("   ✅ قراءة المستخدم المُنشأ تعمل")
            print("   ✅ حذف المستخدم يعمل")
            print("   ✅ الطريقة الأصلية تعمل بدون مشاكل!")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار الطريقة الأصلية لإرسال الهوت سبوت")
    print("=" * 80)
    
    success = test_original_method()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: الطريقة الأصلية تعمل بشكل مثالي!")
        print("\n🎉 تم تطبيق الطريقة الأصلية في البرنامج")
        print("💡 يمكنك الآن استخدام برنامج card لإرسال الهوت سبوت بثقة")
        print("🔧 البرنامج يستخدم الآن نفس الطريقة الموجودة في الملف الأصلي")
    else:
        print("🔧 النتيجة النهائية: ما زالت هناك مشاكل تحتاج مراجعة")
    
    input("\nاضغط Enter للخروج...")
