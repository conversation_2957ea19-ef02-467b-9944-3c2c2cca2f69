#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف قوي لجميع مستخدمي الهوت سبوت المعطوبين
"""

import sys
import os
import json
import traceback

def force_clean_mikrotik():
    """حذف قوي لجميع المستخدمين المعطوبين"""
    print("💪 حذف قوي لجميع مستخدمي الهوت سبوت المعطوبين")
    print("=" * 70)
    
    try:
        # قراءة إعدادات الاتصال
        config_file = "config/mikrotik_settings.json"
        if not os.path.exists(config_file):
            print("❌ ملف الإعدادات غير موجود")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        # فك ترميز كلمة المرور
        def decrypt_password(encrypted_password):
            try:
                if not encrypted_password:
                    return ""
                import base64
                decoded = base64.b64decode(encrypted_password.encode('utf-8')).decode('utf-8')
                return decoded
            except Exception as e:
                return encrypted_password
        
        decrypted_password = decrypt_password(settings.get('api_password', ''))
        
        print("📋 إعدادات الاتصال:")
        print(f"   🌐 IP: {settings.get('api_ip', 'غير محدد')}")
        print(f"   👤 Username: {settings.get('api_username', 'غير محدد')}")
        
        # التحقق من المكتبة المطلوبة
        try:
            import routeros_api
            print("✅ مكتبة routeros_api متوفرة")
        except ImportError:
            print("❌ مكتبة routeros_api غير متوفرة")
            return False
        
        # محاولة الاتصال
        print("\n🔗 الاتصال بـ MikroTik...")
        
        try:
            api_connection = routeros_api.RouterOsApiPool(
                host=settings['api_ip'],
                username=settings['api_username'],
                password=decrypted_password,
                port=int(settings.get('api_port', 8728)),
                plaintext_login=True
            )
            
            api = api_connection.get_api()
            
            # اختبار الاتصال
            identity = api.get_resource('/system/identity').get()
            router_name = identity[0].get('name', 'غير معروف') if identity else 'غير معروف'
            print(f"✅ نجح الاتصال مع MikroTik: {router_name}")
            
            print("\n⚠️ تحذير: سيتم حذف جميع مستخدمي الهوت سبوت!")
            print("💾 تأكد من عمل نسخة احتياطية إذا كان لديك مستخدمين مهمين")
            
            response = input("\nهل تريد حذف جميع مستخدمي الهوت سبوت؟ (y/n): ")
            
            if response.lower() not in ['y', 'yes', 'نعم']:
                print("تم إلغاء العملية")
                api_connection.disconnect()
                return False
            
            # الحصول على جميع IDs
            print("\n🔍 الحصول على قائمة المستخدمين...")
            try:
                user_ids_response = api.get_resource('/ip/hotspot/user').call('print', {'proplist': '.id'})
                print(f"   📊 عدد المستخدمين: {len(user_ids_response)}")
                
                if len(user_ids_response) == 0:
                    print("   ✅ لا توجد مستخدمين")
                    api_connection.disconnect()
                    return True
                
                # حذف جميع المستخدمين واحد تلو الآخر
                print(f"\n🗑️ حذف {len(user_ids_response)} مستخدم...")
                deleted_count = 0
                
                for i, user_id_info in enumerate(user_ids_response):
                    user_id = user_id_info.get('.id', '')
                    if not user_id:
                        continue
                    
                    try:
                        api.get_resource('/ip/hotspot/user').call('remove', {'numbers': user_id})
                        deleted_count += 1
                        print(f"      ✅ تم حذف المستخدم {i+1}/{len(user_ids_response)}")
                    except Exception as delete_error:
                        print(f"      ❌ فشل حذف المستخدم {i+1}: {str(delete_error)}")
                
                print(f"\n🎉 تم حذف {deleted_count} من {len(user_ids_response)} مستخدم")
                
                # التحقق من أن جميع المستخدمين تم حذفهم
                print("\n🔍 التحقق من نظافة قاعدة البيانات...")
                try:
                    remaining_users = api.get_resource('/ip/hotspot/user').get()
                    if len(remaining_users) == 0:
                        print("   ✅ قاعدة البيانات نظيفة تماماً!")
                    else:
                        print(f"   ⚠️ ما زال هناك {len(remaining_users)} مستخدم")
                except UnicodeDecodeError:
                    print("   ❌ ما زالت هناك مشكلة ترميز - قد تحتاج إعادة تشغيل MikroTik")
                except Exception as check_error:
                    print(f"   ⚠️ خطأ في التحقق: {str(check_error)}")
                
            except Exception as list_error:
                print(f"   ❌ خطأ في الحصول على قائمة المستخدمين: {str(list_error)}")
                api_connection.disconnect()
                return False
            
            # اختبار إنشاء مستخدم جديد
            print("\n🧪 اختبار إنشاء مستخدم جديد...")
            
            from datetime import datetime
            timestamp = datetime.now().strftime("%H%M%S")
            test_username = f"clean_test_{timestamp}"
            
            test_params = {
                'name': test_username,
                'password': "test123",
                'profile': "default"
            }
            
            try:
                result = api.get_resource('/ip/hotspot/user').add(**test_params)
                print(f"   ✅ تم إنشاء المستخدم الاختبار بنجاح!")
                
                # اختبار قراءة المستخدم
                try:
                    test_users = api.get_resource('/ip/hotspot/user').get()
                    print(f"   ✅ تم قراءة {len(test_users)} مستخدم بدون مشاكل ترميز!")
                    
                    # حذف المستخدم الاختبار
                    for user in test_users:
                        if user.get('name') == test_username:
                            user_id = user.get('.id')
                            if user_id:
                                api.get_resource('/ip/hotspot/user').remove(user_id)
                                print("   ✅ تم حذف المستخدم الاختبار")
                                break
                    
                except Exception as read_test_error:
                    print(f"   ❌ خطأ في قراءة المستخدم الاختبار: {str(read_test_error)}")
                
            except Exception as create_test_error:
                print(f"   ❌ فشل في إنشاء المستخدم الاختبار: {str(create_test_error)}")
            
            # إغلاق الاتصال
            api_connection.disconnect()
            print("\n🔌 تم قطع الاتصال بنجاح")
            
            print("\n🎉 تنظيف قاعدة البيانات اكتمل!")
            print("\n💡 التوصيات:")
            print("   1. قاعدة البيانات الآن نظيفة تماماً")
            print("   2. يمكن الآن إرسال كروت الهوت سبوت بدون أي مشاكل")
            print("   3. جرب إنشاء مستخدمين جدد من البرنامج")
            print("   4. إذا استمرت المشكلة، قد تحتاج إعادة تشغيل MikroTik")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في التنظيف: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء التنظيف القوي لقاعدة بيانات MikroTik")
    print("=" * 80)
    print("⚠️ تحذير قوي: هذا البرنامج سيحذف جميع مستخدمي الهوت سبوت!")
    print("💾 تأكد من عمل نسخة احتياطية قبل المتابعة")
    print("🔄 قد تحتاج إعادة تشغيل MikroTik بعد التنظيف")
    print()
    
    response = input("هل تريد المتابعة مع التنظيف القوي؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم']:
        success = force_clean_mikrotik()
        
        print("\n" + "=" * 80)
        if success:
            print("🎯 نتيجة التنظيف: تم تنظيف قاعدة البيانات بالكامل!")
            print("\n🎉 يمكنك الآن استخدام برنامج card لإرسال الهوت سبوت")
            print("💡 إذا استمرت المشكلة، أعد تشغيل MikroTik")
        else:
            print("🔧 نتيجة التنظيف: هناك مشاكل تحتاج مراجعة إضافية")
    else:
        print("تم إلغاء عملية التنظيف.")
    
    input("\nاضغط Enter للخروج...")
