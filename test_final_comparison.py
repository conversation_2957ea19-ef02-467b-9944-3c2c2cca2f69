#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مقارنة نهائية بين السكريبت المولد والأصلي
"""

import sys
import os
import json
import traceback
from datetime import datetime

# محاكاة جزء من فئة MikroTikCardGenerator للاختبار
class TestFinalComparison:
    def __init__(self):
        self.generated_credentials = []
        self.system_type = 'user_manager'
        self.caller_id_bind_var = type('MockVar', (), {'get': lambda self: True})()
        self.user_email = type('MockEntry', (), {'get': lambda self: ''})()  # بدون إيميل
        self.customer_entry = type('MockEntry', (), {'get': lambda self: 'admin', 'strip': lambda self: 'admin'})()
        self.profile_combo = type('MockCombo', (), {'get': lambda self: 'card10um', 'strip': lambda self: 'card10um'})()
        self.version_combo = type('MockCombo', (), {'get': lambda self: 'v6'})()
        
    def generate_test_credentials_like_original(self):
        """توليد بيانات اختبار مطابقة للأصلي"""
        self.generated_credentials = []
        
        usernames = [
            "0140147731", "0119141874", "0190655633", "0192360133", 
            "0140307688", "0132455383", "0112612390", "0133227234",
            "0132804261", "0175676378", "0175213353", "0170022635",
            "0161951276", "0159102978", "0145522754", "0142950530"
        ]
        
        for username in usernames:
            cred = {
                "username": username,
                "password": "",  # بدون كلمة مرور مثل الأصلي
                "profile": "card10um",
                "comment": "test",
                "location": "1",
                "email": "",
                "price": "10"
            }
            
            self.generated_credentials.append(cred)
        
        print(f"✅ تم توليد {len(self.generated_credentials)} بيانات اختبار مطابقة للأصلي")
    
    def generate_user_manager_fast_script_final(self):
        """توليد سكريبت User Manager النهائي المطابق للأصلي"""
        try:
            version = self.version_combo.get()
            if version != 'v6':
                print("معلومات: الوضع السريع جداً متاح فقط لـ User Manager إصدار 6")
                return None

            customer = self.customer_entry.get().strip()
            profile = self.profile_combo.get().strip()

            if not customer or not profile:
                print("خطأ: يرجى تحديد العميل والبروفايل")
                return None

            # إنشاء سكريبت محسن بـ array
            script_lines = [':local usr {']
            for cred in self.generated_credentials:
                script_lines.append(f'    "{cred["username"]}"="{cred["password"]}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,p in=$usr do={'
            ])

            # إعداد المعاملات الإضافية
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var.get() else ""
            email_param = f' email="{self.user_email.get()}"' if self.user_email.get() else ""

            add_user_cmd = f'/tool user-manager user add username=$u password=$p customer="{customer}"{caller_id_param} first-name="{profile}"{email_param}'

            script_lines.extend([
                '    :do {',
                f'        {add_user_cmd};',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            # إضافة أوامر التنظيف مثل الأصلي
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";',
                '',
                ':put "🧹 تنظيف السكريبت...";',
                f'/system script remove [/system script find where name="fast_pdf_user_manager_{timestamp}"];',
                f'/system scheduler remove [/system scheduler find where name="fast_pdf_user_manager_{timestamp}"];',
                ':put "🎉 تم الانتهاء من العملية بنجاح!";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"خطأ في توليد سكريبت User Manager: {str(e)}")
            return None
    
    def compare_with_original(self):
        """مقارنة مع السكريبت الأصلي"""
        try:
            print("🔍 مقارنة مع السكريبت الأصلي...")
            
            # 1. توليد بيانات مطابقة للأصلي
            print("\n📊 توليد بيانات مطابقة للأصلي...")
            self.generate_test_credentials_like_original()
            
            # 2. توليد السكريبت
            print("\n🛠️ توليد السكريبت...")
            script = self.generate_user_manager_fast_script_final()
            
            if not script:
                print("❌ فشل في توليد السكريبت!")
                return False
            
            print("✅ تم توليد السكريبت بنجاح")
            
            # 3. فحص العناصر المطلوبة
            print("\n🔍 فحص العناصر المطلوبة...")
            
            lines = script.split('\n')
            
            # فحص العناصر الأساسية
            checks = {
                'local_usr': any(':local usr {' in line for line in lines),
                'start_message': any('🚀 بدء الوضع السريع' in line for line in lines),
                'foreach_loop': any(':foreach u,p in=$usr do={' in line for line in lines),
                'add_user_cmd': any('/tool user-manager user add username=$u password=$p' in line for line in lines),
                'activate_profile': any('create-and-activate-profile' in line for line in lines),
                'cleanup_script': any('🧹 تنظيف السكريبت' in line for line in lines),
                'remove_script': any('/system script remove' in line for line in lines),
                'remove_scheduler': any('/system scheduler remove' in line for line in lines),
                'final_success': any('🎉 تم الانتهاء من العملية بنجاح' in line for line in lines)
            }
            
            print("📋 فحص العناصر:")
            all_passed = True
            for check_name, result in checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {result}")
                if not result:
                    all_passed = False
            
            # فحص بنية المستخدمين
            user_lines = [line for line in lines if '"01' in line and '=""' in line]
            print(f"\n👥 عدد المستخدمين في السكريبت: {len(user_lines)}")
            print(f"👥 عدد المستخدمين المتوقع: {len(self.generated_credentials)}")
            
            if len(user_lines) != len(self.generated_credentials):
                print("❌ عدد المستخدمين غير متطابق!")
                all_passed = False
            else:
                print("✅ عدد المستخدمين متطابق!")
            
            # فحص كلمات المرور الفارغة
            empty_passwords = sum(1 for line in user_lines if '=""' in line)
            print(f"🔒 المستخدمين بكلمات مرور فارغة: {empty_passwords}")
            
            if empty_passwords == len(self.generated_credentials):
                print("✅ جميع كلمات المرور فارغة كما هو مطلوب!")
            else:
                print("❌ كلمات المرور ليست فارغة!")
                all_passed = False
            
            return all_passed
            
        except Exception as e:
            print(f"❌ خطأ في المقارنة: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False

def test_final_script_comparison():
    """اختبار المقارنة النهائية للسكريبت"""
    print("🎯 اختبار المقارنة النهائية للسكريبت")
    print("=" * 70)
    
    try:
        # إنشاء كائن الاختبار
        test_obj = TestFinalComparison()
        
        # مقارنة مع الأصلي
        success = test_obj.compare_with_original()
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار المقارنة النهائية")
    print("=" * 80)
    
    success = test_final_script_comparison()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: السكريبت مطابق للأصلي!")
        print("\n🎉 العناصر المطابقة:")
        print("   • بنية المستخدمين مع كلمات مرور فارغة")
        print("   • رسائل البداية والتقدم")
        print("   • أوامر إضافة المستخدمين")
        print("   • أوامر تفعيل البروفايلات")
        print("   • أوامر التنظيف والحذف")
        print("   • الرموز التعبيرية الجميلة")
        print("\n🎊 السكريبت الآن مطابق تماماً للأصلي!")
    else:
        print("🔧 النتيجة النهائية: ما زالت هناك اختلافات عن الأصلي")
    
    input("\nاضغط Enter للخروج...")
