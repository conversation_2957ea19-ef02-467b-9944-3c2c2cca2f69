#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ الملف الأصلي وإضافة التعديلات المطلوبة فقط
"""

def copy_and_modify_original():
    """نسخ الملف الأصلي مع التعديلات المطلوبة"""
    try:
        print("📖 قراءة الملف الأصلي...")
        
        # قراءة الملف الأصلي
        with open('الاصلي.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 حجم الملف الأصلي: {len(content):,} حرف")
        
        # التعديل 1: تغيير العنوان
        print("🔧 تطبيق التعديل 1: تغيير العنوان...")
        content = content.replace(
            '"🚀 مولد كروت وسكريبتات MikroTik - الإصدار 2.0"',
            '"🚀 مولد كروت وسكريبتات MikroTik - الإصدار المعدل"'
        )
        
        # التعديل 2: إضافة متغير فتح PDF تلقائياً
        print("🔧 تطبيق التعديل 2: إضافة متغير فتح PDF...")
        content = content.replace(
            'self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت',
            '''self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت
        
        # إضافة متغير لفتح PDF تلقائياً (التعديل الجديد)
        self.auto_open_pdf_var = tk.BooleanVar(value=True)'''
        )
        
        # التعديل 3: تعديل دالة generate_enhanced_email للهوت سبوت
        print("🔧 تطبيق التعديل 3: تعديل الإيميل للهوت سبوت...")
        # البحث عن السطر الذي يحتوي على email_suffix = ".bot"
        if 'email_suffix = ".bot"' in content:
            content = content.replace(
                'email_suffix = ".bot"',
                '''# تحديد نهاية الإيميل حسب النظام (التعديل الجديد)
            if self.system_type == 'hotspot':
                email_suffix = ".pro"  # للهوت سبوت
            else:
                email_suffix = ".bot"  # للأنظمة الأخرى'''
            )
        
        # التعديل 4: تحسين اسم ملف PDF
        print("🔧 تطبيق التعديل 4: تحسين اسم ملف PDF...")
        # البحث عن دالة get_pdf_filename وتعديلها
        if 'def get_pdf_filename(self):' in content:
            # البحث عن بداية ونهاية الدالة
            start_pos = content.find('def get_pdf_filename(self):')
            if start_pos != -1:
                # البحث عن نهاية الدالة (الدالة التالية أو نهاية الكلاس)
                next_def_pos = content.find('\n    def ', start_pos + 1)
                if next_def_pos == -1:
                    next_def_pos = len(content)
                
                # استخراج الدالة القديمة
                old_function = content[start_pos:next_def_pos]
                
                # إنشاء الدالة الجديدة
                new_function = '''def get_pdf_filename(self):
        """إنشاء اسم ملف PDF محسن مع السعر والتاريخ والوقت وعدد الكروت"""
        now = datetime.now()
        date = now.strftime("%Y-%m-%d")
        time = now.strftime("%H-%M-%S")
        count = len(self.generated_credentials)
        
        # الحصول على السعر
        price = ""
        if hasattr(self, 'price_entry') and self.price_entry.get().strip():
            price = f"_سعر{self.price_entry.get().strip()}"
        elif hasattr(self, 'price_value_entry') and self.price_value_entry.get().strip():
            price = f"_سعر{self.price_value_entry.get().strip()}"
        
        # تحديد نوع النظام
        system_prefix = "usermanager" if self.system_type == 'user_manager' else "hotspot"
        
        # إنشاء اسم الملف المحسن
        default_filename = f"كروت_{system_prefix}_عدد{count}{price}_{date}_{time}.pdf"

        filename = filedialog.asksaveasfilename(
            defaultextension=".pdf", 
            filetypes=[("PDF files", "*.pdf")], 
            initialfile=default_filename
        )
        return filename

'''
                
                # استبدال الدالة القديمة بالجديدة
                content = content.replace(old_function, new_function)
        
        # التعديل 5: إضافة أرقام الأعمدة في PDF
        print("🔧 تطبيق التعديل 5: إضافة أرقام الأعمدة في PDF...")
        # البحث عن المكان المناسب لإضافة أرقام الأعمدة
        if '# رسم الكروت' in content:
            content = content.replace(
                '# رسم الكروت',
                '''# رسم أرقام الأعمدة فوق الكروت (التعديل الجديد)
            for col in range(columns):
                x = margin_x + col * (box_width + spacing)
                y = page_height - margin_y + 5  # فوق الكروت بـ 5mm
                c.setFont("Arial", 8)
                c.setFillColor("black")
                c.drawCentredText(x + box_width/2, y, str(col + 1))
            
            # رسم الكروت'''
            )
        
        # التعديل 6: إضافة زر علامة صح لفتح PDF تلقائياً
        print("🔧 تطبيق التعديل 6: إضافة زر علامة صح لفتح PDF...")
        # البحث عن دالة setup_pdf_tab وإضافة الزر
        if 'def setup_pdf_tab(self):' in content:
            # البحث عن نهاية دالة setup_pdf_tab
            setup_pdf_start = content.find('def setup_pdf_tab(self):')
            if setup_pdf_start != -1:
                # البحث عن الدالة التالية
                next_def = content.find('\n    def ', setup_pdf_start + 1)
                if next_def != -1:
                    # إضافة الكود قبل الدالة التالية
                    checkbox_code = '''
        # إضافة زر علامة صح لفتح PDF تلقائياً (التعديل الجديد)
        auto_open_frame = ttk.Frame(pdf_settings_frame)
        auto_open_frame.pack(fill=tk.X, pady=5)
        
        self.auto_open_checkbox = ttk.Checkbutton(
            auto_open_frame,
            text="فتح PDF تلقائياً بعد الإنشاء",
            variable=self.auto_open_pdf_var,
            style="TCheckbutton"
        )
        self.auto_open_checkbox.pack(anchor="w")

'''
                    content = content[:next_def] + checkbox_code + content[next_def:]
        
        # التعديل 7: تفعيل فتح PDF تلقائياً
        print("🔧 تطبيق التعديل 7: تفعيل فتح PDF تلقائياً...")
        # البحث عن رسالة النجاح في حفظ PDF وإضافة كود فتح الملف
        success_message = 'messagebox.showinfo("نجاح", f"تم حفظ ملف PDF: {filename}")'
        if success_message in content:
            content = content.replace(
                success_message,
                '''messagebox.showinfo("نجاح", f"تم حفظ ملف PDF: {filename}")
                
                # فتح PDF تلقائياً إذا كان الخيار مفعل (التعديل الجديد)
                if hasattr(self, 'auto_open_pdf_var') and self.auto_open_pdf_var.get():
                    try:
                        import subprocess
                        import platform
                        
                        if platform.system() == 'Windows':
                            os.startfile(filename)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.run(['open', filename])
                        else:  # Linux
                            subprocess.run(['xdg-open', filename])
                        
                        self.logger.info(f"تم فتح PDF تلقائياً: {filename}")
                    except Exception as e:
                        self.logger.error(f"خطأ في فتح PDF تلقائياً: {str(e)}")'''
            )
        
        # كتابة الملف المعدل
        print("💾 كتابة الملف المعدل...")
        with open('المعدل.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إنشاء الملف المعدل بنجاح!")
        
        # التحقق من الملف
        import os
        if os.path.exists('المعدل.py'):
            size = os.path.getsize('المعدل.py')
            print(f"📊 حجم الملف المعدل: {size:,} بايت")
            
            # عد الأسطر
            with open('المعدل.py', 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
            print(f"📋 عدد الأسطر: {lines:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء نسخ الملف الأصلي مع التعديلات")
    print("=" * 60)
    
    success = copy_and_modify_original()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم إنشاء الملف المعدل بنجاح!")
        print("\n📋 التعديلات المطبقة:")
        print("   ✅ تغيير العنوان إلى 'الإصدار المعدل'")
        print("   ✅ إضافة متغير فتح PDF تلقائياً")
        print("   ✅ تعديل الإيميل للهوت سبوت (.pro)")
        print("   ✅ تحسين اسم ملف PDF (سعر + تاريخ + وقت + عدد)")
        print("   ✅ إضافة أرقام الأعمدة في PDF")
        print("   ✅ إضافة زر علامة صح لفتح PDF تلقائياً")
        print("   ✅ تفعيل فتح PDF تلقائياً بعد الإنشاء")
        print("   ❌ عدم تعديل أي شيء في User Manager")
        print("\n🎊 الملف 'المعدل.py' جاهز للاستخدام!")
    else:
        print("❌ فشل في إنشاء الملف المعدل")
    
    input("\nاضغط Enter للخروج...")
