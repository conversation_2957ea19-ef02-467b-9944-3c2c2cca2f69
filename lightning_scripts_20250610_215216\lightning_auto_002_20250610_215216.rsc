:put "⚡ بدء تنفيذ سكريبت البرق 2/2";
:put "📊 عدد الكروت في هذا السكريبت: 100";
:put "⏰ وقت البدء: $[/system clock get time]";

:local usr {
    "0171169109"="" ;
    "0147608644"="" ;
    "0148953605"="" ;
    "0112129389"="" ;
    "0101244114"="" ;
    "0138517087"="" ;
    "0181721636"="" ;
    "0192880255"="" ;
    "0184637549"="" ;
    "0154850808"="" ;
    "0146794732"="" ;
    "0126229868"="" ;
    "0197064505"="" ;
    "0156931553"="" ;
    "0127512209"="" ;
    "0191836309"="" ;
    "0158143998"="" ;
    "0152161250"="" ;
    "0102334391"="" ;
    "0154944420"="" ;
    "0171808114"="" ;
    "0173851624"="" ;
    "0142271402"="" ;
    "0100144934"="" ;
    "0155504203"="" ;
    "0193722342"="" ;
    "0169141496"="" ;
    "0188835409"="" ;
    "0175270706"="" ;
    "0108351706"="" ;
    "0177986500"="" ;
    "0129516946"="" ;
    "0131743161"="" ;
    "0162151989"="" ;
    "0174897562"="" ;
    "0178248192"="" ;
    "0151220513"="" ;
    "0184710272"="" ;
    "0100389119"="" ;
    "0130340822"="" ;
    "0107116854"="" ;
    "0114963607"="" ;
    "0119400435"="" ;
    "0195430118"="" ;
    "0170505105"="" ;
    "0169076960"="" ;
    "0138630853"="" ;
    "0113028894"="" ;
    "0157974259"="" ;
    "0145979658"="" ;
    "0180260020"="" ;
    "0183602061"="" ;
    "0177642827"="" ;
    "0146525701"="" ;
    "0145770719"="" ;
    "0191215845"="" ;
    "0180327129"="" ;
    "0176133625"="" ;
    "0125972807"="" ;
    "0128033012"="" ;
    "0147955214"="" ;
    "0123826497"="" ;
    "0168410847"="" ;
    "0149672791"="" ;
    "0126957422"="" ;
    "0157228622"="" ;
    "0185406848"="" ;
    "0145071290"="" ;
    "0159817066"="" ;
    "0134646311"="" ;
    "0174927214"="" ;
    "0172233109"="" ;
    "0183924755"="" ;
    "0174213249"="" ;
    "0127385642"="" ;
    "0156767008"="" ;
    "0102357305"="" ;
    "0169554749"="" ;
    "0186620053"="" ;
    "0127723145"="" ;
    "0131310820"="" ;
    "0104838569"="" ;
    "0171278257"="" ;
    "0108224084"="" ;
    "0170642043"="" ;
    "0139181436"="" ;
    "0112505775"="" ;
    "0129575315"="" ;
    "0143187744"="" ;
    "0117144629"="" ;
    "0152893904"="" ;
    "0107565806"="" ;
    "0178123866"="" ;
    "0127249173"="" ;
    "0143228061"="" ;
    "0138165621"="" ;
    "0173019721"="" ;
    "0123869251"="" ;
    "0180376009"="" ;
    "0171334923"=""
};

:put "🚀 بدء الوضع السريع - إضافة المستخدمين...";
:local count 0;
:local total [:len $usr];
:put "📊 العدد الإجمالي: $total مستخدم";

:foreach u,p in=$usr do={
    :do {
        /tool user-manager user add username=$u password=$p customer="admin" caller-id-bind-on-first-use=yes first-name="card10um";
        :set count ($count + 1);
        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };
    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };
}

:put "✅ تم إضافة $count مستخدم بنجاح";

:put "🔄 بدء تفعيل البروفايلات...";
:do {
    /tool user-manager user create-and-activate-profile customer="admin" profile="card10um" numbers="0171169109,0147608644,0148953605,0112129389,0101244114,0138517087,0181721636,0192880255,0184637549,0154850808,0146794732,0126229868,0197064505,0156931553,0127512209,0191836309,0158143998,0152161250,0102334391,0154944420,0171808114,0173851624,0142271402,0100144934,0155504203,0193722342,0169141496,0188835409,0175270706,0108351706,0177986500,0129516946,0131743161,0162151989,0174897562,0178248192,0151220513,0184710272,0100389119,0130340822,0107116854,0114963607,0119400435,0195430118,0170505105,0169076960,0138630853,0113028894,0157974259,0145979658,0180260020,0183602061,0177642827,0146525701,0145770719,0191215845,0180327129,0176133625,0125972807,0128033012,0147955214,0123826497,0168410847,0149672791,0126957422,0157228622,0185406848,0145071290,0159817066,0134646311,0174927214,0172233109,0183924755,0174213249,0127385642,0156767008,0102357305,0169554749,0186620053,0127723145,0131310820,0104838569,0171278257,0108224084,0170642043,0139181436,0112505775,0129575315,0143187744,0117144629,0152893904,0107565806,0178123866,0127249173,0143228061,0138165621,0173019721,0123869251,0180376009,0171334923";
    :put "✅ تم تفعيل البروفايل للمجموعة 1";
} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };

:put "🎉 تم الانتهاء من الوضع السريع بنجاح!";
:put "📈 تم إضافة وتفعيل 100 مستخدم";

:put "🎉 تم الانتهاء من جميع سكريبتات البرق!";
:put "📈 إجمالي الكروت المضافة: 1600";
:put "⏰ وقت الانتهاء: $[/system clock get time]";
:put "🗑️ حذف السكريبت الأخير: lightning_auto_002_20250610_215216";
/system script remove lightning_auto_002_20250610_215216;