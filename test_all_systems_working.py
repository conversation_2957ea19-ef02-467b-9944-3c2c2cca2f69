#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الأنظمة بعد الإصلاحات
"""

import sys
import os
import json
import traceback
from datetime import datetime

# محاكاة جزء من فئة MikroTikCardGenerator للاختبار
class TestAllSystemsWorking:
    def __init__(self):
        self.generated_credentials = []
        self.system_type = 'user_manager'
        self.caller_id_bind_var = type('MockVar', (), {'get': lambda self: False})()
        
    def generate_test_credentials(self, count=3):
        """توليد بيانات اختبار"""
        self.generated_credentials = []
        
        for i in range(count):
            serial_number = i + 1
            
            cred = {
                "username": f"test_user_{i+1:02d}",
                "password": f"pass{i+1:02d}",
                "profile": "default",
                "comment": f"اختبار",
                "location": str(serial_number),
                "email": "",  # لا إيميل للـ User Manager
                "price": "10"
            }
            
            self.generated_credentials.append(cred)
        
        print(f"✅ تم توليد {len(self.generated_credentials)} بيانات اختبار")
        for i, cred in enumerate(self.generated_credentials):
            print(f"   {i+1}. {cred['username']} - {cred['password']} - إيميل: '{cred['email']}'")
    
    def test_user_manager_fast_script_fixed(self):
        """اختبار سكريبت User Manager السريع المُصلح - بدون إيميل"""
        try:
            customer = "test_customer"
            profile = "default"
            
            # إنشاء سكريبت محسن بـ array - الطريقة الأصلية بدون إيميل
            script_lines = [':local usr {']
            for cred in self.generated_credentials:
                script_lines.append(f'    "{cred["username"]}"="{cred["password"]}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,p in=$usr do={'
            ])

            # إعداد المعاملات الإضافية (بدون إيميل)
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var.get() else ""

            add_user_cmd = f'/tool user-manager user add username=$u password=$p customer="{customer}"{caller_id_param} first-name="{profile}"'

            script_lines.extend([
                '    :do {',
                f'        {add_user_cmd};',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"❌ خطأ في توليد سكريبت User Manager: {str(e)}")
            return None
    
    def test_lightning_normal_batch(self):
        """اختبار البرق العادي - مطابق لبوت التلجرام"""
        try:
            customer = "test_customer"
            profile = "default"
            
            # إنشاء اسم السكريبت
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            script_name = f"lightning_scheduled_{timestamp}"

            # إنشاء السكريبت (مطابق لبوت التلجرام)
            script_lines = [':local usr {']

            # إضافة المستخدمين (مطابق لبوت التلجرام)
            for cred in self.generated_credentials:
                script_lines.append(f'    "{cred["username"]}"="" ;')  # Empty password

            # تنسيق صحيح
            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            # بنية السكريبت (مطابق لبوت التلجرام)
            script_lines.append('};')
            script_lines.append(':foreach u,p in=$usr do={')

            # أوامر User Manager (مطابق لبوت التلجرام)
            add_user_cmd = f'/tool user-manager user add username=$u password=$p customer={customer} first-name="{profile}"'
            script_lines.append(f'    :do {{ {add_user_cmd}; }} on-error={{ :put "no user" }};')

            activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer={customer} profile={profile} numbers=$u'
            script_lines.append(f'    :do {{ {activate_profile_cmd}; }} on-error={{ :put "no profile" }};')

            script_lines.append('}')

            # إضافة أوامر التنظيف (مطابق لبوت التلجرام)
            script_lines.append('')
            script_lines.append('# Cleanup - remove script and scheduler')
            script_lines.append(':delay 3s')
            script_lines.append(f'/system script remove [/system script find where name="{script_name}"]')
            script_lines.append(f'/system scheduler remove [/system scheduler find where name="{script_name}"]')

            return '\n'.join(script_lines)
            
        except Exception as e:
            print(f"❌ خطأ في توليد البرق العادي: {str(e)}")
            return None
    
    def test_lightning_smart_splitting(self, total_cards=5000):
        """اختبار البرق الذكي مع التقسيم - مطابق لبوت التلجرام"""
        try:
            customer = "test_customer"
            profile = "default"
            
            # حساب عدد السكريبتات المطلوبة (مطابق لبوت التلجرام)
            max_cards_per_script = 1000  # الحد الأقصى لكل سكريبت
            num_scripts = (total_cards + max_cards_per_script - 1) // max_cards_per_script

            # إنشاء أسماء السكريبتات مسبق<|im_start|> (مطابق لبوت التلجرام)
            script_names = []
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            for i in range(num_scripts):
                script_name = f"lightning_seq_{i+1}_{timestamp}"
                script_names.append(script_name)

            # إنشاء السكريبت الأول كمثال
            script_num = 0
            script_name = script_names[script_num]

            # إنشاء محتوى السكريبت مع التنفيذ المتسلسل
            script_lines = [':local usr {']

            # إضافة عينة من المستخدمين
            for cred in self.generated_credentials:
                script_lines.append(f'    "{cred["username"]}"="" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.append('};')
            script_lines.append(':foreach u,p in=$usr do={')

            add_user_cmd = f'/tool user-manager user add username=$u password=$p customer={customer} first-name="{profile}"'
            script_lines.append(f'    :do {{ {add_user_cmd}; }} on-error={{ :put "no user" }};')

            activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer={customer} profile={profile} numbers=$u'
            script_lines.append(f'    :do {{ {activate_profile_cmd}; }} on-error={{ :put "no profile" }};')

            script_lines.append('}')
            script_lines.append('')

            # إضافة منطق التنفيذ المتسلسل (مطابق لبوت التلجرام)
            if script_num < num_scripts - 1:
                # ليس السكريبت الأخير - تشغيل السكريبت التالي وحذف الحالي
                next_script_name = script_names[script_num + 1]
                script_lines.append('# Sequential execution - trigger next script')
                script_lines.append(':delay 2s')
                script_lines.append(f'/system script run [/system script find where name="{next_script_name}"]')
                script_lines.append(':delay 1s')
                script_lines.append('# Clean current script and scheduler')
                script_lines.append(f'/system script remove [/system script find where name="{script_name}"]')
                script_lines.append(f'/system scheduler remove [/system scheduler find where name="{script_name}"]')
            else:
                # السكريبت الأخير - حذف نفسه والمجدول فقط
                script_lines.append('# Final cleanup - remove last script and scheduler')
                script_lines.append(':delay 3s')
                script_lines.append(f'/system script remove [/system script find where name="{script_name}"]')
                script_lines.append(f'/system scheduler remove [/system scheduler find where name="{script_name}"]')

            return {
                'script_content': '\n'.join(script_lines),
                'num_scripts': num_scripts,
                'max_cards_per_script': max_cards_per_script,
                'script_names': script_names
            }
            
        except Exception as e:
            print(f"❌ خطأ في توليد البرق الذكي: {str(e)}")
            return None

def test_all_systems():
    """اختبار شامل لجميع الأنظمة"""
    print("🎯 اختبار شامل لجميع الأنظمة بعد الإصلاحات")
    print("=" * 70)
    
    try:
        # إنشاء كائن الاختبار
        test_obj = TestAllSystemsWorking()
        
        # اختبار 1: توليد بيانات بدون إيميل
        print("\n📊 اختبار 1: توليد بيانات بدون إيميل للـ User Manager...")
        test_obj.generate_test_credentials(3)
        
        # التحقق من أن الإيميل فارغ
        all_emails_empty = all(cred["email"] == "" for cred in test_obj.generated_credentials)
        if all_emails_empty:
            print("   ✅ جميع الإيميلات فارغة كما هو مطلوب")
        else:
            print("   ❌ بعض الإيميلات ليست فارغة!")
            return False
        
        # اختبار 2: السريع<|im_start|> المُصلح
        print("\n🚀 اختبار 2: السريع<|im_start|> المُصلح (بدون إيميل)...")
        
        fast_script = test_obj.test_user_manager_fast_script_fixed()
        
        if fast_script:
            print("✅ تم توليد السكريبت السريع بنجاح!")
            
            # فحص أن السكريبت لا يحتوي على إيميل
            lines = fast_script.split('\n')
            has_email = any('email=' in line for line in lines)
            
            if not has_email:
                print("   ✅ السكريبت لا يحتوي على إيميل (صحيح)")
            else:
                print("   ❌ السكريبت يحتوي على إيميل (خطأ)!")
                return False
            
            # فحص البنية الصحيحة
            has_local_usr = any(':local usr {' in line for line in lines)
            has_foreach = any(':foreach u,p in=$usr do={' in line for line in lines)
            has_add_user = any('/tool user-manager user add username=$u password=$p' in line for line in lines)
            has_activate_profile = any('/tool user-manager user create-and-activate-profile' in line for line in lines)
            
            if all([has_local_usr, has_foreach, has_add_user, has_activate_profile]):
                print("   ✅ بنية السكريبت صحيحة")
            else:
                print("   ❌ بنية السكريبت غير صحيحة!")
                return False
        else:
            print("❌ فشل في توليد السكريبت السريع!")
            return False
        
        # اختبار 3: البرق العادي
        print("\n⚡ اختبار 3: البرق العادي (مطابق لبوت التلجرام)...")
        
        lightning_normal = test_obj.test_lightning_normal_batch()
        
        if lightning_normal:
            print("✅ تم توليد البرق العادي بنجاح!")
            
            lines = lightning_normal.split('\n')
            
            # فحص العناصر المطلوبة
            has_empty_password = any('""="" ;' in line for line in lines)
            has_cleanup = any('/system script remove' in line for line in lines)
            has_scheduler_cleanup = any('/system scheduler remove' in line for line in lines)
            
            if has_empty_password:
                print("   ✅ كلمات المرور فارغة (مطابق لبوت التلجرام)")
            else:
                print("   ❌ كلمات المرور ليست فارغة!")
                return False
            
            if has_cleanup and has_scheduler_cleanup:
                print("   ✅ التنظيف التلقائي موجود")
            else:
                print("   ❌ التنظيف التلقائي مفقود!")
                return False
        else:
            print("❌ فشل في توليد البرق العادي!")
            return False
        
        # اختبار 4: البرق الذكي
        print("\n🧠 اختبار 4: البرق الذكي مع التقسيم (مطابق لبوت التلجرام)...")
        
        lightning_smart = test_obj.test_lightning_smart_splitting(5000)
        
        if lightning_smart:
            print("✅ تم توليد البرق الذكي بنجاح!")
            
            print(f"   📊 عدد السكريبتات: {lightning_smart['num_scripts']}")
            print(f"   🎯 كروت لكل سكريبت: {lightning_smart['max_cards_per_script']}")
            print(f"   📝 أسماء السكريبتات: {len(lightning_smart['script_names'])}")
            
            lines = lightning_smart['script_content'].split('\n')
            
            # فحص التسلسل
            has_next_script = any('lightning_seq_2_' in line for line in lines)
            has_sequential_execution = any('Sequential execution' in line for line in lines)
            
            if has_next_script and has_sequential_execution:
                print("   ✅ التسلسل والتنفيذ المتسلسل موجودان")
            else:
                print("   ❌ التسلسل أو التنفيذ المتسلسل مفقود!")
                return False
        else:
            print("❌ فشل في توليد البرق الذكي!")
            return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print("\n📋 النتائج:")
        print("   ✅ لا إيميل في User Manager")
        print("   ✅ السريع<|im_start|> المُصلح يعمل")
        print("   ✅ البرق العادي مطابق لبوت التلجرام")
        print("   ✅ البرق الذكي مع التقسيم يعمل")
        print("   ✅ التنظيف التلقائي موجود")
        print("   ✅ جميع الأوامر صحيحة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء الاختبار الشامل لجميع الأنظمة")
    print("=" * 80)
    
    success = test_all_systems()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: جميع الأنظمة تعمل بشكل مثالي!")
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("💡 الإصلاحات المطبقة:")
        print("   • إزالة الإيميل من User Manager")
        print("   • إصلاح السريع<|im_start|> ليعمل بدون إيميل")
        print("   • البرق العادي مطابق لبوت التلجرام")
        print("   • البرق الذكي مع التقسيم المتسلسل")
        print("   • التنظيف التلقائي للسكريبتات")
        print("\n🎊 البرنامج الآن جاهز للاستخدام بدون مشاكل!")
    else:
        print("🔧 النتيجة النهائية: ما زالت هناك مشاكل تحتاج مراجعة")
    
    input("\nاضغط Enter للخروج...")
