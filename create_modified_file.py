#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء الملف المعدل بالتعديلات المطلوبة
"""

def create_modified_file():
    """إنشاء الملف المعدل"""
    try:
        # قراءة الملف الأصلي
        with open('الاصلي.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التعديل 1: تغيير العنوان
        content = content.replace(
            '"🚀 مولد كروت وسكريبتات MikroTik - الإصدار 2.0"',
            '"🚀 مولد كروت وسكريبتات MikroTik - الإصدار المعدل"'
        )
        
        # التعديل 2: إضافة متغير فتح PDF تلقائياً
        content = content.replace(
            'self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت',
            '''self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت
        
        # إضافة متغير لفتح PDF تلقائياً (التعديل الجديد)
        self.auto_open_pdf_var = tk.BooleanVar(value=True)'''
        )
        
        # كتابة الملف المعدل
        with open('المعدل.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إنشاء الملف المعدل بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    create_modified_file()
