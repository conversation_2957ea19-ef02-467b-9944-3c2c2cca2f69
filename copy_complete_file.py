#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ الملف الأصلي بالكامل إلى المعدل
"""

def copy_complete_file():
    """نسخ الملف الأصلي بالكامل"""
    try:
        print("📖 قراءة الملف الأصلي...")
        
        # قراءة الملف الأصلي
        with open('الاصلي.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 حجم الملف الأصلي: {len(content)} حرف")
        
        # تطبيق التعديلات الأساسية
        print("🔧 تطبيق التعديلات...")
        
        # التعديل 1: تغيير العنوان
        content = content.replace(
            '"🚀 مولد كروت وسكريبتات MikroTik - الإصدار 2.0"',
            '"🚀 مولد كروت وسكريبتات MikroTik - الإصدار المعدل"'
        )
        
        # التعديل 2: إضافة متغير فتح PDF تلقائياً
        content = content.replace(
            'self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت',
            '''self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت
        
        # إضافة متغير لفتح PDF تلقائياً (التعديل الجديد)
        self.auto_open_pdf_var = tk.BooleanVar(value=True)'''
        )
        
        print("✅ تم تطبيق التعديلات الأساسية")
        
        # كتابة الملف المعدل
        print("💾 كتابة الملف المعدل...")
        with open('المعدل.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"📊 حجم الملف المعدل: {len(content)} حرف")
        print("✅ تم إنشاء الملف المعدل بنجاح!")
        
        # التحقق من الملف
        with open('المعدل.py', 'r', encoding='utf-8') as f:
            lines = len(f.readlines())
        
        print(f"📋 عدد الأسطر في الملف المعدل: {lines}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء نسخ الملف الأصلي بالكامل")
    print("=" * 50)
    
    success = copy_complete_file()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 تم نسخ الملف بنجاح!")
        print("📁 الملف المعدل جاهز للتشغيل")
    else:
        print("❌ فشل في نسخ الملف")
    
    input("\nاضغط Enter للخروج...")
