#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار السريع جداً بعد الإصلاح
"""

import sys
import os
import json
import traceback
from datetime import datetime

# محاكاة جزء من فئة MikroTikCardGenerator للاختبار
class TestVeryFastFixed:
    def __init__(self):
        self.generated_credentials = []
        self.system_type = 'user_manager'
        self.caller_id_bind_var = type('MockVar', (), {'get': lambda self: True})()  # تفعيل caller-id
        self.user_email = type('MockEntry', (), {'get': lambda self: '<EMAIL>'})()
        self.customer_entry = type('MockEntry', (), {'get': lambda self: 'admin', 'strip': lambda self: 'admin'})()
        self.profile_combo = type('MockCombo', (), {'get': lambda self: 'card10um', 'strip': lambda self: 'card10um'})()
        self.version_combo = type('MockCombo', (), {'get': lambda self: 'v6'})()
        
    def generate_test_credentials_with_empty_passwords(self):
        """توليد بيانات اختبار مع كلمات مرور فارغة (محاكاة المشكلة)"""
        self.generated_credentials = []
        
        usernames = ["0157138912", "0146798989", "0150466377", "0145357311", "0107029734"]
        
        for username in usernames:
            cred = {
                "username": username,
                "password": "",  # كلمة مرور فارغة (المشكلة)
                "profile": "card10um",
                "comment": "test",
                "location": "1",
                "email": "",
                "price": "10"
            }
            
            self.generated_credentials.append(cred)
        
        print(f"✅ تم توليد {len(self.generated_credentials)} بيانات اختبار مع كلمات مرور فارغة")
        for i, cred in enumerate(self.generated_credentials):
            print(f"   {i+1}. {cred['username']} - كلمة المرور: '{cred['password']}'")
    
    def generate_user_manager_fast_script_fixed(self):
        """توليد سكريبت User Manager مع إصلاح كلمات المرور الفارغة"""
        try:
            version = self.version_combo.get()
            if version != 'v6':
                print("معلومات: الوضع السريع جداً متاح فقط لـ User Manager إصدار 6")
                return None

            customer = self.customer_entry.get().strip()
            profile = self.profile_combo.get().strip()

            if not customer or not profile:
                print("خطأ: يرجى تحديد العميل والبروفايل")
                return None

            # إنشاء سكريبت محسن بـ array مع إصلاح كلمات المرور
            script_lines = [':local usr {']
            for cred in self.generated_credentials:
                # إصلاح كلمات المرور الفارغة
                password = cred["password"] if cred["password"] else cred["username"]
                script_lines.append(f'    "{cred["username"]}"="{password}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,p in=$usr do={'
            ])

            # إعداد المعاملات الإضافية
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var.get() else ""
            email_param = f' email="{self.user_email.get()}"' if self.user_email.get() else ""

            add_user_cmd = f'/tool user-manager user add username=$u password=$p customer="{customer}"{caller_id_param} first-name="{profile}"{email_param}'

            script_lines.extend([
                '    :do {',
                f'        {add_user_cmd};',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"خطأ في توليد سكريبت User Manager: {str(e)}")
            return None
    
    def test_password_fix(self):
        """اختبار إصلاح كلمات المرور الفارغة"""
        try:
            print("🔍 اختبار إصلاح كلمات المرور الفارغة...")
            
            # 1. توليد بيانات مع كلمات مرور فارغة
            print("\n📊 توليد بيانات مع كلمات مرور فارغة...")
            self.generate_test_credentials_with_empty_passwords()
            
            # 2. توليد السكريبت مع الإصلاح
            print("\n🛠️ توليد السكريبت مع إصلاح كلمات المرور...")
            script = self.generate_user_manager_fast_script_fixed()
            
            if not script:
                print("❌ فشل في توليد السكريبت!")
                return False
            
            print("✅ تم توليد السكريبت بنجاح")
            
            # 3. فحص السكريبت
            print("\n🔍 فحص السكريبت...")
            
            lines = script.split('\n')
            
            # البحث عن أسطر المستخدمين
            user_lines = []
            for line in lines:
                if '"01' in line and '="' in line:  # أسطر المستخدمين
                    user_lines.append(line.strip())
            
            print(f"   📏 عدد أسطر المستخدمين: {len(user_lines)}")
            
            # فحص كلمات المرور
            all_passwords_valid = True
            for line in user_lines:
                # استخراج اسم المستخدم وكلمة المرور
                if '="' in line:
                    parts = line.split('="')
                    if len(parts) >= 2:
                        username = parts[0].replace('"', '').strip()
                        password = parts[1].split('"')[0]
                        
                        print(f"   👤 {username} -> كلمة المرور: '{password}'")
                        
                        if not password:
                            print(f"   ❌ كلمة مرور فارغة للمستخدم: {username}")
                            all_passwords_valid = False
                        elif password == username:
                            print(f"   ✅ تم إصلاح كلمة المرور (استخدام اسم المستخدم)")
                        else:
                            print(f"   ✅ كلمة مرور صحيحة")
            
            # فحص أمر إضافة المستخدم
            add_user_line = None
            for line in lines:
                if '/tool user-manager user add username=$u password=$p' in line:
                    add_user_line = line.strip()
                    break
            
            if add_user_line:
                print(f"\n📝 أمر إضافة المستخدم:")
                print(f"   {add_user_line}")
                
                # فحص المعاملات
                has_customer = 'customer=' in add_user_line
                has_caller_id = 'caller-id-bind-on-first-use=yes' in add_user_line
                has_email = 'email=' in add_user_line
                
                print(f"   ✅ العميل: {has_customer}")
                print(f"   ✅ Caller ID: {has_caller_id}")
                print(f"   ✅ الإيميل: {has_email}")
            else:
                print("❌ لم يتم العثور على أمر إضافة المستخدم!")
                return False
            
            # فحص أمر تفعيل البروفايل
            activate_lines = [line for line in lines if 'create-and-activate-profile' in line]
            if activate_lines:
                print(f"\n🔄 أوامر تفعيل البروفايل: {len(activate_lines)}")
                for i, line in enumerate(activate_lines):
                    print(f"   {i+1}. {line.strip()}")
            else:
                print("❌ لم يتم العثور على أوامر تفعيل البروفايل!")
                return False
            
            if all_passwords_valid:
                print("\n🎉 جميع كلمات المرور صحيحة!")
                return True
            else:
                print("\n❌ ما زالت هناك كلمات مرور فارغة!")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False

def test_very_fast_password_fix():
    """اختبار إصلاح كلمات المرور في السريع جداً"""
    print("🎯 اختبار إصلاح كلمات المرور في السريع جداً")
    print("=" * 70)
    
    try:
        # إنشاء كائن الاختبار
        test_obj = TestVeryFastFixed()
        
        # اختبار الإصلاح
        success = test_obj.test_password_fix()
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار إصلاح السريع جداً")
    print("=" * 80)
    
    success = test_very_fast_password_fix()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: تم إصلاح السريع جداً بنجاح!")
        print("\n🎉 الإصلاحات المطبقة:")
        print("   • إصلاح كلمات المرور الفارغة")
        print("   • استخدام اسم المستخدم ككلمة مرور إذا كانت فارغة")
        print("   • الحفاظ على جميع المعاملات الأخرى")
        print("   • أوامر تفعيل البروفايل صحيحة")
        print("\n🎊 السريع جداً الآن جاهز للاستخدام!")
    else:
        print("🔧 النتيجة النهائية: ما زالت هناك مشاكل تحتاج مراجعة")
    
    input("\nاضغط Enter للخروج...")
