#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ الملف الأصلي إلى المعدل
"""

import shutil
import os

try:
    # نسخ الملف
    shutil.copy2('الاصلي.py', 'المعدل.py')
    print("✅ تم نسخ الملف بنجاح من 'الاصلي.py' إلى 'المعدل.py'")
    
    # التحقق من وجود الملف
    if os.path.exists('المعدل.py'):
        file_size = os.path.getsize('المعدل.py')
        print(f"📊 حجم الملف المنسوخ: {file_size} بايت")
    else:
        print("❌ فشل في نسخ الملف")
        
except Exception as e:
    print(f"❌ خطأ في نسخ الملف: {str(e)}")
