#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتأكد من أن إرسال الهوت سبوت يعمل الآن
"""

import sys
import os
import json
import traceback

def final_test_hotspot():
    """اختبار نهائي لإرسال الهوت سبوت"""
    print("🎯 اختبار نهائي - إرسال الهوت سبوت يعمل الآن!")
    print("=" * 70)
    
    try:
        # قراءة إعدادات الاتصال
        config_file = "config/mikrotik_settings.json"
        if not os.path.exists(config_file):
            print("❌ ملف الإعدادات غير موجود")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        # فك ترميز كلمة المرور
        def decrypt_password(encrypted_password):
            try:
                if not encrypted_password:
                    return ""
                import base64
                decoded = base64.b64decode(encrypted_password.encode('utf-8')).decode('utf-8')
                return decoded
            except Exception as e:
                return encrypted_password
        
        decrypted_password = decrypt_password(settings.get('api_password', ''))
        
        print("📋 إعدادات الاتصال:")
        print(f"   🌐 IP: {settings.get('api_ip', 'غير محدد')}")
        print(f"   👤 Username: {settings.get('api_username', 'غير محدد')}")
        
        # التحقق من المكتبة المطلوبة
        try:
            import routeros_api
            print("✅ مكتبة routeros_api متوفرة")
        except ImportError:
            print("❌ مكتبة routeros_api غير متوفرة")
            return False
        
        # محاولة الاتصال
        print("\n🔗 الاتصال بـ MikroTik...")
        
        try:
            api_connection = routeros_api.RouterOsApiPool(
                host=settings['api_ip'],
                username=settings['api_username'],
                password=decrypted_password,
                port=int(settings.get('api_port', 8728)),
                plaintext_login=True
            )
            
            api = api_connection.get_api()
            
            # اختبار الاتصال
            identity = api.get_resource('/system/identity').get()
            router_name = identity[0].get('name', 'غير معروف') if identity else 'غير معروف'
            print(f"✅ نجح الاتصال مع MikroTik: {router_name}")
            
            # اختبار قراءة مستخدمي الهوت سبوت
            print("\n📊 اختبار قراءة مستخدمي الهوت سبوت...")
            try:
                hotspot_users = api.get_resource('/ip/hotspot/user').get()
                print(f"   ✅ تم قراءة {len(hotspot_users)} مستخدم بنجاح - لا توجد مشاكل ترميز!")
                
                if hotspot_users:
                    print("   👥 أول 3 مستخدمين:")
                    for i, user in enumerate(hotspot_users[:3]):
                        name = user.get('name', 'غير معروف')
                        profile = user.get('profile', 'غير محدد')
                        print(f"      {i+1}. {name} - {profile}")
                
            except UnicodeDecodeError as unicode_error:
                print(f"   ❌ ما زالت هناك مشكلة ترميز: {str(unicode_error)}")
                return False
            except Exception as read_error:
                print(f"   ❌ خطأ في قراءة المستخدمين: {str(read_error)}")
                return False
            
            # اختبار إنشاء مستخدمين متعددين (محاكاة البرنامج الحقيقي)
            print("\n🧪 اختبار إنشاء مستخدمين متعددين...")
            
            from datetime import datetime
            timestamp = datetime.now().strftime("%H%M%S")
            
            # دالة تنظيف محسنة (مطابقة للبرنامج)
            def clean_text_for_mikrotik(text):
                if not text:
                    return ""
                try:
                    text_str = str(text)
                    clean_text = text_str.encode('ascii', 'ignore').decode('ascii')
                    clean_text = clean_text.strip()
                    if not clean_text or not clean_text.replace(' ', '').replace('-', '').replace('_', ''):
                        return ""
                    return clean_text
                except Exception:
                    return ""
            
            # إنشاء 3 مستخدمين اختبار
            test_users = []
            for i in range(1, 4):
                test_user = {
                    'name': clean_text_for_mikrotik(f"final_test_{timestamp}_{i:02d}"),
                    'password': clean_text_for_mikrotik(f"pass{i:02d}"),
                    'profile': clean_text_for_mikrotik("default"),
                    'comment': clean_text_for_mikrotik("اختبار نهائي"),  # سيصبح فارغ
                    'email': clean_text_for_mikrotik(f"10@test{i}.pro")
                }
                
                # إزالة الحقول الفارغة
                clean_params = {}
                for key, value in test_user.items():
                    if value:  # إضافة فقط الحقول غير الفارغة
                        clean_params[key] = value
                
                test_users.append(clean_params)
            
            print(f"   📝 سيتم إنشاء {len(test_users)} مستخدم اختبار:")
            for i, user in enumerate(test_users):
                print(f"      {i+1}. {user['name']} - {user['password']} - {user['profile']}")
            
            # إنشاء المستخدمين
            created_users = []
            for i, user_params in enumerate(test_users):
                try:
                    print(f"   🔄 إنشاء المستخدم {i+1}...")
                    result = api.get_resource('/ip/hotspot/user').add(**user_params)
                    created_users.append(user_params['name'])
                    print(f"      ✅ تم إنشاء {user_params['name']} بنجاح!")
                    
                except Exception as create_error:
                    print(f"      ❌ فشل إنشاء {user_params['name']}: {str(create_error)}")
                    return False
            
            print(f"\n🎉 تم إنشاء {len(created_users)} مستخدم بنجاح!")
            
            # اختبار قراءة المستخدمين المُنشأين
            print("\n📖 اختبار قراءة المستخدمين المُنشأين...")
            try:
                updated_users = api.get_resource('/ip/hotspot/user').get()
                print(f"   ✅ تم قراءة {len(updated_users)} مستخدم (بما في ذلك الجدد)")
                
                # البحث عن المستخدمين المُنشأين
                found_users = []
                for user in updated_users:
                    if user.get('name', '').startswith(f"final_test_{timestamp}"):
                        found_users.append(user.get('name', ''))
                
                print(f"   🔍 تم العثور على {len(found_users)} من المستخدمين المُنشأين")
                
            except Exception as read_new_error:
                print(f"   ❌ خطأ في قراءة المستخدمين الجدد: {str(read_new_error)}")
                return False
            
            # حذف المستخدمين الاختبار
            print("\n🗑️ حذف المستخدمين الاختبار...")
            deleted_count = 0
            for username in created_users:
                try:
                    # البحث عن المستخدم وحذفه
                    users_to_delete = api.get_resource('/ip/hotspot/user').get(name=username)
                    if users_to_delete:
                        for user_to_delete in users_to_delete:
                            user_id = user_to_delete.get('.id')
                            if user_id:
                                api.get_resource('/ip/hotspot/user').remove(user_id)
                                deleted_count += 1
                                print(f"      ✅ تم حذف {username}")
                except Exception as delete_error:
                    print(f"      ⚠️ خطأ في حذف {username}: {str(delete_error)}")
            
            print(f"   🗑️ تم حذف {deleted_count} من {len(created_users)} مستخدم")
            
            # إغلاق الاتصال
            api_connection.disconnect()
            print("\n🔌 تم قطع الاتصال بنجاح")
            
            print("\n🎉 الاختبار النهائي نجح بالكامل!")
            print("\n📋 النتائج:")
            print("   ✅ الاتصال بـ MikroTik يعمل")
            print("   ✅ قراءة مستخدمي الهوت سبوت تعمل بدون مشاكل ترميز")
            print("   ✅ إنشاء مستخدمين متعددين يعمل")
            print("   ✅ قراءة المستخدمين الجدد تعمل")
            print("   ✅ حذف المستخدمين يعمل")
            print("   ✅ دالة تنظيف النصوص تعمل بشكل مثالي")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء الاختبار النهائي لإرسال الهوت سبوت")
    print("=" * 80)
    
    success = final_test_hotspot()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: إرسال الهوت سبوت يعمل بشكل مثالي!")
        print("\n🎉 يمكنك الآن استخدام برنامج card بثقة تامة")
        print("💡 جرب إنشاء كروت هوت سبوت من البرنامج الآن")
    else:
        print("🔧 النتيجة النهائية: ما زالت هناك مشاكل تحتاج مراجعة")
    
    input("\nاضغط Enter للخروج...")
