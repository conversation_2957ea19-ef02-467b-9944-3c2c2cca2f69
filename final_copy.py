#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

def copy_file_with_modifications():
    """نسخ الملف الأصلي مع التعديلات المطلوبة"""
    try:
        print("📖 قراءة الملف الأصلي...")
        
        # قراءة الملف الأصلي
        with open('الاصلي.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 تم قراءة {len(content):,} حرف")
        
        # التعديل 1: تغيير العنوان
        print("🔧 تطبيق التعديل 1: تغيير العنوان...")
        content = content.replace(
            'الإصدار 2.0',
            'الإصدار المعدل'
        )
        
        # التعديل 2: إضافة متغير فتح PDF تلقائياً
        print("🔧 تطبيق التعديل 2: إضافة متغير فتح PDF...")
        content = content.replace(
            'self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت',
            '''self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت
        
        # إضافة متغير لفتح PDF تلقائياً (التعديل الجديد)
        self.auto_open_pdf_var = tk.BooleanVar(value=True)'''
        )
        
        # التعديل 3: تعديل الإيميل للهوت سبوت
        print("🔧 تطبيق التعديل 3: تعديل الإيميل للهوت سبوت...")
        content = content.replace(
            'email_suffix = ".bot"',
            '''# تحديد نهاية الإيميل حسب النظام (التعديل الجديد)
            if self.system_type == 'hotspot':
                email_suffix = ".pro"  # للهوت سبوت
            else:
                email_suffix = ".bot"  # للأنظمة الأخرى'''
        )
        
        # التعديل 4: إضافة أرقام الأعمدة في PDF
        print("🔧 تطبيق التعديل 4: إضافة أرقام الأعمدة في PDF...")
        content = content.replace(
            '# رسم الكروت',
            '''# رسم أرقام الأعمدة فوق الكروت (التعديل الجديد)
            for col in range(columns):
                x = margin_x + col * (box_width + spacing)
                y = page_height - margin_y + 5  # فوق الكروت بـ 5mm
                c.setFont("Arial", 8)
                c.setFillColor("black")
                c.drawCentredText(x + box_width/2, y, str(col + 1))
            
            # رسم الكروت'''
        )
        
        # التعديل 5: تحسين اسم ملف PDF
        print("🔧 تطبيق التعديل 5: تحسين اسم ملف PDF...")
        # البحث عن دالة get_pdf_filename وتعديلها
        old_pdf_function = '''def get_pdf_filename(self):
        filename = filedialog.asksaveasfilename(defaultextension=".pdf", filetypes=[("PDF files", "*.pdf")])
        return filename'''
        
        new_pdf_function = '''def get_pdf_filename(self):
        """إنشاء اسم ملف PDF محسن مع السعر والتاريخ والوقت وعدد الكروت"""
        now = datetime.now()
        date = now.strftime("%Y-%m-%d")
        time = now.strftime("%H-%M-%S")
        count = len(self.generated_credentials)
        
        # الحصول على السعر
        price = ""
        if hasattr(self, 'price_entry') and self.price_entry.get().strip():
            price = f"_سعر{self.price_entry.get().strip()}"
        elif hasattr(self, 'price_value_entry') and self.price_value_entry.get().strip():
            price = f"_سعر{self.price_value_entry.get().strip()}"
        
        # تحديد نوع النظام
        system_prefix = "usermanager" if self.system_type == 'user_manager' else "hotspot"
        
        # إنشاء اسم الملف المحسن
        default_filename = f"كروت_{system_prefix}_عدد{count}{price}_{date}_{time}.pdf"

        filename = filedialog.asksaveasfilename(
            defaultextension=".pdf", 
            filetypes=[("PDF files", "*.pdf")], 
            initialfile=default_filename
        )
        return filename'''
        
        if old_pdf_function in content:
            content = content.replace(old_pdf_function, new_pdf_function)
        
        # التعديل 6: إضافة فتح PDF تلقائياً
        print("🔧 تطبيق التعديل 6: إضافة فتح PDF تلقائياً...")
        content = content.replace(
            'messagebox.showinfo("نجاح", f"تم حفظ ملف PDF: {filename}")',
            '''messagebox.showinfo("نجاح", f"تم حفظ ملف PDF: {filename}")
                
                # فتح PDF تلقائياً إذا كان الخيار مفعل (التعديل الجديد)
                if hasattr(self, 'auto_open_pdf_var') and self.auto_open_pdf_var.get():
                    try:
                        import subprocess
                        import platform
                        
                        if platform.system() == 'Windows':
                            os.startfile(filename)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.run(['open', filename])
                        else:  # Linux
                            subprocess.run(['xdg-open', filename])
                        
                        self.logger.info(f"تم فتح PDF تلقائياً: {filename}")
                    except Exception as e:
                        self.logger.error(f"خطأ في فتح PDF تلقائياً: {str(e)}")'''
        )
        
        # كتابة الملف المعدل
        print("💾 كتابة الملف المعدل...")
        with open('المعدل.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إنشاء الملف المعدل بنجاح!")
        
        # التحقق من الملف
        if os.path.exists('المعدل.py'):
            size = os.path.getsize('المعدل.py')
            print(f"📊 حجم الملف المعدل: {size:,} بايت")
            
            with open('المعدل.py', 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
            print(f"📋 عدد الأسطر: {lines:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء نسخ الملف الأصلي مع التعديلات")
    print("=" * 60)
    
    success = copy_file_with_modifications()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم إنشاء الملف المعدل بنجاح!")
        print("\n📋 التعديلات المطبقة:")
        print("   ✅ تغيير العنوان إلى 'الإصدار المعدل'")
        print("   ✅ إضافة متغير فتح PDF تلقائياً")
        print("   ✅ تعديل الإيميل للهوت سبوت (.pro)")
        print("   ✅ تحسين اسم ملف PDF (سعر + تاريخ + وقت + عدد)")
        print("   ✅ إضافة أرقام الأعمدة في PDF")
        print("   ✅ تفعيل فتح PDF تلقائياً بعد الإنشاء")
        print("   ❌ عدم تعديل أي شيء في User Manager")
        print("\n🎊 الملف 'المعدل.py' جاهز للاستخدام!")
    else:
        print("❌ فشل في إنشاء الملف المعدل")
    
    input("\nاضغط Enter للخروج...")
