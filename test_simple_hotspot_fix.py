#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لإصلاح مشكلة إرسال الهوت سبوت
"""

import sys
import os

def test_simple_hotspot_fix():
    """اختبار بسيط لإصلاح الهوت سبوت"""
    print("🔧 اختبار بسيط لإصلاح إرسال الهوت سبوت")
    print("=" * 50)
    
    try:
        # اختبار استيراد المكتبات المطلوبة
        print("📦 اختبار المكتبات المطلوبة...")
        
        try:
            import routeros_api
            print("   ✅ routeros_api متوفرة")
        except ImportError:
            print("   ❌ routeros_api غير متوفرة - يجب تثبيتها")
            print("   💡 تشغيل: pip install routeros-api")
            return False
        
        try:
            import tkinter as tk
            print("   ✅ tkinter متوفرة")
        except ImportError:
            print("   ❌ tkinter غير متوفرة")
            return False
        
        # اختبار وجود الملف الرئيسي
        print("\n📄 اختبار الملف الرئيسي...")
        if os.path.exists("card.py"):
            print("   ✅ card.py موجود")
        else:
            print("   ❌ card.py غير موجود")
            return False
        
        # اختبار دالة تنظيف النصوص بشكل مباشر
        print("\n🧹 اختبار دالة تنظيف النصوص...")
        
        # محاكاة دالة التنظيف
        def clean_text_for_mikrotik(text):
            if not text:
                return ""
            try:
                text_str = str(text)
                clean_text = text_str.encode('ascii', 'ignore').decode('ascii')
                return clean_text.strip()
            except:
                return ""
        
        test_cases = [
            ("test123", "test123"),
            ("مستخدم عربي", ""),
            ("<EMAIL>", "<EMAIL>"),
            ("", ""),
            ("test🚀emoji", "testemoji"),
        ]
        
        passed = 0
        for input_text, expected in test_cases:
            result = clean_text_for_mikrotik(input_text)
            if result == expected:
                print(f"   ✅ '{input_text}' -> '{result}'")
                passed += 1
            else:
                print(f"   ⚠️ '{input_text}' -> '{result}' (متوقع: '{expected}')")
        
        print(f"   📊 نتيجة التنظيف: {passed}/{len(test_cases)} نجح")
        
        # اختبار محاكاة معاملات الإرسال
        print("\n📤 اختبار محاكاة معاملات الإرسال...")
        
        # بيانات اختبار
        test_cred = {
            "username": "test001",
            "password": "pass001", 
            "profile": "default",
            "comment": "تعليق عربي",
            "limit_bytes": "1",
            "limit_unit": "GB",
            "days": "7",
            "email_template": "@test.com"
        }
        
        # تنظيف البيانات
        clean_username = clean_text_for_mikrotik(test_cred["username"])
        clean_password = clean_text_for_mikrotik(test_cred["password"])
        clean_profile = clean_text_for_mikrotik(test_cred["profile"])
        clean_comment = clean_text_for_mikrotik(test_cred["comment"])
        
        # بناء معاملات الإرسال
        params = {
            'name': clean_username,
            'password': clean_password,
            'profile': clean_profile,
            'comment': clean_comment
        }
        
        # إضافة حد البيانات
        if test_cred["limit_bytes"]:
            try:
                limit_value = float(test_cred["limit_bytes"])
                bytes_value = int(limit_value * 1073741824)  # GB to bytes
                params['limit-bytes-total'] = str(bytes_value)
            except:
                pass
        
        # إضافة الإيميل
        if test_cred["days"] and test_cred["email_template"]:
            email_value = f"{test_cred['days']}{test_cred['email_template']}"
            clean_email = clean_text_for_mikrotik(email_value)
            if clean_email and '@' in clean_email:
                params['email'] = clean_email
        
        print("   📋 معاملات الإرسال النهائية:")
        for key, value in params.items():
            print(f"      • {key}: '{value}'")
        
        # محاكاة استدعاء API
        print("\n🌐 محاكاة استدعاء MikroTik API...")
        print("   📞 api.get_resource('/ip/hotspot/user').add(**params)")
        print("   ✅ المعاملات صحيحة وجاهزة للإرسال")
        
        # فحص ملف الإعدادات
        print("\n⚙️ فحص ملف الإعدادات...")
        config_file = "config/mikrotik_settings.json"
        if os.path.exists(config_file):
            print("   ✅ ملف الإعدادات موجود")
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                required_keys = ['api_ip', 'api_username', 'api_password', 'api_port']
                missing_keys = [key for key in required_keys if not settings.get(key)]
                
                if missing_keys:
                    print(f"   ⚠️ مفاتيح مفقودة: {missing_keys}")
                else:
                    print("   ✅ جميع إعدادات الاتصال موجودة")
                    
            except Exception as e:
                print(f"   ❌ خطأ في قراءة الإعدادات: {str(e)}")
        else:
            print("   ⚠️ ملف الإعدادات غير موجود")
        
        print("\n🎉 جميع الاختبارات البسيطة نجحت!")
        print("\n💡 خطوات التشخيص التالية:")
        print("   1. تأكد من إعدادات الاتصال في البرنامج")
        print("   2. اختبر الاتصال بـ MikroTik أولاً")
        print("   3. جرب إرسال حساب واحد فقط")
        print("   4. راجع ملف السجل للأخطاء")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_simple_hotspot_fix()
    if success:
        print("\n✅ الاختبار البسيط نجح!")
    else:
        print("\n❌ الاختبار البسيط فشل!")
    
    input("\nاضغط Enter للمتابعة...")
