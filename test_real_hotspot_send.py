#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حقيقي لإرسال الهوت سبوت إلى MikroTik
"""

import sys
import os
import json
import traceback

def test_real_hotspot_send():
    """اختبار حقيقي لإرسال الهوت سبوت"""
    print("🚀 اختبار حقيقي لإرسال الهوت سبوت إلى MikroTik")
    print("=" * 60)
    
    try:
        # قراءة إعدادات الاتصال
        config_file = "config/mikrotik_settings.json"
        if not os.path.exists(config_file):
            print("❌ ملف الإعدادات غير موجود")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)

        # فك ترميز كلمة المرور
        def decrypt_password(encrypted_password):
            """فك تشفير كلمة المرور"""
            try:
                if not encrypted_password:
                    return ""
                import base64
                decoded = base64.b64decode(encrypted_password.encode('utf-8')).decode('utf-8')
                return decoded
            except Exception as e:
                print(f"خطأ في فك تشفير كلمة المرور: {str(e)}")
                return encrypted_password

        # فك ترميز كلمة المرور
        decrypted_password = decrypt_password(settings.get('api_password', ''))

        print("📋 إعدادات الاتصال:")
        print(f"   🌐 IP: {settings.get('api_ip', 'غير محدد')}")
        print(f"   🔌 Port: {settings.get('api_port', 8728)}")
        print(f"   👤 Username: {settings.get('api_username', 'غير محدد')}")
        print(f"   🔑 Password: {'***' if decrypted_password else 'غير محدد'}")
        print(f"   🔓 Password (decrypted): {decrypted_password}")
        
        # التحقق من المكتبة المطلوبة
        try:
            import routeros_api
            print("✅ مكتبة routeros_api متوفرة")
        except ImportError:
            print("❌ مكتبة routeros_api غير متوفرة")
            return False
        
        # محاولة الاتصال
        print("\n🔗 محاولة الاتصال بـ MikroTik...")
        
        try:
            api_connection = routeros_api.RouterOsApiPool(
                host=settings['api_ip'],
                username=settings['api_username'],
                password=decrypted_password,  # استخدام كلمة المرور المفكوكة الترميز
                port=int(settings.get('api_port', 8728)),
                plaintext_login=True
            )
            
            api = api_connection.get_api()
            
            # اختبار الاتصال
            identity = api.get_resource('/system/identity').get()
            print(f"✅ نجح الاتصال مع MikroTik: {identity[0].get('name', 'غير معروف')}")
            
            # اختبار قراءة مستخدمي الهوت سبوت الحاليين
            print("\n📊 قراءة مستخدمي الهوت سبوت الحاليين...")
            hotspot_users = api.get_resource('/ip/hotspot/user').get()
            print(f"   📈 عدد المستخدمين الحاليين: {len(hotspot_users)}")
            
            if hotspot_users:
                print("   👥 أول 3 مستخدمين:")
                for i, user in enumerate(hotspot_users[:3]):
                    print(f"      {i+1}. {user.get('name', 'غير معروف')} - {user.get('profile', 'غير محدد')}")
            
            # إنشاء مستخدم اختبار
            print("\n🧪 إنشاء مستخدم اختبار...")
            
            from datetime import datetime
            timestamp = datetime.now().strftime("%H%M%S")
            test_username = f"test_hotspot_{timestamp}"
            
            # دالة تنظيف النصوص
            def clean_text_for_mikrotik(text):
                if not text:
                    return ""
                try:
                    text_str = str(text)
                    clean_text = text_str.encode('ascii', 'ignore').decode('ascii')
                    return clean_text.strip()
                except:
                    return ""
            
            # بيانات المستخدم الاختبار
            test_params = {
                'name': clean_text_for_mikrotik(test_username),
                'password': clean_text_for_mikrotik("test123"),
                'profile': clean_text_for_mikrotik("default"),
                'comment': clean_text_for_mikrotik("اختبار إرسال الهوت سبوت"),  # سيصبح فارغ
                'email': clean_text_for_mikrotik("<EMAIL>")
            }
            
            print(f"   📝 بيانات المستخدم الاختبار:")
            for key, value in test_params.items():
                print(f"      • {key}: '{value}'")
            
            # محاولة إضافة المستخدم
            try:
                result = api.get_resource('/ip/hotspot/user').add(**test_params)
                print(f"✅ تم إنشاء المستخدم الاختبار بنجاح!")
                print(f"   🆔 ID: {result}")
                
                # محاولة حذف المستخدم الاختبار
                print("\n🗑️ حذف المستخدم الاختبار...")
                api.get_resource('/ip/hotspot/user').remove(result)
                print("✅ تم حذف المستخدم الاختبار بنجاح!")
                
            except Exception as add_error:
                print(f"❌ فشل في إنشاء المستخدم الاختبار: {str(add_error)}")
                print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
                
                # تحليل نوع الخطأ
                error_str = str(add_error).lower()
                if "invalid" in error_str:
                    print("💡 السبب المحتمل: معاملات غير صحيحة")
                elif "already exists" in error_str:
                    print("💡 السبب المحتمل: المستخدم موجود مسبقاً")
                elif "access denied" in error_str:
                    print("💡 السبب المحتمل: عدم وجود صلاحيات كافية")
                else:
                    print("💡 السبب المحتمل: خطأ غير معروف")
                
                return False
            
            # إغلاق الاتصال
            api_connection.disconnect()
            print("\n🔌 تم قطع الاتصال بنجاح")
            
            print("\n🎉 اختبار إرسال الهوت سبوت نجح بالكامل!")
            print("\n📋 النتائج:")
            print("   ✅ الاتصال بـ MikroTik يعمل")
            print("   ✅ قراءة مستخدمي الهوت سبوت تعمل")
            print("   ✅ إضافة مستخدم جديد تعمل")
            print("   ✅ حذف المستخدم يعمل")
            print("   ✅ دالة تنظيف النصوص تعمل")
            
            return True
            
        except routeros_api.exceptions.RouterOsApiConnectionError as conn_error:
            print(f"❌ خطأ في الاتصال: {str(conn_error)}")
            print("💡 تحقق من:")
            print("   • عنوان IP صحيح")
            print("   • المنفذ صحيح (عادة 8728)")
            print("   • اسم المستخدم وكلمة المرور صحيحة")
            print("   • MikroTik API مفعل")
            return False
            
        except Exception as e:
            print(f"❌ خطأ عام: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("⚠️ هذا اختبار حقيقي سيحاول الاتصال بـ MikroTik")
    response = input("هل تريد المتابعة؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم']:
        success = test_real_hotspot_send()
        if success:
            print("\n🎯 الخلاصة: إرسال الهوت سبوت يعمل بشكل صحيح!")
        else:
            print("\n🔧 الخلاصة: هناك مشكلة تحتاج إلى إصلاح.")
    else:
        print("تم إلغاء الاختبار.")
    
    input("\nاضغط Enter للخروج...")
