#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح سكريبتات User Manager مع الإيميل المحسن
"""

import sys
import os
import json
import traceback
from datetime import datetime

# محاكاة جزء من فئة MikroTikCardGenerator للاختبار
class TestUserManagerScripts:
    def __init__(self):
        self.generated_credentials = []
        self.caller_id_bind_var = False  # محاكاة بسيطة بدلاً من tk.BooleanVar()
        
    def generate_enhanced_email(self, serial_number, mode="normal"):
        """توليد إيميل محسن مع السيريال والتاريخ والوقت"""
        try:
            current_date = datetime.now().strftime("%m%d")
            current_time = datetime.now().strftime("%H%M")
            
            # تحديد نهاية الإيميل حسب النوع
            if mode == "fast":
                email_suffix = ".fast"
            elif mode == "lightning":
                email_suffix = ".lightning"
            else:
                email_suffix = ".sa"
            
            enhanced_email = f"{serial_number}@{current_date}-{current_time}{email_suffix}"
            return enhanced_email
        except Exception as e:
            print(f"خطأ في توليد الإيميل المحسن: {str(e)}")
            return f"{serial_number}@default.sa"
    
    def generate_test_credentials(self, count=5):
        """توليد بيانات اختبار للـ User Manager"""
        self.generated_credentials = []
        
        for i in range(count):
            serial_number = i + 1
            enhanced_email = self.generate_enhanced_email(serial_number, "normal")
            
            cred = {
                "username": f"test_user_{i+1:02d}",
                "password": f"pass{i+1:02d}",
                "profile": "default",
                "comment": "اختبار الإيميل المحسن",
                "location": str(serial_number),
                "email": enhanced_email,
                "price": "10"
            }
            
            self.generated_credentials.append(cred)
        
        print(f"✅ تم توليد {len(self.generated_credentials)} بيانات اختبار")
        for i, cred in enumerate(self.generated_credentials):
            print(f"   {i+1}. {cred['username']} - {cred['email']}")
    
    def generate_user_manager_fast_script_fixed(self):
        """توليد سكريبت User Manager محسن مع الإيميل المحسن"""
        try:
            customer = "test_customer"
            profile = "default"
            
            if not customer or not profile:
                print("❌ خطأ: يرجى تحديد العميل والبروفايل")
                return None
            
            # إنشاء سكريبت محسن بـ array مع الإيميل المحسن (وضع سريع)
            script_lines = [':local usr {']
            for i, cred in enumerate(self.generated_credentials):
                # استخدام الإيميل المحسن الموجود في البيانات
                enhanced_email = cred.get("email", "")
                script_lines.append(f'    "{cred["username"]}"="{cred["password"]},{enhanced_email}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين مع إيميل محسن...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,data in=$usr do={'
            ])

            # إعداد المعاملات الإضافية
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var else ""

            script_lines.extend([
                '    :do {',
                '        # استخراج البيانات من المصفوفة',
                '        :local userdata [:toarray $data];',
                '        :local password [:pick $userdata 0];',
                '        :local enhanced_email [:pick $userdata 1];',
                '',
                f'        /tool user-manager user add username=$u password=$password customer="{customer}"{caller_id_param} first-name="{profile}" email=$enhanced_email;',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"❌ خطأ في توليد سكريبت User Manager: {str(e)}")
            return None
    
    def generate_user_manager_normal_script_fixed(self):
        """توليد سكريبت User Manager عادي مع الإيميل المحسن"""
        try:
            customer = "test_customer"
            version = "v6"
            delay = 0
            
            script = ""
            
            for i, cred in enumerate(self.generated_credentials):
                if version == "v6":
                    script += f'/tool user-manager user add customer="{customer}" username="{cred["username"]}" password="{cred["password"]}" comment="{cred["comment"]}" location="{cred["location"]}" email="{cred["email"]}";\n'
                else:
                    script += f'/user-manager user add name="{cred["username"]}" password="{cred["password"]}" comment="{cred["comment"]}" location="{cred["location"]}" email="{cred["email"]}";\n'
                    script += f'/user-manager user-profile add profile="{cred["profile"]}" user="{cred["username"]}";\n'
                
                if delay > 0 and (i + 1) % 100 == 0 and i < len(self.generated_credentials) - 1:
                    script += f'/delay {delay}ms;\n'
            
            if version == "v6":
                profile = "default"
                numbers = ",".join([cred["username"] for cred in self.generated_credentials])
                script += f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers}";\n'
            
            return script
            
        except Exception as e:
            print(f"❌ خطأ في توليد السكريبت العادي: {str(e)}")
            return None

def test_user_manager_scripts():
    """اختبار سكريبتات User Manager المحسنة"""
    print("🎯 اختبار إصلاح سكريبتات User Manager مع الإيميل المحسن")
    print("=" * 70)
    
    try:
        # إنشاء كائن الاختبار
        test_obj = TestUserManagerScripts()
        
        # توليد بيانات اختبار
        print("\n📊 توليد بيانات اختبار...")
        test_obj.generate_test_credentials(5)
        
        # اختبار السكريبت السريع المحسن
        print("\n🚀 اختبار السكريبت السريع المحسن...")
        fast_script = test_obj.generate_user_manager_fast_script_fixed()
        
        if fast_script:
            print("✅ تم توليد السكريبت السريع بنجاح!")
            print("\n📝 عينة من السكريبت السريع:")
            lines = fast_script.split('\n')
            for i, line in enumerate(lines[:15]):  # أول 15 سطر
                print(f"   {i+1:2d}. {line}")
            if len(lines) > 15:
                print(f"   ... (و {len(lines) - 15} سطر إضافي)")
            
            # فحص أن الإيميل المحسن موجود في السكريبت
            email_found = False
            for line in lines:
                if "@" in line and ".sa" in line:
                    email_found = True
                    print(f"\n✅ تم العثور على الإيميل المحسن في السكريبت: {line.strip()}")
                    break
            
            if not email_found:
                print("\n❌ لم يتم العثور على الإيميل المحسن في السكريبت!")
        else:
            print("❌ فشل في توليد السكريبت السريع!")
            return False
        
        # اختبار السكريبت العادي المحسن
        print("\n📜 اختبار السكريبت العادي المحسن...")
        normal_script = test_obj.generate_user_manager_normal_script_fixed()
        
        if normal_script:
            print("✅ تم توليد السكريبت العادي بنجاح!")
            print("\n📝 عينة من السكريبت العادي:")
            lines = normal_script.split('\n')
            for i, line in enumerate(lines[:10]):  # أول 10 أسطر
                print(f"   {i+1:2d}. {line}")
            if len(lines) > 10:
                print(f"   ... (و {len(lines) - 10} سطر إضافي)")
            
            # فحص أن الإيميل المحسن موجود في السكريبت العادي
            email_found = False
            for line in lines:
                if "email=" in line and "@" in line and ".sa" in line:
                    email_found = True
                    print(f"\n✅ تم العثور على الإيميل المحسن في السكريبت العادي: {line.strip()}")
                    break
            
            if not email_found:
                print("\n❌ لم يتم العثور على الإيميل المحسن في السكريبت العادي!")
        else:
            print("❌ فشل في توليد السكريبت العادي!")
            return False
        
        print("\n🎉 جميع اختبارات سكريبتات User Manager نجحت!")
        print("\n📋 النتائج:")
        print("   ✅ توليد بيانات الاختبار يعمل")
        print("   ✅ توليد الإيميل المحسن يعمل")
        print("   ✅ السكريبت السريع يستخدم الإيميل المحسن")
        print("   ✅ السكريبت العادي يستخدم الإيميل المحسن")
        print("   ✅ تنسيق السكريبتات صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار إصلاح سكريبتات User Manager")
    print("=" * 80)
    
    success = test_user_manager_scripts()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: تم إصلاح سكريبتات User Manager بنجاح!")
        print("\n🎉 السكريبتات الآن تستخدم الإيميل المحسن بشكل صحيح")
        print("💡 يمكن الآن استخدام جميع أنواع السكريبتات مع الإيميل المحسن")
        print("🔧 تم إصلاح:")
        print("   • السكريبت السريع ليستخدم الإيميل المحسن من البيانات")
        print("   • السكريبت العادي يستخدم الإيميل المحسن بالفعل")
        print("   • تنسيق البيانات في المصفوفات محسن")
    else:
        print("🔧 النتيجة النهائية: ما زالت هناك مشاكل تحتاج مراجعة")
    
    input("\nاضغط Enter للخروج...")
