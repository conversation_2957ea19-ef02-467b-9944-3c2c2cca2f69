#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار البرق ليكون مطابق تمام所有情节 للسريع<|im_start|>
"""

import sys
import os
import json
import traceback
from datetime import datetime

# محاكاة جزء من فئة MikroTikCardGenerator للاختبار
class TestLightningLikeVeryFast:
    def __init__(self):
        self.generated_credentials = []
        self.system_type = 'user_manager'
        self.caller_id_bind_var = type('MockVar', (), {'get': lambda self: False})()
        self.customer_entry = type('MockEntry', (), {'get': lambda self: 'test_customer', 'strip': lambda self: 'test_customer'})()
        self.profile_combo = type('MockCombo', (), {'get': lambda self: 'default', 'strip': lambda self: 'default'})()
        self.version_combo = type('MockCombo', (), {'get': lambda self: 'v6'})()
        
    def generate_test_credentials(self, count=3):
        """توليد بيانات اختبار"""
        self.generated_credentials = []
        
        for i in range(count):
            serial_number = i + 1
            
            cred = {
                "username": f"test_user_{i+1:02d}",
                "password": f"pass{i+1:02d}",
                "profile": "default",
                "comment": f"اختبار",
                "location": str(serial_number),
                "email": "",  # لا إيميل للـ User Manager
                "price": "10"
            }
            
            self.generated_credentials.append(cred)
        
        print(f"✅ تم توليد {len(self.generated_credentials)} بيانات اختبار")
        for i, cred in enumerate(self.generated_credentials):
            print(f"   {i+1}. {cred['username']} - {cred['password']} - إيميل: '{cred['email']}'")
    
    def generate_user_manager_fast_script(self):
        """توليد سكريبت سريع محسن لـ User Manager - الطريقة الأصلية بدون إيميل"""
        try:
            version = self.version_combo.get()
            if version != 'v6':
                print("معلومات: الوضع السريع<|im_start|> متاح فقط لـ User Manager إصدار 6")
                return None

            customer = self.customer_entry.get().strip()
            profile = self.profile_combo.get().strip()

            if not customer or not profile:
                print("خطأ: يرجى تحديد العميل والبروفايل")
                return None

            # إنشاء سكريبت محسن بـ array - الطريقة الأصلية
            script_lines = [':local usr {']
            for cred in self.generated_credentials:
                script_lines.append(f'    "{cred["username"]}"="{cred["password"]}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,p in=$usr do={'
            ])

            # إعداد المعاملات الإضافية (بدون إيميل)
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var.get() else ""

            add_user_cmd = f'/tool user-manager user add username=$u password=$p customer="{customer}"{caller_id_param} first-name="{profile}"'

            script_lines.extend([
                '    :do {',
                f'        {add_user_cmd};',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"خطأ في توليد سكريبت User Manager: {str(e)}")
            return None
    
    def create_lightning_script_content(self, credentials, script_num, total_scripts, script_names, delay_seconds):
        """إنشاء محتوى سكريبت البرق مطابق تمام所有情节 للسريع<|im_start|> مع التنفيذ المتسلسل"""
        try:
            # حفظ الكروت الأصلية مؤقت所有情节
            original_credentials = self.generated_credentials
            
            # تعيين كروت هذا السكريبت مؤقت所有情节
            self.generated_credentials = credentials
            
            # إنشاء السكريبت الأساسي مطابق تمام所有情节 للسريع<|im_start|>
            if self.system_type == 'user_manager':
                base_script = self.generate_user_manager_fast_script()
            else:
                raise ValueError("نوع النظام غير مدعوم")
            
            # استعادة الكروت الأصلية
            self.generated_credentials = original_credentials
            
            if not base_script:
                raise ValueError("فشل في توليد السكريبت الأساسي")

            # إضافة منطق التنفيذ المتسلسل والتنظيف (الفرق الوحيد عن السريع<|im_start|>)
            script_lines = base_script.split('\n')
            script_name = script_names[script_num]

            # إضافة التنفيذ المتسلسل
            if script_num < total_scripts - 1:
                # ليس السكريبت الأخير - تشغيل التالي
                next_script_name = script_names[script_num + 1]
                script_lines.extend([
                    '',
                    ':put "⚡ البرق: تشغيل السكريبت التالي...";',
                    f':delay {delay_seconds}s',
                    f'/system script run [/system script find where name="{next_script_name}"]',
                    ':delay 1s',
                    ':put "🧹 تنظيف السكريبت الحالي...";',
                    f'/system script remove [/system script find where name="{script_name}"];',
                    f'/system scheduler remove [/system scheduler find where name="{script_name}"];'
                ])
            else:
                # السكريبت الأخير - تنظيف نفسه فقط
                script_lines.extend([
                    '',
                    ':put "⚡ البرق: انتهاء السلسلة...";',
                    f':delay {delay_seconds}s',
                    ':put "🧹 تنظيف السكريبت الأخير...";',
                    f'/system script remove [/system script find where name="{script_name}"];',
                    f'/system scheduler remove [/system scheduler find where name="{script_name}"];',
                    ':put "🎉 تم الانتهاء من جميع السكريبتات بنجاح!";'
                ])

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"خطأ في إنشاء محتوى سكريبت البرق: {str(e)}")
            raise e
    
    def test_lightning_vs_very_fast(self):
        """اختبار مقارنة البرق مع السريع<|im_start|>"""
        try:
            print("🔍 اختبار مقارنة البرق مع السريع<|im_start|>...")
            
            # 1. توليد السكريبت السريع<|im_start|> الأصلي
            print("\n📊 توليد السكريبت السريع<|im_start|> الأصلي...")
            very_fast_script = self.generate_user_manager_fast_script()
            
            if not very_fast_script:
                print("❌ فشل في توليد السكريبت السريع<|im_start|>!")
                return False
            
            print("✅ تم توليد السكريبت السريع<|im_start|> بنجاح")
            
            # 2. توليد سكريبت البرق (سكريبت واحد)
            print("\n⚡ توليد سكريبت البرق...")
            script_names = ["lightning_test_001"]
            lightning_script = self.create_lightning_script_content(
                self.generated_credentials, 0, 1, script_names, 3
            )
            
            if not lightning_script:
                print("❌ فشل في توليد سكريبت البرق!")
                return False
            
            print("✅ تم توليد سكريبت البرق بنجاح")
            
            # 3. مقارنة المحتوى الأساسي
            print("\n🔍 مقارنة المحتوى...")
            
            very_fast_lines = very_fast_script.split('\n')
            lightning_lines = lightning_script.split('\n')
            
            # العثور على نهاية الجزء الأساسي في البرق (قبل التنظيف)
            lightning_base_end = -1
            for i, line in enumerate(lightning_lines):
                if "⚡ البرق: انتهاء السلسلة" in line:
                    lightning_base_end = i
                    break
            
            if lightning_base_end == -1:
                print("❌ لم يتم العثور على نهاية الجزء الأساسي في البرق!")
                return False
            
            lightning_base_lines = lightning_lines[:lightning_base_end]
            
            # مقارنة الأجزاء الأساسية
            print(f"   📏 السريع<|im_start|>: {len(very_fast_lines)} سطر")
            print(f"   📏 البرق (الجزء الأساسي): {len(lightning_base_lines)} سطر")
            
            # فحص العناصر المهمة
            very_fast_has_local_usr = any(':local usr {' in line for line in very_fast_lines)
            lightning_has_local_usr = any(':local usr {' in line for line in lightning_base_lines)
            
            very_fast_has_foreach = any(':foreach u,p in=$usr do={' in line for line in very_fast_lines)
            lightning_has_foreach = any(':foreach u,p in=$usr do={' in line for line in lightning_base_lines)
            
            very_fast_has_add_user = any('/tool user-manager user add username=$u password=$p' in line for line in very_fast_lines)
            lightning_has_add_user = any('/tool user-manager user add username=$u password=$p' in line for line in lightning_base_lines)
            
            very_fast_has_activate = any('/tool user-manager user create-and-activate-profile' in line for line in very_fast_lines)
            lightning_has_activate = any('/tool user-manager user create-and-activate-profile' in line for line in lightning_base_lines)
            
            # فحص عدم وجود إيميل
            very_fast_has_email = any('email=' in line for line in very_fast_lines)
            lightning_has_email = any('email=' in line for line in lightning_base_lines)
            
            print(f"\n📋 مقارنة العناصر:")
            print(f"   :local usr - السريع<|im_start|>: {very_fast_has_local_usr}, البرق: {lightning_has_local_usr}")
            print(f"   :foreach - السريع<|im_start|>: {very_fast_has_foreach}, البرق: {lightning_has_foreach}")
            print(f"   add user - السريع<|im_start|>: {very_fast_has_add_user}, البرق: {lightning_has_add_user}")
            print(f"   activate - السريع<|im_start|>: {very_fast_has_activate}, البرق: {lightning_has_activate}")
            print(f"   email - السريع<|im_start|>: {very_fast_has_email}, البرق: {lightning_has_email}")
            
            # التحقق من التطابق
            basic_elements_match = (
                very_fast_has_local_usr == lightning_has_local_usr and
                very_fast_has_foreach == lightning_has_foreach and
                very_fast_has_add_user == lightning_has_add_user and
                very_fast_has_activate == lightning_has_activate and
                very_fast_has_email == lightning_has_email
            )
            
            if basic_elements_match:
                print("✅ العناصر الأساسية متطابقة!")
            else:
                print("❌ العناصر الأساسية غير متطابقة!")
                return False
            
            # 4. فحص إضافات البرق
            print("\n⚡ فحص إضافات البرق...")
            
            lightning_has_cleanup = any('/system script remove' in line for line in lightning_lines)
            lightning_has_scheduler_cleanup = any('/system scheduler remove' in line for line in lightning_lines)
            lightning_has_sequential = any('البرق: انتهاء السلسلة' in line for line in lightning_lines)
            
            print(f"   🧹 تنظيف السكريبت: {lightning_has_cleanup}")
            print(f"   🧹 تنظيف الجدولة: {lightning_has_scheduler_cleanup}")
            print(f"   ⚡ التسلسل: {lightning_has_sequential}")
            
            if lightning_has_cleanup and lightning_has_scheduler_cleanup and lightning_has_sequential:
                print("✅ إضافات البرق موجودة!")
            else:
                print("❌ إضافات البرق مفقودة!")
                return False
            
            print("\n🎉 البرق مطابق تمام所有情节 للسريع<|im_start|> مع إضافة التسلسل!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False

def test_lightning_compatibility():
    """اختبار توافق البرق مع السريع<|im_start|>"""
    print("🎯 اختبار توافق البرق مع السريع<|im_start|>")
    print("=" * 70)
    
    try:
        # إنشاء كائن الاختبار
        test_obj = TestLightningLikeVeryFast()
        
        # توليد بيانات اختبار
        print("\n📊 توليد بيانات اختبار...")
        test_obj.generate_test_credentials(5)
        
        # اختبار التوافق
        success = test_obj.test_lightning_vs_very_fast()
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار توافق البرق مع السريع<|im_start|>")
    print("=" * 80)
    
    success = test_lightning_compatibility()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: البرق مطابق تمام所有情节 للسريع<|im_start|>!")
        print("\n🎉 تم الإصلاح بنجاح!")
        print("💡 الآن البرق يستخدم:")
        print("   • نفس طريقة إنشاء الكروت للسريع<|im_start|>")
        print("   • نفس بنية السكريبت")
        print("   • نفس الأوامر")
        print("   • بدون إيميل في User Manager")
        print("   • مع إضافة التسلسل والتنظيف التلقائي")
        print("\n🎊 البرق الآن جاهز للاستخدام!")
    else:
        print("🔧 النتيجة النهائية: ما زالت هناك مشاكل في التوافق")
    
    input("\nاضغط Enter للخروج...")
