#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخ الملف الأصلي بالكامل إلى المعدل
"""

def copy_original_to_modified():
    """نسخ الملف الأصلي إلى المعدل"""
    try:
        # قراءة الملف الأصلي
        with open('الاصلي.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # كتابة المحتوى إلى الملف المعدل
        with open('المعدل.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم نسخ الملف الأصلي إلى المعدل بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نسخ الملف: {str(e)}")
        return False

if __name__ == "__main__":
    copy_original_to_modified()
