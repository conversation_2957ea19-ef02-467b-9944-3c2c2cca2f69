#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار السريع جداً مع كلمات مرور فارغة
"""

import sys
import os
import json
import traceback
from datetime import datetime

# محاكاة جزء من فئة MikroTikCardGenerator للاختبار
class TestNoPassword:
    def __init__(self):
        self.generated_credentials = []
        self.system_type = 'user_manager'
        self.caller_id_bind_var = type('MockVar', (), {'get': lambda self: True})()
        self.user_email = type('MockEntry', (), {'get': lambda self: '<EMAIL>'})()
        self.customer_entry = type('MockEntry', (), {'get': lambda self: 'admin', 'strip': lambda self: 'admin'})()
        self.profile_combo = type('MockCombo', (), {'get': lambda self: 'card10um', 'strip': lambda self: 'card10um'})()
        self.version_combo = type('MockCombo', (), {'get': lambda self: 'v6'})()
        
    def generate_test_credentials_no_password(self):
        """توليد بيانات اختبار بدون كلمات مرور"""
        self.generated_credentials = []
        
        usernames = ["0157138912", "0146798989", "0150466377"]
        
        for username in usernames:
            cred = {
                "username": username,
                "password": "",  # بدون كلمة مرور
                "profile": "card10um",
                "comment": "test",
                "location": "1",
                "email": "",
                "price": "10"
            }
            
            self.generated_credentials.append(cred)
        
        print(f"✅ تم توليد {len(self.generated_credentials)} بيانات اختبار بدون كلمات مرور")
        for i, cred in enumerate(self.generated_credentials):
            print(f"   {i+1}. {cred['username']} - كلمة المرور: '{cred['password']}'")
    
    def generate_user_manager_fast_script_no_password(self):
        """توليد سكريبت User Manager للمستخدمين بدون كلمة مرور"""
        try:
            version = self.version_combo.get()
            if version != 'v6':
                print("معلومات: الوضع السريع جداً متاح فقط لـ User Manager إصدار 6")
                return None

            customer = self.customer_entry.get().strip()
            profile = self.profile_combo.get().strip()

            if not customer or not profile:
                print("خطأ: يرجى تحديد العميل والبروفايل")
                return None

            # إنشاء سكريبت محسن بـ array - للمستخدمين بدون كلمة مرور
            script_lines = [':local usr {']
            for cred in self.generated_credentials:
                # للمستخدمين بدون كلمة مرور - نضع علامة خاصة
                username = cred["username"]
                password = cred["password"] if cred["password"] else "NOPASS"
                script_lines.append(f'    "{username}"="{password}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,p in=$usr do={'
            ])

            # إعداد المعاملات الإضافية
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var.get() else ""
            email_param = f' email="{self.user_email.get()}"' if self.user_email.get() else ""

            # أمر إضافة المستخدم مع التعامل مع كلمات المرور الفارغة
            script_lines.extend([
                '    :local username $u;',
                '    :local password $p;',
                '    :if ($password = "NOPASS") do={',
                f'        /tool user-manager user add username=$username customer="{customer}"{caller_id_param} first-name="{profile}"{email_param};',
                '    } else={',
                f'        /tool user-manager user add username=$username password=$password customer="{customer}"{caller_id_param} first-name="{profile}"{email_param};',
                '    };'
            ])

            script_lines.extend([
                '    :do {',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"خطأ في توليد سكريبت User Manager: {str(e)}")
            return None
    
    def test_no_password_script(self):
        """اختبار السكريبت للمستخدمين بدون كلمة مرور"""
        try:
            print("🔍 اختبار السكريبت للمستخدمين بدون كلمة مرور...")
            
            # 1. توليد بيانات بدون كلمات مرور
            print("\n📊 توليد بيانات بدون كلمات مرور...")
            self.generate_test_credentials_no_password()
            
            # 2. توليد السكريبت
            print("\n🛠️ توليد السكريبت...")
            script = self.generate_user_manager_fast_script_no_password()
            
            if not script:
                print("❌ فشل في توليد السكريبت!")
                return False
            
            print("✅ تم توليد السكريبت بنجاح")
            
            # 3. فحص السكريبت
            print("\n🔍 فحص السكريبت...")
            
            lines = script.split('\n')
            
            # البحث عن أسطر المستخدمين
            user_lines = []
            for line in lines:
                if '"01' in line and '="' in line:  # أسطر المستخدمين
                    user_lines.append(line.strip())
            
            print(f"   📏 عدد أسطر المستخدمين: {len(user_lines)}")
            
            # فحص علامة NOPASS
            nopass_count = 0
            for line in user_lines:
                if 'NOPASS' in line:
                    nopass_count += 1
                    print(f"   🔒 {line}")
            
            print(f"   📊 عدد المستخدمين بدون كلمة مرور: {nopass_count}")
            
            # فحص أوامر إضافة المستخدم
            conditional_add = False
            for line in lines:
                if ':if ($password = "NOPASS") do={' in line:
                    conditional_add = True
                    print("   ✅ تم العثور على الشرط للمستخدمين بدون كلمة مرور")
                    break
            
            if not conditional_add:
                print("   ❌ لم يتم العثور على الشرط للمستخدمين بدون كلمة مرور!")
                return False
            
            # فحص أوامر إضافة المستخدم بدون password
            no_password_cmd = False
            for line in lines:
                if '/tool user-manager user add username=$username customer=' in line and 'password=' not in line:
                    no_password_cmd = True
                    print(f"   ✅ أمر إضافة بدون كلمة مرور: {line.strip()}")
                    break
            
            if not no_password_cmd:
                print("   ❌ لم يتم العثور على أمر إضافة بدون كلمة مرور!")
                return False
            
            # فحص أوامر إضافة المستخدم مع password
            with_password_cmd = False
            for line in lines:
                if '/tool user-manager user add username=$username password=$password customer=' in line:
                    with_password_cmd = True
                    print(f"   ✅ أمر إضافة مع كلمة مرور: {line.strip()}")
                    break
            
            if not with_password_cmd:
                print("   ❌ لم يتم العثور على أمر إضافة مع كلمة مرور!")
                return False
            
            print("\n🎉 السكريبت صحيح ويدعم المستخدمين بدون كلمة مرور!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False

def test_no_password_support():
    """اختبار دعم المستخدمين بدون كلمة مرور"""
    print("🎯 اختبار دعم المستخدمين بدون كلمة مرور")
    print("=" * 70)
    
    try:
        # إنشاء كائن الاختبار
        test_obj = TestNoPassword()
        
        # اختبار السكريبت
        success = test_obj.test_no_password_script()
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار دعم المستخدمين بدون كلمة مرور")
    print("=" * 80)
    
    success = test_no_password_support()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: السكريبت يدعم المستخدمين بدون كلمة مرور!")
        print("\n🎉 الميزات المطبقة:")
        print("   • دعم المستخدمين بدون كلمة مرور")
        print("   • استخدام شرط للتمييز بين المستخدمين")
        print("   • أوامر منفصلة للمستخدمين مع/بدون كلمة مرور")
        print("   • الحفاظ على جميع المعاملات الأخرى")
        print("\n🎊 السريع جداً الآن يدعم كلمات المرور الفارغة!")
    else:
        print("🔧 النتيجة النهائية: ما زالت هناك مشاكل تحتاج مراجعة")
    
    input("\nاضغط Enter للخروج...")
