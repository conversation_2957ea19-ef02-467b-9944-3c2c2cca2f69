#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار زر "سريع جداً" فقط (بدون إرسال)
"""

import sys
import os
import json
import traceback
from datetime import datetime

# محاكاة جزء من فئة MikroTikCardGenerator للاختبار
class TestVeryFastButton:
    def __init__(self):
        self.generated_credentials = []
        self.system_type = 'user_manager'
        self.caller_id_bind_var = type('MockVar', (), {'get': lambda self: True})()
        self.user_email = type('MockEntry', (), {'get': lambda self: ''})()  # بدون إيميل مثل الأصلي
        self.customer_entry = type('MockEntry', (), {'get': lambda self: 'admin', 'strip': lambda self: 'admin'})()
        self.profile_combo = type('MockCombo', (), {'get': lambda self: 'card10um', 'strip': lambda self: 'card10um'})()
        self.version_combo = type('MockCombo', (), {'get': lambda self: 'v6'})()
        
        # محاكاة logger
        self.logger = type('MockLogger', (), {
            'error': lambda self, msg: print(f"ERROR: {msg}"),
            'info': lambda self, msg: print(f"INFO: {msg}")
        })()
        
    def generate_test_credentials_like_your_example(self):
        """توليد بيانات اختبار مطابقة لمثالك"""
        self.generated_credentials = []
        
        usernames = [
            "0178700912", "0179095754", "0151176435", "0170422116", 
            "0107093834", "0104959021", "0184365112", "0141756884",
            "0145579861", "0188002089", "0125511930", "0169541773",
            "0184512877", "0143197879", "0141339736", "0169987835"
        ]
        
        for username in usernames:
            cred = {
                "username": username,
                "password": "",  # بدون كلمة مرور مثل مثالك
                "profile": "card10um",
                "comment": "test",
                "location": "1",
                "email": "",
                "price": "10"
            }
            
            self.generated_credentials.append(cred)
        
        print(f"✅ تم توليد {len(self.generated_credentials)} بيانات اختبار مطابقة لمثالك")
    
    def generate_user_manager_fast_script(self):
        """توليد سكريبت User Manager - نسخة مطابقة من card.py"""
        try:
            version = self.version_combo.get()
            if version != 'v6':
                print("معلومات: الوضع السريع جداً متاح فقط لـ User Manager إصدار 6")
                return None

            customer = self.customer_entry.get().strip()
            profile = self.profile_combo.get().strip()

            if not customer or not profile:
                print("خطأ: يرجى تحديد العميل والبروفايل")
                return None

            # إنشاء سكريبت محسن بـ array
            script_lines = [':local usr {']
            for cred in self.generated_credentials:
                script_lines.append(f'    "{cred["username"]}"="{cred["password"]}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,p in=$usr do={'
            ])

            # إعداد المعاملات الإضافية
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var.get() else ""
            email_param = f' email="{self.user_email.get()}"' if self.user_email.get() else ""

            add_user_cmd = f'/tool user-manager user add username=$u password=$p customer="{customer}"{caller_id_param} first-name="{profile}"{email_param}'

            script_lines.extend([
                '    :do {',
                f'        {add_user_cmd};',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            # النهاية مطابقة للأصلي (بدون أوامر التنظيف)
            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            self.logger.error(f"خطأ في توليد سكريبت User Manager: {str(e)}")
            return None
    
    def test_very_fast_button_output(self):
        """اختبار مخرجات زر "سريع جداً" فقط"""
        try:
            print("🔍 اختبار زر 'سريع جداً' فقط (بدون إرسال)...")
            
            # 1. توليد بيانات مطابقة لمثالك
            print("\n📊 توليد بيانات مطابقة لمثالك...")
            self.generate_test_credentials_like_your_example()
            
            # 2. توليد السكريبت (نفس ما يفعله زر "سريع جداً")
            print("\n🛠️ توليد السكريبت بزر 'سريع جداً'...")
            script = self.generate_user_manager_fast_script()
            
            if not script:
                print("❌ فشل في توليد السكريبت!")
                return False
            
            print("✅ تم توليد السكريبت بنجاح")
            
            # 3. عرض السكريبت المولد
            print("\n📝 السكريبت المولد من زر 'سريع جداً':")
            print("=" * 80)
            print(script)
            print("=" * 80)
            
            # 4. فحص العناصر المهمة
            print("\n🔍 فحص العناصر المهمة...")
            
            lines = script.split('\n')
            
            # فحص الرموز التعبيرية (يجب أن تكون موجودة)
            emoji_checks = {
                '🚀 بدء الوضع السريع': any('🚀 بدء الوضع السريع' in line for line in lines),
                '📊 العدد الإجمالي': any('📊 العدد الإجمالي' in line for line in lines),
                '✅ تم إضافة': any('✅ تم إضافة' in line for line in lines),
                '❌ خطأ في إضافة': any('❌ خطأ في إضافة' in line for line in lines),
                '🔄 بدء تفعيل': any('🔄 بدء تفعيل' in line for line in lines),
                '🎉 تم الانتهاء': any('🎉 تم الانتهاء' in line for line in lines),
                '📈 تم إضافة وتفعيل': any('📈 تم إضافة وتفعيل' in line for line in lines)
            }
            
            # فحص عدم وجود النصوص الإنجليزية (يجب ألا تكون موجودة)
            english_checks = {
                'لا يحتوي على [START]': not any('[START]' in line for line in lines),
                'لا يحتوي على [OK]': not any('[OK]' in line for line in lines),
                'لا يحتوي على [ERROR]': not any('[ERROR]' in line for line in lines),
                'لا يحتوي على [STATS]': not any('[STATS]' in line for line in lines),
                'لا يحتوي على [SUCCESS]': not any('[SUCCESS]' in line for line in lines),
                'لا يحتوي على [REFRESH]': not any('[REFRESH]' in line for line in lines),
                'لا يحتوي على [PROGRESS]': not any('[PROGRESS]' in line for line in lines)
            }
            
            # فحص عدم وجود أوامر التنظيف (يجب ألا تكون موجودة)
            cleanup_checks = {
                'لا يحتوي على أوامر التنظيف': not any('/system script remove' in line for line in lines),
                'لا يحتوي على حذف الجدولة': not any('/system scheduler remove' in line for line in lines)
            }
            
            print("📋 فحص الرموز التعبيرية:")
            emoji_passed = True
            for check_name, result in emoji_checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {result}")
                if not result:
                    emoji_passed = False
            
            print("\n📋 فحص عدم وجود النصوص الإنجليزية:")
            english_passed = True
            for check_name, result in english_checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {result}")
                if not result:
                    english_passed = False
            
            print("\n📋 فحص عدم وجود أوامر التنظيف:")
            cleanup_passed = True
            for check_name, result in cleanup_checks.items():
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {result}")
                if not result:
                    cleanup_passed = False
            
            # فحص بنية المستخدمين
            user_lines = [line for line in lines if '"01' in line and '=""' in line]
            print(f"\n👥 عدد المستخدمين في السكريبت: {len(user_lines)}")
            print(f"👥 عدد المستخدمين المتوقع: {len(self.generated_credentials)}")
            
            users_match = len(user_lines) == len(self.generated_credentials)
            if users_match:
                print("✅ عدد المستخدمين متطابق!")
            else:
                print("❌ عدد المستخدمين غير متطابق!")
            
            # النتيجة النهائية
            all_passed = emoji_passed and english_passed and cleanup_passed and users_match
            
            if all_passed:
                print("\n🎉 زر 'سريع جداً' يعمل بشكل مثالي!")
                print("   ✅ يحتوي على الرموز التعبيرية العربية")
                print("   ✅ لا يحتوي على نصوص إنجليزية")
                print("   ✅ لا يحتوي على أوامر التنظيف")
                print("   ✅ مطابق تماماً للملف الأصلي")
                return True
            else:
                print("\n❌ زر 'سريع جداً' يحتاج إصلاح!")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False

def test_very_fast_button_only():
    """اختبار زر 'سريع جداً' فقط"""
    print("🎯 اختبار زر 'سريع جداً' فقط (بدون إرسال)")
    print("=" * 70)
    
    try:
        # إنشاء كائن الاختبار
        test_obj = TestVeryFastButton()
        
        # اختبار الزر
        success = test_obj.test_very_fast_button_output()
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار زر 'سريع جداً'")
    print("=" * 80)
    
    success = test_very_fast_button_only()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: زر 'سريع جداً' يعمل مثل الأصلي تماماً!")
        print("\n🎉 التأكيدات:")
        print("   • يولد سكريبت مع رموز تعبيرية عربية جميلة")
        print("   • لا يحول النصوص إلى إنجليزية")
        print("   • لا يضيف أوامر تنظيف")
        print("   • مطابق تماماً للملف الأصلي")
        print("\n🎊 زر 'سريع جداً' جاهز للاستخدام!")
    else:
        print("🔧 النتيجة النهائية: زر 'سريع جداً' يحتاج مراجعة")
    
    input("\nاضغط Enter للخروج...")
