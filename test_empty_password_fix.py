#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح كلمات المرور الفارغة في MikroTik User Manager
"""

import sys
import os
import json
import traceback
from datetime import datetime

# محاكاة جزء من فئة MikroTikCardGenerator للاختبار
class TestEmptyPasswordFix:
    def __init__(self):
        self.generated_credentials = []
        self.system_type = 'user_manager'
        self.caller_id_bind_var = type('MockVar', (), {'get': lambda self: True})()
        self.user_email = type('MockEntry', (), {'get': lambda self: ''})()
        self.customer_entry = type('MockEntry', (), {'get': lambda self: 'admin', 'strip': lambda self: 'admin'})()
        self.profile_combo = type('MockCombo', (), {'get': lambda self: 'card10um', 'strip': lambda self: 'card10um'})()
        self.version_combo = type('MockCombo', (), {'get': lambda self: 'v6'})()
        
        # محاكاة logger
        self.logger = type('MockLogger', (), {
            'error': lambda self, msg: print(f"ERROR: {msg}"),
            'info': lambda self, msg: print(f"INFO: {msg}")
        })()
        
    def generate_test_credentials_empty_passwords(self):
        """توليد بيانات اختبار مع كلمات مرور فارغة"""
        self.generated_credentials = []
        
        usernames = [
            "0178700912", "0179095754", "0151176435", "0170422116", 
            "0107093834", "0104959021"
        ]
        
        for username in usernames:
            cred = {
                "username": username,
                "password": "",  # كلمة مرور فارغة (المشكلة الأصلية)
                "profile": "card10um",
                "comment": "test",
                "location": "1",
                "email": "",
                "price": "10"
            }
            
            self.generated_credentials.append(cred)
        
        print(f"✅ تم توليد {len(self.generated_credentials)} بيانات اختبار مع كلمات مرور فارغة")
        for i, cred in enumerate(self.generated_credentials):
            print(f"   {i+1}. {cred['username']} - كلمة المرور: '{cred['password']}'")
    
    def generate_user_manager_fast_script_fixed(self):
        """توليد سكريبت User Manager مع إصلاح كلمات المرور الفارغة"""
        try:
            version = self.version_combo.get()
            if version != 'v6':
                print("معلومات: الوضع السريع جداً متاح فقط لـ User Manager إصدار 6")
                return None

            customer = self.customer_entry.get().strip()
            profile = self.profile_combo.get().strip()

            if not customer or not profile:
                print("خطأ: يرجى تحديد العميل والبروفايل")
                return None

            # إنشاء سكريبت محسن بـ array
            script_lines = [':local usr {']
            for cred in self.generated_credentials:
                script_lines.append(f'    "{cred["username"]}"="{cred["password"]}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,p in=$usr do={'
            ])

            # إعداد المعاملات الإضافية
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var.get() else ""
            email_param = f' email="{self.user_email.get()}"' if self.user_email.get() else ""

            # الإصلاح الجديد: التعامل مع كلمات المرور الفارغة
            script_lines.extend([
                '    :do {',
                '        :if ([:len $p] > 0) do={',
                f'            /tool user-manager user add username=$u password=$p customer="{customer}"{caller_id_param} first-name="{profile}"{email_param};',
                '        } else={',
                f'            /tool user-manager user add username=$u customer="{customer}"{caller_id_param} first-name="{profile}"{email_param};',
                '        };',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            # النهاية مطابقة للأصلي (بدون أوامر التنظيف)
            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            print(f"خطأ في توليد سكريبت User Manager: {str(e)}")
            return None
    
    def test_empty_password_fix(self):
        """اختبار إصلاح كلمات المرور الفارغة"""
        try:
            print("🔍 اختبار إصلاح كلمات المرور الفارغة...")
            
            # 1. توليد بيانات مع كلمات مرور فارغة
            print("\n📊 توليد بيانات مع كلمات مرور فارغة...")
            self.generate_test_credentials_empty_passwords()
            
            # 2. توليد السكريبت مع الإصلاح
            print("\n🛠️ توليد السكريبت مع الإصلاح...")
            script = self.generate_user_manager_fast_script_fixed()
            
            if not script:
                print("❌ فشل في توليد السكريبت!")
                return False
            
            print("✅ تم توليد السكريبت بنجاح")
            
            # 3. عرض السكريبت المولد
            print("\n📝 السكريبت المولد مع الإصلاح:")
            print("=" * 80)
            print(script)
            print("=" * 80)
            
            # 4. فحص الإصلاح
            print("\n🔍 فحص الإصلاح...")
            
            lines = script.split('\n')
            
            # فحص وجود الشرط للكلمات الفارغة
            condition_check = any(':if ([:len $p] > 0) do={' in line for line in lines)
            print(f"   ✅ وجود شرط فحص كلمة المرور: {condition_check}")
            
            # فحص وجود أمر إضافة مع كلمة مرور
            with_password_cmd = any('/tool user-manager user add username=$u password=$p customer=' in line for line in lines)
            print(f"   ✅ وجود أمر إضافة مع كلمة مرور: {with_password_cmd}")
            
            # فحص وجود أمر إضافة بدون كلمة مرور
            without_password_cmd = any('/tool user-manager user add username=$u customer=' in line and 'password=' not in line for line in lines)
            print(f"   ✅ وجود أمر إضافة بدون كلمة مرور: {without_password_cmd}")
            
            # فحص بنية المستخدمين
            user_lines = [line for line in lines if '"01' in line and '=""' in line]
            print(f"   📊 عدد المستخدمين بكلمات مرور فارغة: {len(user_lines)}")
            
            # فحص الرموز التعبيرية
            emoji_check = any('🚀 بدء الوضع السريع' in line for line in lines)
            print(f"   ✅ وجود الرموز التعبيرية: {emoji_check}")
            
            # النتيجة النهائية
            all_checks_passed = (
                condition_check and 
                with_password_cmd and 
                without_password_cmd and 
                emoji_check and
                len(user_lines) == len(self.generated_credentials)
            )
            
            if all_checks_passed:
                print("\n🎉 الإصلاح يعمل بشكل مثالي!")
                print("   ✅ يفحص طول كلمة المرور قبل الإضافة")
                print("   ✅ يضيف المستخدمين مع كلمة مرور إذا كانت موجودة")
                print("   ✅ يضيف المستخدمين بدون كلمة مرور إذا كانت فارغة")
                print("   ✅ يحتوي على الرموز التعبيرية العربية")
                print("   ✅ يدعم جميع المعاملات الأخرى")
                return True
            else:
                print("\n❌ الإصلاح يحتاج مراجعة!")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False

def test_empty_password_solution():
    """اختبار حل مشكلة كلمات المرور الفارغة"""
    print("🎯 اختبار حل مشكلة كلمات المرور الفارغة")
    print("=" * 70)
    
    try:
        # إنشاء كائن الاختبار
        test_obj = TestEmptyPasswordFix()
        
        # اختبار الإصلاح
        success = test_obj.test_empty_password_fix()
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار حل مشكلة كلمات المرور الفارغة")
    print("=" * 80)
    
    success = test_empty_password_solution()
    
    print("\n" + "=" * 80)
    if success:
        print("🎯 النتيجة النهائية: تم حل مشكلة كلمات المرور الفارغة!")
        print("\n🎉 الحل المطبق:")
        print("   • فحص طول كلمة المرور قبل الإضافة")
        print("   • استخدام أمر مختلف للمستخدمين بدون كلمة مرور")
        print("   • الحفاظ على جميع الميزات الأخرى")
        print("   • متوافق مع MikroTik User Manager")
        print("\n🎊 السريع جداً الآن يعمل مع كلمات المرور الفارغة!")
    else:
        print("🔧 النتيجة النهائية: الحل يحتاج مراجعة إضافية")
    
    input("\nاضغط Enter للخروج...")
