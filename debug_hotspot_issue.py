#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مفصل لمشكلة إرسال الهوت سبوت
"""

import sys
import os
import json
import traceback

def debug_hotspot_issue():
    """تشخيص مفصل لمشكلة الهوت سبوت"""
    print("🔍 تشخيص مفصل لمشكلة إرسال الهوت سبوت")
    print("=" * 70)
    
    try:
        # قراءة إعدادات الاتصال
        config_file = "config/mikrotik_settings.json"
        if not os.path.exists(config_file):
            print("❌ ملف الإعدادات غير موجود")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        # فك ترميز كلمة المرور
        def decrypt_password(encrypted_password):
            try:
                if not encrypted_password:
                    return ""
                import base64
                decoded = base64.b64decode(encrypted_password.encode('utf-8')).decode('utf-8')
                return decoded
            except Exception as e:
                return encrypted_password
        
        decrypted_password = decrypt_password(settings.get('api_password', ''))
        
        print("📋 إعدادات الاتصال:")
        print(f"   🌐 IP: {settings.get('api_ip', 'غير محدد')}")
        print(f"   👤 Username: {settings.get('api_username', 'غير محدد')}")
        print(f"   🔑 Password: {'***' if decrypted_password else 'غير محدد'}")
        
        # التحقق من المكتبة المطلوبة
        try:
            import routeros_api
            print("✅ مكتبة routeros_api متوفرة")
        except ImportError:
            print("❌ مكتبة routeros_api غير متوفرة")
            return False
        
        # محاولة الاتصال
        print("\n🔗 محاولة الاتصال بـ MikroTik...")
        
        try:
            api_connection = routeros_api.RouterOsApiPool(
                host=settings['api_ip'],
                username=settings['api_username'],
                password=decrypted_password,
                port=int(settings.get('api_port', 8728)),
                plaintext_login=True
            )
            
            api = api_connection.get_api()
            
            # اختبار الاتصال
            identity = api.get_resource('/system/identity').get()
            router_name = identity[0].get('name', 'غير معروف') if identity else 'غير معروف'
            print(f"✅ نجح الاتصال مع MikroTik: {router_name}")
            
            # اختبار قراءة مستخدمي الهوت سبوت مع معالجة مفصلة للأخطاء
            print("\n📊 اختبار قراءة مستخدمي الهوت سبوت...")
            try:
                hotspot_users = api.get_resource('/ip/hotspot/user').get()
                print(f"   ✅ تم قراءة {len(hotspot_users)} مستخدم بنجاح")
                
                # فحص أول 5 مستخدمين بتفصيل أكثر
                if hotspot_users:
                    print("   👥 تفاصيل أول 5 مستخدمين:")
                    for i, user in enumerate(hotspot_users[:5]):
                        try:
                            name = user.get('name', 'غير معروف')
                            profile = user.get('profile', 'غير محدد')
                            comment = user.get('comment', '')
                            email = user.get('email', '')
                            print(f"      {i+1}. Name: {name}, Profile: {profile}")
                            if comment:
                                print(f"         Comment: {comment}")
                            if email:
                                print(f"         Email: {email}")
                        except UnicodeDecodeError as ude:
                            print(f"      {i+1}. [مستخدم بترميز غير متوافق] - خطأ: {str(ude)}")
                        except Exception as e:
                            print(f"      {i+1}. [خطأ في قراءة المستخدم] - خطأ: {str(e)}")
                
            except UnicodeDecodeError as unicode_error:
                print(f"   ❌ مشكلة ترميز في قراءة المستخدمين: {str(unicode_error)}")
                print("   💡 هذا يؤكد وجود بيانات قديمة بترميز غير متوافق")
                print("   🔧 الحل: تنظيف قاعدة البيانات أولاً")
                
                # محاولة قراءة المستخدمين واحد تلو الآخر
                print("\n🔍 محاولة قراءة المستخدمين واحد تلو الآخر...")
                try:
                    user_ids = api.get_resource('/ip/hotspot/user').call('print', {'proplist': '.id'})
                    print(f"   📊 عدد المستخدمين: {len(user_ids)}")
                    
                    problematic_users = []
                    for i, user_id in enumerate(user_ids[:10]):  # فحص أول 10 فقط
                        try:
                            user_detail = api.get_resource('/ip/hotspot/user').get(id=user_id['.id'])
                            print(f"   ✅ المستخدم {i+1}: OK")
                        except UnicodeDecodeError:
                            problematic_users.append(user_id['.id'])
                            print(f"   ❌ المستخدم {i+1}: مشكلة ترميز")
                        except Exception as e:
                            print(f"   ⚠️ المستخدم {i+1}: خطأ - {str(e)}")
                    
                    if problematic_users:
                        print(f"\n🚨 تم العثور على {len(problematic_users)} مستخدم بمشاكل ترميز")
                        print("   💡 هؤلاء المستخدمون يحتاجون حذف أو إصلاح")
                        
                except Exception as detailed_error:
                    print(f"   ❌ خطأ في القراءة التفصيلية: {str(detailed_error)}")
                
            except Exception as read_error:
                print(f"   ❌ خطأ عام في قراءة المستخدمين: {str(read_error)}")
            
            # اختبار إنشاء مستخدم جديد مع تشخيص مفصل
            print("\n🧪 اختبار إنشاء مستخدم جديد...")
            
            from datetime import datetime
            timestamp = datetime.now().strftime("%H%M%S")
            test_username = f"debug_test_{timestamp}"
            
            # دالة تنظيف محسنة
            def clean_text_for_mikrotik(text):
                if not text:
                    return ""
                try:
                    text_str = str(text)
                    clean_text = text_str.encode('ascii', 'ignore').decode('ascii')
                    clean_text = clean_text.strip()
                    if not clean_text or not clean_text.replace(' ', '').replace('-', '').replace('_', ''):
                        return ""
                    return clean_text
                except Exception:
                    return ""
            
            # بيانات المستخدم الاختبار
            test_params = {
                'name': clean_text_for_mikrotik(test_username),
                'password': clean_text_for_mikrotik("debug123"),
                'profile': clean_text_for_mikrotik("default")
            }
            
            print(f"   📝 بيانات المستخدم الاختبار:")
            for key, value in test_params.items():
                print(f"      • {key}: '{value}'")
            
            # محاولة إضافة المستخدم مع تشخيص مفصل
            try:
                print("   🔄 محاولة إضافة المستخدم...")
                result = api.get_resource('/ip/hotspot/user').add(**test_params)
                print(f"   ✅ تم إنشاء المستخدم الاختبار بنجاح!")
                print(f"   🆔 ID: {result}")
                
                # محاولة قراءة المستخدم المُنشأ
                try:
                    created_user = api.get_resource('/ip/hotspot/user').get(id=result)
                    print("   ✅ تم قراءة المستخدم المُنشأ بنجاح")
                    print(f"      Name: {created_user[0].get('name', 'N/A')}")
                    print(f"      Profile: {created_user[0].get('profile', 'N/A')}")
                except Exception as read_new_error:
                    print(f"   ⚠️ خطأ في قراءة المستخدم المُنشأ: {str(read_new_error)}")
                
                # محاولة حذف المستخدم الاختبار
                try:
                    print("   🗑️ محاولة حذف المستخدم الاختبار...")
                    api.get_resource('/ip/hotspot/user').remove(result)
                    print("   ✅ تم حذف المستخدم الاختبار بنجاح!")
                except Exception as delete_error:
                    print(f"   ⚠️ خطأ في حذف المستخدم: {str(delete_error)}")
                    # محاولة حذف بطريقة أخرى
                    try:
                        api.get_resource('/ip/hotspot/user').call('remove', {'numbers': result})
                        print("   ✅ تم حذف المستخدم بالطريقة البديلة")
                    except Exception as alt_delete_error:
                        print(f"   ❌ فشل الحذف بالطريقة البديلة: {str(alt_delete_error)}")
                
            except Exception as add_error:
                print(f"   ❌ فشل في إنشاء المستخدم الاختبار: {str(add_error)}")
                print(f"   📍 نوع الخطأ: {type(add_error).__name__}")
                print(f"   📍 تفاصيل الخطأ: {traceback.format_exc()}")
                
                # تحليل نوع الخطأ
                error_str = str(add_error).lower()
                if "unicode" in error_str or "decode" in error_str:
                    print("   💡 السبب: مشكلة ترميز")
                elif "invalid" in error_str:
                    print("   💡 السبب: معاملات غير صحيحة")
                elif "already exists" in error_str:
                    print("   💡 السبب: المستخدم موجود مسبقاً")
                elif "permission" in error_str or "access" in error_str:
                    print("   💡 السبب: مشكلة في الصلاحيات")
                else:
                    print("   💡 السبب: خطأ غير معروف")
                
                return False
            
            # إغلاق الاتصال
            api_connection.disconnect()
            print("\n🔌 تم قطع الاتصال بنجاح")
            
            print("\n🎉 التشخيص المفصل اكتمل!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في التشخيص: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء التشخيص المفصل لمشكلة الهوت سبوت")
    print("=" * 80)
    
    response = input("هل تريد تشغيل التشخيص المفصل؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم']:
        success = debug_hotspot_issue()
        
        print("\n" + "=" * 80)
        if success:
            print("🎯 نتيجة التشخيص: تم تحديد المشكلة بدقة")
            print("\n💡 الخطوات التالية:")
            print("   1. إذا كانت المشكلة في البيانات القديمة - نظف قاعدة البيانات")
            print("   2. إذا كانت المشكلة في الصلاحيات - تحقق من صلاحيات المستخدم")
            print("   3. إذا كانت المشكلة في المعاملات - راجع البيانات المُرسلة")
            print("   4. جرب إرسال مستخدم واحد فقط للاختبار")
        else:
            print("🔧 نتيجة التشخيص: تم تحديد المشكلة - راجع التفاصيل أعلاه")
    else:
        print("تم إلغاء التشخيص.")
    
    input("\nاضغط Enter للخروج...")
