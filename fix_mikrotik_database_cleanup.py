#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لقاعدة بيانات MikroTik - تنظيف البيانات المعطوبة
"""

import sys
import os
import json
import traceback

def fix_mikrotik_database():
    """إصلاح قاعدة بيانات MikroTik"""
    print("🛠️ إصلاح شامل لقاعدة بيانات MikroTik")
    print("=" * 70)
    
    try:
        # قراءة إعدادات الاتصال
        config_file = "config/mikrotik_settings.json"
        if not os.path.exists(config_file):
            print("❌ ملف الإعدادات غير موجود")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        # فك ترميز كلمة المرور
        def decrypt_password(encrypted_password):
            try:
                if not encrypted_password:
                    return ""
                import base64
                decoded = base64.b64decode(encrypted_password.encode('utf-8')).decode('utf-8')
                return decoded
            except Exception as e:
                return encrypted_password
        
        decrypted_password = decrypt_password(settings.get('api_password', ''))
        
        print("📋 إعدادات الاتصال:")
        print(f"   🌐 IP: {settings.get('api_ip', 'غير محدد')}")
        print(f"   👤 Username: {settings.get('api_username', 'غير محدد')}")
        
        # التحقق من المكتبة المطلوبة
        try:
            import routeros_api
            print("✅ مكتبة routeros_api متوفرة")
        except ImportError:
            print("❌ مكتبة routeros_api غير متوفرة")
            return False
        
        # محاولة الاتصال
        print("\n🔗 الاتصال بـ MikroTik...")
        
        try:
            api_connection = routeros_api.RouterOsApiPool(
                host=settings['api_ip'],
                username=settings['api_username'],
                password=decrypted_password,
                port=int(settings.get('api_port', 8728)),
                plaintext_login=True
            )
            
            api = api_connection.get_api()
            
            # اختبار الاتصال
            identity = api.get_resource('/system/identity').get()
            router_name = identity[0].get('name', 'غير معروف') if identity else 'غير معروف'
            print(f"✅ نجح الاتصال مع MikroTik: {router_name}")
            
            # الحصول على قائمة المستخدمين بطريقة آمنة
            print("\n🔍 فحص مستخدمي الهوت سبوت...")
            
            try:
                # محاولة الحصول على IDs فقط أولاً
                user_ids_response = api.get_resource('/ip/hotspot/user').call('print', {'proplist': '.id'})
                print(f"   📊 عدد المستخدمين: {len(user_ids_response)}")
                
                if len(user_ids_response) == 0:
                    print("   ✅ لا توجد مستخدمين - قاعدة البيانات نظيفة")
                    api_connection.disconnect()
                    return True
                
                # فحص كل مستخدم للعثور على المعطوبين
                problematic_users = []
                good_users = []
                
                print("   🔍 فحص كل مستخدم...")
                for i, user_id_info in enumerate(user_ids_response):
                    user_id = user_id_info.get('.id', '')
                    if not user_id:
                        continue
                    
                    try:
                        # محاولة قراءة بيانات المستخدم
                        user_detail = api.get_resource('/ip/hotspot/user').call('print', {
                            'numbers': user_id,
                            'proplist': 'name,profile,comment'
                        })
                        
                        if user_detail:
                            user_info = user_detail[0]
                            name = user_info.get('name', f'user_{i+1}')
                            print(f"      ✅ المستخدم {i+1}: {name}")
                            good_users.append(user_id)
                        else:
                            print(f"      ⚠️ المستخدم {i+1}: بيانات فارغة")
                            problematic_users.append(user_id)
                            
                    except UnicodeDecodeError as ude:
                        print(f"      ❌ المستخدم {i+1}: مشكلة ترميز - {str(ude)}")
                        problematic_users.append(user_id)
                    except Exception as e:
                        print(f"      ⚠️ المستخدم {i+1}: خطأ - {str(e)}")
                        problematic_users.append(user_id)
                
                print(f"\n📊 نتائج الفحص:")
                print(f"   ✅ مستخدمين سليمين: {len(good_users)}")
                print(f"   ❌ مستخدمين معطوبين: {len(problematic_users)}")
                
                # حذف المستخدمين المعطوبين
                if problematic_users:
                    print(f"\n🗑️ حذف {len(problematic_users)} مستخدم معطوب...")
                    
                    response = input(f"هل تريد حذف {len(problematic_users)} مستخدم معطوب؟ (y/n): ")
                    if response.lower() in ['y', 'yes', 'نعم']:
                        deleted_count = 0
                        for user_id in problematic_users:
                            try:
                                api.get_resource('/ip/hotspot/user').call('remove', {'numbers': user_id})
                                deleted_count += 1
                                print(f"      ✅ تم حذف المستخدم {user_id}")
                            except Exception as delete_error:
                                print(f"      ❌ فشل حذف المستخدم {user_id}: {str(delete_error)}")
                        
                        print(f"\n🎉 تم حذف {deleted_count} من {len(problematic_users)} مستخدم معطوب")
                    else:
                        print("   ⏭️ تم تخطي عملية الحذف")
                
                # اختبار إنشاء مستخدم جديد بعد التنظيف
                print("\n🧪 اختبار إنشاء مستخدم جديد بعد التنظيف...")
                
                from datetime import datetime
                timestamp = datetime.now().strftime("%H%M%S")
                test_username = f"test_clean_{timestamp}"
                
                test_params = {
                    'name': test_username,
                    'password': "test123",
                    'profile': "default",
                    'comment': "اختبار بعد التنظيف"
                }
                
                try:
                    result = api.get_resource('/ip/hotspot/user').add(**test_params)
                    print(f"   ✅ تم إنشاء المستخدم الاختبار بنجاح!")
                    
                    # محاولة قراءة المستخدم المُنشأ
                    try:
                        created_users = api.get_resource('/ip/hotspot/user').call('print', {
                            'proplist': 'name,profile,comment',
                            '?name': test_username
                        })
                        if created_users:
                            print("   ✅ تم قراءة المستخدم المُنشأ بنجاح")
                            user_data = created_users[0]
                            print(f"      Name: {user_data.get('name', 'N/A')}")
                            print(f"      Profile: {user_data.get('profile', 'N/A')}")
                            print(f"      Comment: {user_data.get('comment', 'N/A')}")
                        else:
                            print("   ⚠️ لم يتم العثور على المستخدم المُنشأ")
                    except Exception as read_error:
                        print(f"   ⚠️ خطأ في قراءة المستخدم المُنشأ: {str(read_error)}")
                    
                    # حذف المستخدم الاختبار
                    try:
                        api.get_resource('/ip/hotspot/user').call('remove', {'?name': test_username})
                        print("   ✅ تم حذف المستخدم الاختبار بنجاح")
                    except Exception as delete_test_error:
                        print(f"   ⚠️ خطأ في حذف المستخدم الاختبار: {str(delete_test_error)}")
                    
                except Exception as test_error:
                    print(f"   ❌ فشل في إنشاء المستخدم الاختبار: {str(test_error)}")
                
            except Exception as list_error:
                print(f"   ❌ خطأ في فحص المستخدمين: {str(list_error)}")
                print(f"   📍 تفاصيل: {traceback.format_exc()}")
            
            # إغلاق الاتصال
            api_connection.disconnect()
            print("\n🔌 تم قطع الاتصال بنجاح")
            
            print("\n🎉 إصلاح قاعدة البيانات اكتمل!")
            print("\n💡 التوصيات:")
            print("   1. قاعدة البيانات الآن نظيفة من البيانات المعطوبة")
            print("   2. يمكن الآن إرسال كروت الهوت سبوت بدون مشاكل")
            print("   3. جرب إنشاء مستخدمين جدد من البرنامج")
            print("   4. تجنب استخدام رموز غير ASCII في المستقبل")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح قاعدة بيانات MikroTik")
    print("=" * 80)
    print("⚠️ تحذير: هذا البرنامج سيحذف المستخدمين المعطوبين من MikroTik")
    print("💾 تأكد من عمل نسخة احتياطية قبل المتابعة")
    print()
    
    response = input("هل تريد المتابعة مع إصلاح قاعدة البيانات؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم']:
        success = fix_mikrotik_database()
        
        print("\n" + "=" * 80)
        if success:
            print("🎯 نتيجة الإصلاح: تم إصلاح قاعدة البيانات بنجاح!")
            print("\n🎉 يمكنك الآن استخدام برنامج card لإرسال الهوت سبوت بدون مشاكل")
        else:
            print("🔧 نتيجة الإصلاح: هناك مشاكل تحتاج مراجعة إضافية")
    else:
        print("تم إلغاء عملية الإصلاح.")
    
    input("\nاضغط Enter للخروج...")
