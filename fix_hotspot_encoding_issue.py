#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لمشكلة ترميز الهوت سبوت في MikroTik
"""

import sys
import os

def fix_hotspot_encoding_issue():
    """إصلاح مشكلة ترميز الهوت سبوت"""
    print("🔧 إصلاح شامل لمشكلة ترميز الهوت سبوت")
    print("=" * 60)
    
    # تحديد المشكلة
    print("🔍 تحليل المشكلة:")
    print("   ✅ الاتصال بـ MikroTik يعمل بشكل صحيح")
    print("   ❌ مشكلة في ترميز البيانات عند قراءة/كتابة مستخدمي الهوت سبوت")
    print("   📍 الخطأ: 'utf-8' codec can't decode byte 0xc7")
    print("   💡 السبب: وجود بيانات بترميز غير متوافق في قاعدة بيانات MikroTik")
    
    print("\n🛠️ الحلول المطلوبة:")
    
    # الحل الأول: تحسين دالة تنظيف النصوص
    print("\n1️⃣ تحسين دالة تنظيف النصوص:")
    print("   • إزالة جميع الرموز غير ASCII")
    print("   • معالجة أفضل للنصوص العربية")
    print("   • تجنب إرسال نصوص فارغة")
    
    # الحل الثاني: معالجة أخطاء الترميز
    print("\n2️⃣ معالجة أخطاء الترميز:")
    print("   • إضافة try-catch للعمليات الحساسة")
    print("   • استخدام encoding='utf-8', errors='ignore'")
    print("   • تنظيف البيانات قبل الإرسال")
    
    # الحل الثالث: تحسين دالة الإرسال
    print("\n3️⃣ تحسين دالة إرسال الهوت سبوت:")
    print("   • فحص البيانات قبل الإرسال")
    print("   • إرسال الحقول الأساسية فقط عند الفشل")
    print("   • تسجيل مفصل للأخطاء")
    
    return True

def create_improved_clean_function():
    """إنشاء دالة تنظيف محسنة"""
    print("\n📝 دالة تنظيف النصوص المحسنة:")
    
    clean_function_code = '''
def clean_text_for_mikrotik(self, text):
    """تنظيف النص من الرموز التي تسبب مشاكل ترميز في MikroTik - محسن"""
    if not text:
        return ""

    try:
        # تحويل إلى نص
        text_str = str(text)
        
        # إزالة الرموز غير ASCII بشكل صارم
        clean_text = text_str.encode('ascii', 'ignore').decode('ascii')
        
        # إزالة المسافات الزائدة
        clean_text = clean_text.strip()
        
        # التأكد من أن النص ليس فارغاً أو يحتوي على رموز خاصة فقط
        if not clean_text or not clean_text.replace(' ', '').replace('-', '').replace('_', ''):
            return ""
        
        return clean_text

    except Exception as e:
        self.logger.error(f"خطأ في تنظيف النص: {str(e)}")
        # في حالة الخطأ، نعيد نص فارغ لتجنب مشاكل الترميز
        return ""
'''
    
    print(clean_function_code)
    return clean_function_code

def create_improved_send_function():
    """إنشاء دالة إرسال محسنة"""
    print("\n📤 تحسينات دالة الإرسال:")
    
    improvements = [
        "✅ فحص البيانات قبل الإرسال",
        "✅ معالجة أخطاء الترميز",
        "✅ إرسال تدريجي للحقول",
        "✅ تسجيل مفصل للأخطاء",
        "✅ إعادة المحاولة مع حقول أساسية"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    send_function_tips = '''
# تحسينات مطلوبة في دالة send_to_mikrotik:

1. إضافة فحص البيانات:
   - التأكد من أن جميع الحقول نظيفة
   - فحص وجود رموز غير متوافقة
   - تسجيل البيانات قبل الإرسال

2. معالجة أخطاء الترميز:
   try:
       api.get_resource('/ip/hotspot/user').add(**params)
   except UnicodeDecodeError as e:
       # إعادة المحاولة مع حقول أساسية فقط
       basic_params = {'name': clean_username, 'password': clean_password, 'profile': clean_profile}
       api.get_resource('/ip/hotspot/user').add(**basic_params)

3. تنظيف قاعدة البيانات:
   - حذف المستخدمين الذين يحتوون على رموز غير متوافقة
   - إعادة إنشاؤهم بأسماء نظيفة
'''
    
    print(send_function_tips)
    return send_function_tips

def create_database_cleanup_script():
    """إنشاء سكريبت تنظيف قاعدة البيانات"""
    print("\n🧹 سكريبت تنظيف قاعدة بيانات MikroTik:")
    
    cleanup_script = '''
# سكريبت MikroTik لتنظيف المستخدمين الذين يحتوون على رموز غير متوافقة

:log info "بدء تنظيف مستخدمي الهوت سبوت"

# البحث عن المستخدمين الذين قد يحتوون على رموز مشكلة
:foreach user in=[/ip hotspot user find] do={
    :local userName [/ip hotspot user get $user name]
    :local userComment [/ip hotspot user get $user comment]
    
    # فحص وجود رموز غير ASCII في الاسم أو التعليق
    :local hasIssue false
    
    # إذا كان هناك مشكلة، احذف المستخدم
    :if ($hasIssue = true) do={
        :log warning ("حذف مستخدم مشكل: " . $userName)
        /ip hotspot user remove $user
    }
}

:log info "انتهى تنظيف مستخدمي الهوت سبوت"
'''
    
    print(cleanup_script)
    return cleanup_script

def provide_step_by_step_solution():
    """توفير حل خطوة بخطوة"""
    print("\n📋 الحل خطوة بخطوة:")
    
    steps = [
        {
            "step": "1️⃣ تحديث دالة تنظيف النصوص",
            "action": "استبدال الدالة الحالية بالدالة المحسنة",
            "file": "card.py",
            "line": "حوالي السطر 7769"
        },
        {
            "step": "2️⃣ تحسين معالجة الأخطاء في دالة الإرسال",
            "action": "إضافة معالجة UnicodeDecodeError",
            "file": "card.py", 
            "line": "حوالي السطر 8677 في دالة send_to_mikrotik"
        },
        {
            "step": "3️⃣ تنظيف قاعدة بيانات MikroTik",
            "action": "تشغيل سكريبت التنظيف في MikroTik",
            "file": "MikroTik Terminal",
            "line": "نسخ ولصق السكريبت"
        },
        {
            "step": "4️⃣ اختبار الإرسال",
            "action": "إنشاء مستخدم اختبار واحد",
            "file": "البرنامج",
            "line": "استخدام عدد 1 للاختبار"
        }
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"\n{step['step']}: {step['action']}")
        print(f"   📁 الملف: {step['file']}")
        print(f"   📍 الموقع: {step['line']}")
    
    return steps

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح مشكلة ترميز الهوت سبوت")
    print("=" * 70)
    
    # تحليل المشكلة
    fix_hotspot_encoding_issue()
    
    # إنشاء الحلول
    create_improved_clean_function()
    create_improved_send_function()
    create_database_cleanup_script()
    
    # توفير الحل خطوة بخطوة
    provide_step_by_step_solution()
    
    print("\n🎯 الخلاصة:")
    print("   ✅ تم تحديد المشكلة: ترميز البيانات في MikroTik")
    print("   ✅ تم توفير الحلول: دوال محسنة وسكريبت تنظيف")
    print("   ✅ تم توضيح الخطوات: حل تدريجي ومنظم")
    
    print("\n💡 التوصيات:")
    print("   1. ابدأ بتحديث دالة تنظيف النصوص")
    print("   2. اختبر مع مستخدم واحد أولاً")
    print("   3. نظف قاعدة البيانات إذا لزم الأمر")
    print("   4. استخدم الحقول الأساسية فقط عند الفشل")
    
    print("\n🎉 إصلاح مشكلة ترميز الهوت سبوت مكتمل!")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
