import os
import shutil

try:
    # نسخ الملف الأصلي
    print("نسخ الملف الأصلي...")
    with open('الاصلي.py', 'r', encoding='utf-8') as source:
        content = source.read()
    
    # تطبيق التعديلات الأساسية
    print("تطبيق التعديلات...")
    
    # تغيير العنوان
    content = content.replace(
        'الإصدار 2.0',
        'الإصدار المعدل'
    )
    
    # إضافة متغير فتح PDF
    content = content.replace(
        'self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت',
        '''self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت
        
        # إضافة متغير لفتح PDF تلقائياً (التعديل الجديد)
        self.auto_open_pdf_var = tk.BooleanVar(value=True)'''
    )
    
    # تعديل الإيميل للهوت سبوت
    content = content.replace(
        'email_suffix = ".bot"',
        '''# تحديد نهاية الإيميل حسب النظام (التعديل الجديد)
            if self.system_type == 'hotspot':
                email_suffix = ".pro"  # للهوت سبوت
            else:
                email_suffix = ".bot"  # للأنظمة الأخرى'''
    )
    
    # كتابة الملف المعدل
    print("كتابة الملف المعدل...")
    with open('المعدل.py', 'w', encoding='utf-8') as target:
        target.write(content)
    
    print("تم إنشاء الملف المعدل بنجاح!")
    
    # التحقق
    if os.path.exists('المعدل.py'):
        size = os.path.getsize('المعدل.py')
        print(f"حجم الملف: {size:,} بايت")
        
        with open('المعدل.py', 'r', encoding='utf-8') as f:
            lines = len(f.readlines())
        print(f"عدد الأسطر: {lines:,}")
    
except Exception as e:
    print(f"خطأ: {e}")
    import traceback
    traceback.print_exc()
