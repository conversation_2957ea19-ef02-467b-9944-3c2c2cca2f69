import sqlite3
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, font
from PIL import Image, ImageTk
import random
import string
import json
import os
import logging
import hashlib
import base64
import re
import ipaddress
import shutil
import threading
from datetime import datetime, timedelta
from pathlib import Path
try:
    import routeros_api
    ROUTEROS_AVAILABLE = True
except ImportError:
    ROUTEROS_AVAILABLE = False
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import qrcode
import io
from reportlab.lib.utils import ImageReader
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

class MikroTikCardGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 مولد كروت وسكريبتات MikroTik - الإصدار 2.0")
        self.root.geometry("1400x900")  # حجم نافذة محسن
        self.root.resizable(True, True)

        # تحسين الحد الأدنى لحجم النافذة
        self.root.minsize(1000, 700)

        # تحسين أيقونة النافذة (إذا كانت متوفرة)
        try:
            # يمكن إضافة أيقونة مخصصة هنا
            pass
        except:
            pass

        # إعداد نظام السجلات
        self.setup_logging()
        self.logger.info("تم بدء تشغيل التطبيق")

        # إعداد المجلدات الأساسية
        self.setup_directories()

        # إعداد التشفير
        self.encryption_key = self.get_or_create_encryption_key()

        self.system_type = None
        self.generated_credentials = []
        self.background_image = None
        self.background_image_path = None
        self.settings_file = "config/mikrotik_settings.json"
        self.user_manager_templates_file = "config/mikrotik_user_manager_templates.json"
        self.hotspot_templates_file = "config/mikrotik_hotspot_templates.json"
        self.profiles = []
        self.last_serial = 0
        self.dragging_element = None
        self.element_positions = {}
        self.canvas_elements = {}
        self.progress_var = tk.DoubleVar()
        self.connection_status_var = tk.StringVar(value="غير متصل")
        self.user_email = tk.StringVar()
        self.caller_id_bind_var = tk.BooleanVar()
        self.run_script_before_cleanup_var = tk.BooleanVar()
        self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت

        self.api_connection = None
        self.api = None

        # إعداد الواجهة
        self.setup_styles()

        try:
            self.initialize_database()
            self.setup_selection_gui()
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.logger.info("تم إعداد التطبيق بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في إعداد التطبيق: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في إعداد التطبيق: {str(e)}")

    def setup_logging(self):
        """إعداد نظام السجلات"""
        try:
            # إنشاء مجلد السجلات إذا لم يكن موجوداً
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)

            # إعداد تنسيق السجل
            log_format = '%(asctime)s - %(levelname)s - %(message)s'

            # إعداد ملف السجل
            log_file = log_dir / f"mikrotik_generator_{datetime.now().strftime('%Y%m%d')}.log"

            # إعداد المسجل
            logging.basicConfig(
                level=logging.INFO,
                format=log_format,
                handlers=[
                    logging.FileHandler(log_file, encoding='utf-8'),
                    logging.StreamHandler()
                ]
            )

            self.logger = logging.getLogger(__name__)

        except Exception as e:
            # في حالة فشل إعداد السجلات، استخدم مسجل افتراضي
            self.logger = logging.getLogger(__name__)
            self.logger.setLevel(logging.INFO)

    def setup_directories(self):
        """إنشاء المجلدات الأساسية"""
        try:
            directories = ["config", "backups", "exports", "logs", "templates"]
            for directory in directories:
                Path(directory).mkdir(exist_ok=True)
            self.logger.info("تم إنشاء المجلدات الأساسية")
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المجلدات: {str(e)}")

    def get_or_create_encryption_key(self):
        """الحصول على مفتاح التشفير أو إنشاؤه"""
        try:
            key_file = Path("config/encryption.key")
            if key_file.exists():
                with open(key_file, 'rb') as f:
                    return f.read()
            else:
                # إنشاء مفتاح جديد
                key = os.urandom(32)  # 256-bit key
                with open(key_file, 'wb') as f:
                    f.write(key)
                self.logger.info("تم إنشاء مفتاح تشفير جديد")
                return key
        except Exception as e:
            self.logger.error(f"خطأ في إعداد التشفير: {str(e)}")
            return os.urandom(32)  # مفتاح مؤقت

    def setup_styles(self):
        """إعداد أنماط الواجهة المحسنة"""
        try:
            self.style = ttk.Style()

            # نظام الألوان الموحد
            self.colors = {
                'primary': '#2E86AB',      # أزرق أساسي
                'secondary': '#A23B72',    # وردي ثانوي
                'accent': '#F18F01',       # برتقالي للتأكيد
                'success': '#4CAF50',      # أخضر للنجاح
                'warning': '#FF9800',      # برتقالي للتحذير
                'error': '#F44336',        # أحمر للخطأ
                'background': '#F5F5F5',   # خلفية فاتحة
                'surface': '#FFFFFF',      # سطح أبيض
                'text_primary': '#212121', # نص أساسي
                'text_secondary': '#757575', # نص ثانوي
                'border': '#E0E0E0',       # حدود
                'hover': '#E3F2FD'         # تأثير التمرير
            }

            # إعداد الخطوط المحسنة
            self.fonts = {
                'default': ('Segoe UI', 9),
                'heading': ('Segoe UI', 12, 'bold'),
                'subheading': ('Segoe UI', 10, 'bold'),
                'small': ('Segoe UI', 8),
                'arabic': ('Tahoma', 9),  # خط أفضل للعربية
                'arabic_heading': ('Tahoma', 12, 'bold')
            }

            # تطبيق نمط حديث
            self.style.theme_use('clam')

            # تخصيص الأنماط الأساسية
            self.style.configure('TLabel',
                               font=self.fonts['arabic'],
                               foreground=self.colors['text_primary'])

            self.style.configure('TButton',
                               font=self.fonts['arabic'],
                               foreground=self.colors['surface'],
                               background=self.colors['primary'],
                               borderwidth=0,
                               focuscolor='none',
                               padding=(10, 5))

            self.style.map('TButton',
                          background=[('active', self.colors['secondary']),
                                    ('pressed', self.colors['accent'])])

            self.style.configure('TEntry',
                               font=self.fonts['arabic'],
                               fieldbackground=self.colors['surface'],
                               borderwidth=1,
                               relief='solid',
                               bordercolor=self.colors['border'])

            self.style.map('TEntry',
                          bordercolor=[('focus', self.colors['primary'])])

            self.style.configure('TCombobox',
                               font=self.fonts['arabic'],
                               fieldbackground=self.colors['surface'],
                               borderwidth=1,
                               relief='solid',
                               bordercolor=self.colors['border'])

            self.style.configure('TLabelFrame',
                               font=self.fonts['arabic_heading'],
                               foreground=self.colors['primary'],
                               borderwidth=2,
                               relief='solid',
                               bordercolor=self.colors['border'])

            self.style.configure('TLabelFrame.Label',
                               font=self.fonts['arabic_heading'],
                               foreground=self.colors['primary'])

            self.style.configure('TNotebook',
                               background=self.colors['background'],
                               borderwidth=0)

            self.style.configure('TNotebook.Tab',
                               font=self.fonts['arabic'],
                               padding=(20, 8),
                               background=self.colors['surface'],
                               foreground=self.colors['text_primary'])

            self.style.map('TNotebook.Tab',
                          background=[('selected', self.colors['primary']),
                                    ('active', self.colors['hover'])],
                          foreground=[('selected', self.colors['surface'])])

            # أنماط مخصصة للحالات
            self.style.configure("Connected.TLabel",
                               foreground=self.colors['success'],
                               font=self.fonts['arabic'])

            self.style.configure("Disconnected.TLabel",
                               foreground=self.colors['error'],
                               font=self.fonts['arabic'])

            self.style.configure("Error.TLabel",
                               foreground=self.colors['error'],
                               font=self.fonts['small'])

            self.style.configure("Success.TLabel",
                               foreground=self.colors['success'],
                               font=self.fonts['small'])

            self.style.configure("Heading.TLabel",
                               font=self.fonts['arabic_heading'],
                               foreground=self.colors['primary'])

            # أنماط أزرار مخصصة
            self.style.configure("Primary.TButton",
                               background=self.colors['primary'],
                               foreground=self.colors['surface'],
                               font=self.fonts['arabic'])

            self.style.configure("Success.TButton",
                               background=self.colors['success'],
                               foreground=self.colors['surface'],
                               font=self.fonts['arabic'])

            self.style.configure("Warning.TButton",
                               background=self.colors['warning'],
                               foreground=self.colors['surface'],
                               font=self.fonts['arabic'])

            self.style.configure("Error.TButton",
                               background=self.colors['error'],
                               foreground=self.colors['surface'],
                               font=self.fonts['arabic'])

            # تخصيص شريط التقدم
            self.style.configure("TProgressbar",
                               background=self.colors['primary'],
                               troughcolor=self.colors['border'],
                               borderwidth=0,
                               lightcolor=self.colors['primary'],
                               darkcolor=self.colors['primary'])

            # تخصيص Treeview
            self.style.configure("Treeview",
                               font=self.fonts['arabic'],
                               background=self.colors['surface'],
                               foreground=self.colors['text_primary'],
                               fieldbackground=self.colors['surface'])

            self.style.configure("Treeview.Heading",
                               font=self.fonts['arabic'],
                               background=self.colors['primary'],
                               foreground=self.colors['surface'])

            # تعيين خلفية النافذة الرئيسية
            self.root.configure(bg=self.colors['background'])

            self.connection_status_var.trace("w", self.update_status_color)

            # إنشاء الأيقونات النصية
            self.create_icons()

        except Exception as e:
            self.logger.error(f"خطأ في إعداد الأنماط: {str(e)}")

    def create_icons(self):
        """إنشاء أيقونات نصية للأزرار"""
        try:
            self.icons = {
                'connect': '🔗',
                'disconnect': '❌',
                'test': '🧪',
                'generate': '⚙️',
                'save': '💾',
                'load': '📁',
                'export': '📤',
                'import': '📥',
                'settings': '⚙️',
                'help': '❓',
                'refresh': '🔄',
                'delete': '🗑️',
                'edit': '✏️',
                'add': '➕',
                'remove': '➖',
                'search': '🔍',
                'print': '🖨️',
                'pdf': '📄',
                'script': '📜',
                'database': '🗄️',
                'network': '🌐',
                'user': '👤',
                'password': '🔐',
                'profile': '📋',
                'template': '📝',
                'backup': '💾',
                'restore': '♻️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': 'ℹ️'
            }
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الأيقونات: {str(e)}")
            self.icons = {}

    def create_styled_button(self, parent, text, command=None, style="TButton", icon=None, width=None):
        """إنشاء زر محسن مع أيقونة"""
        try:
            if icon and icon in self.icons:
                button_text = f"{self.icons[icon]} {text}"
            else:
                button_text = text

            button = ttk.Button(parent, text=button_text, command=command, style=style)

            if width:
                button.configure(width=width)

            return button
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الزر: {str(e)}")
            return ttk.Button(parent, text=text, command=command)

    def encrypt_password(self, password):
        """تشفير كلمة المرور"""
        try:
            if not password:
                return ""
            # استخدام base64 للتشفير البسيط (يمكن تحسينه لاحقاً)
            encoded = base64.b64encode(password.encode('utf-8')).decode('utf-8')
            return encoded
        except Exception as e:
            self.logger.error(f"خطأ في تشفير كلمة المرور: {str(e)}")
            return password

    def decrypt_password(self, encrypted_password):
        """فك تشفير كلمة المرور"""
        try:
            if not encrypted_password:
                return ""
            decoded = base64.b64decode(encrypted_password.encode('utf-8')).decode('utf-8')
            return decoded
        except Exception as e:
            self.logger.error(f"خطأ في فك تشفير كلمة المرور: {str(e)}")
            return encrypted_password

    def validate_ip_address(self, ip):
        """التحقق من صحة عنوان IP"""
        try:
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False

    def validate_port(self, port):
        """التحقق من صحة رقم المنفذ"""
        try:
            port_num = int(port)
            return 1 <= port_num <= 65535
        except ValueError:
            return False

    def validate_email(self, email):
        """التحقق من صحة البريد الإلكتروني"""
        if not email:
            return True  # البريد الإلكتروني اختياري
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def validate_number_input(self, value, min_val=0, max_val=None):
        """التحقق من صحة المدخلات الرقمية"""
        try:
            num = int(value)
            if num < min_val:
                return False
            if max_val is not None and num > max_val:
                return False
            return True
        except ValueError:
            return False

    def validate_required_field(self, value, field_name):
        """التحقق من الحقول المطلوبة"""
        if not value or not value.strip():
            messagebox.showerror("خطأ في التحقق", f"الحقل '{field_name}' مطلوب")
            return False
        return True

    def show_validation_error(self, message):
        """عرض رسالة خطأ في التحقق"""
        self.logger.warning(f"خطأ في التحقق: {message}")
        messagebox.showerror("خطأ في التحقق", message)

    def create_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            backup_dir = Path("backups")
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"mikrotik_cards_backup_{timestamp}.db"

            if Path("mikrotik_cards.db").exists():
                shutil.copy2("mikrotik_cards.db", backup_file)
                self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_file}")
                return str(backup_file)
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return None

    def initialize_database(self):
        """إعداد قاعدة البيانات مع معالجة الأخطاء"""
        try:
            # إنشاء نسخة احتياطية قبل أي تعديل
            self.create_backup()

            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()

            # إنشاء جدول بيانات اعتماد User Manager
            cursor.execute('''CREATE TABLE IF NOT EXISTS user_manager_credentials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                password TEXT,
                profile TEXT,
                comment TEXT,
                location TEXT,
                email TEXT,
                price TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''')

            # إنشاء جدول بيانات اعتماد Hotspot
            cursor.execute('''CREATE TABLE IF NOT EXISTS hotspot_credentials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                password TEXT,
                profile TEXT,
                comment TEXT,
                location TEXT,
                limit_bytes TEXT,
                limit_unit TEXT,
                days TEXT,
                email_template TEXT,
                price TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''')

            # إنشاء جدول سجل العمليات
            cursor.execute('''CREATE TABLE IF NOT EXISTS operation_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                operation_type TEXT NOT NULL,
                description TEXT,
                status TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                details TEXT
            )''')

            # إنشاء فهارس لتحسين الأداء
            cursor.execute('''CREATE INDEX IF NOT EXISTS idx_user_manager_username
                             ON user_manager_credentials(username)''')
            cursor.execute('''CREATE INDEX IF NOT EXISTS idx_hotspot_username
                             ON hotspot_credentials(username)''')
            cursor.execute('''CREATE INDEX IF NOT EXISTS idx_operation_log_timestamp
                             ON operation_log(timestamp)''')

            conn.commit()
            conn.close()

            self.logger.info("تم إعداد قاعدة البيانات بنجاح")

        except sqlite3.Error as e:
            self.logger.error(f"خطأ في قاعدة البيانات: {str(e)}")
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في إعداد قاعدة البيانات: {str(e)}")
        except Exception as e:
            self.logger.error(f"خطأ غير متوقع في إعداد قاعدة البيانات: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ غير متوقع: {str(e)}")

    def log_operation(self, operation_type, description, status="نجح", details=""):
        """تسجيل العمليات في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()
            cursor.execute('''INSERT INTO operation_log
                             (operation_type, description, status, details)
                             VALUES (?, ?, ?, ?)''',
                          (operation_type, description, status, details))
            conn.commit()
            conn.close()
            self.logger.info(f"تم تسجيل العملية: {operation_type} - {description}")
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل العملية: {str(e)}")

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        try:
            menubar = tk.Menu(self.root)
            self.root.config(menu=menubar)

            # قائمة الملف
            file_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="ملف", menu=file_menu)
            file_menu.add_command(label="نسخة احتياطية جديدة", command=self.manual_backup, accelerator="Ctrl+B")
            file_menu.add_command(label="استيراد البيانات", command=self.import_data, accelerator="Ctrl+I")
            file_menu.add_command(label="تصدير البيانات", command=self.export_data, accelerator="Ctrl+E")
            file_menu.add_separator()
            file_menu.add_command(label="إعدادات", command=self.show_settings, accelerator="Ctrl+S")
            file_menu.add_separator()
            file_menu.add_command(label="خروج", command=self.on_closing, accelerator="Ctrl+Q")

            # قائمة الأدوات
            tools_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="أدوات", menu=tools_menu)
            tools_menu.add_command(label="تنظيف قاعدة البيانات", command=self.cleanup_database)
            tools_menu.add_command(label="عرض السجلات", command=self.show_logs)
            tools_menu.add_command(label="اختبار الاتصال", command=self.test_api_connection)

            # قائمة المساعدة
            help_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="مساعدة", menu=help_menu)
            help_menu.add_command(label="دليل المستخدم", command=self.show_help)
            help_menu.add_command(label="اختصارات لوحة المفاتيح", command=self.show_shortcuts)
            help_menu.add_command(label="حول البرنامج", command=self.show_about)

            # إعداد اختصارات لوحة المفاتيح
            self.setup_keyboard_shortcuts()

        except Exception as e:
            self.logger.error(f"خطأ في إعداد شريط القوائم: {str(e)}")

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        try:
            self.root.bind('<Control-b>', lambda e: self.manual_backup())
            self.root.bind('<Control-i>', lambda e: self.import_data())
            self.root.bind('<Control-e>', lambda e: self.export_data())
            self.root.bind('<Control-s>', lambda e: self.show_settings())
            self.root.bind('<Control-q>', lambda e: self.on_closing())
            self.root.bind('<F1>', lambda e: self.show_help())
            self.root.bind('<F5>', lambda e: self.test_api_connection())
        except Exception as e:
            self.logger.error(f"خطأ في إعداد اختصارات لوحة المفاتيح: {str(e)}")

    def manual_backup(self):
        """إنشاء نسخة احتياطية يدوية"""
        try:
            backup_file = self.create_backup()
            if backup_file:
                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية:\n{backup_file}")
                self.log_operation("نسخة احتياطية", "تم إنشاء نسخة احتياطية يدوية", "نجح")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")
        except Exception as e:
            self.logger.error(f"خطأ في النسخة الاحتياطية اليدوية: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في النسخة الاحتياطية: {str(e)}")

    def show_settings(self):
        """عرض نافذة الإعدادات المتقدمة"""
        try:
            settings_window = tk.Toplevel(self.root)
            settings_window.title("الإعدادات المتقدمة")
            settings_window.geometry("400x300")
            settings_window.transient(self.root)
            settings_window.grab_set()

            # إعدادات النسخ الاحتياطي
            backup_frame = ttk.LabelFrame(settings_window, text="إعدادات النسخ الاحتياطي", padding=10)
            backup_frame.pack(fill=tk.X, padx=10, pady=5)

            ttk.Label(backup_frame, text="نسخ احتياطي تلقائي كل (دقيقة):").pack(anchor="w")
            self.auto_backup_var = tk.StringVar(value="60")
            ttk.Entry(backup_frame, textvariable=self.auto_backup_var, width=10).pack(anchor="w", pady=2)

            # إعدادات السجلات
            log_frame = ttk.LabelFrame(settings_window, text="إعدادات السجلات", padding=10)
            log_frame.pack(fill=tk.X, padx=10, pady=5)

            self.log_level_var = tk.StringVar(value="INFO")
            ttk.Label(log_frame, text="مستوى السجل:").pack(anchor="w")
            log_combo = ttk.Combobox(log_frame, textvariable=self.log_level_var,
                                   values=["DEBUG", "INFO", "WARNING", "ERROR"], state="readonly")
            log_combo.pack(anchor="w", pady=2)

            # أزرار الحفظ والإلغاء
            button_frame = ttk.Frame(settings_window)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            ttk.Button(button_frame, text="حفظ", command=lambda: self.save_advanced_settings(settings_window)).pack(side=tk.RIGHT, padx=5)
            ttk.Button(button_frame, text="إلغاء", command=settings_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            self.logger.error(f"خطأ في عرض الإعدادات: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في عرض الإعدادات: {str(e)}")

    def save_advanced_settings(self, window):
        """حفظ الإعدادات المتقدمة"""
        try:
            # حفظ الإعدادات هنا
            self.logger.info("تم حفظ الإعدادات المتقدمة")
            window.destroy()
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")

    def show_help(self):
        """عرض دليل المستخدم"""
        try:
            help_window = tk.Toplevel(self.root)
            help_window.title("دليل المستخدم")
            help_window.geometry("600x400")
            help_window.transient(self.root)

            help_text = tk.Text(help_window, wrap=tk.WORD, font=("Arial", 10))
            scrollbar = ttk.Scrollbar(help_window, orient="vertical", command=help_text.yview)
            help_text.configure(yscrollcommand=scrollbar.set)

            help_content = """
دليل استخدام مولد كروت MikroTik

1. اختيار النظام:
   - User Manager: لإدارة المستخدمين في MikroTik
   - Hotspot: لإدارة نقاط الاتصال اللاسلكي

2. إعداد الاتصال:
   - أدخل عنوان IP الخاص بجهاز MikroTik
   - أدخل اسم المستخدم وكلمة المرور
   - اختبر الاتصال قبل المتابعة

3. إعدادات التوليد:
   - اختر نوع الكود (أرقام، حروف، أو مختلط)
   - حدد طول الكود وعدد الحسابات
   - أضف البادئة والنهاية حسب الحاجة

4. إعدادات PDF:
   - ارفع صورة خلفية للكروت
   - حدد عدد الأعمدة والصفوف
   - اضبط مواقع النصوص والعناصر

5. التصدير والحفظ:
   - احفظ كملف نصي أو سكريبت MikroTik
   - صدّر كملف PDF للطباعة
   - أنشئ نسخ احتياطية دورية

اختصارات لوحة المفاتيح:
- Ctrl+B: نسخة احتياطية
- Ctrl+I: استيراد البيانات
- Ctrl+E: تصدير البيانات
- Ctrl+S: الإعدادات
- F1: المساعدة
- F5: اختبار الاتصال
            """

            help_text.insert(tk.END, help_content)
            help_text.config(state=tk.DISABLED)

            help_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            self.logger.error(f"خطأ في عرض المساعدة: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في عرض المساعدة: {str(e)}")

    def show_shortcuts(self):
        """عرض اختصارات لوحة المفاتيح"""
        shortcuts_text = """
اختصارات لوحة المفاتيح:

Ctrl+B - إنشاء نسخة احتياطية
Ctrl+I - استيراد البيانات
Ctrl+E - تصدير البيانات
Ctrl+S - فتح الإعدادات
Ctrl+Q - خروج من البرنامج
F1 - عرض المساعدة
F5 - اختبار الاتصال
        """
        messagebox.showinfo("اختصارات لوحة المفاتيح", shortcuts_text)

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
مولد كروت وسكريبتات MikroTik
الإصدار 2.0

برنامج شامل لتوليد كروت الإنترنت وإدارة المستخدمين
في أجهزة MikroTik RouterOS

الميزات:
- دعم User Manager و Hotspot
- توليد كروت PDF قابلة للطباعة
- إدارة قاعدة البيانات
- نسخ احتياطية تلقائية
- تصدير واستيراد البيانات
- واجهة عربية سهلة الاستخدام

تطوير: فريق التطوير
        """
        messagebox.showinfo("حول البرنامج", about_text)

    def setup_selection_gui(self):
        """إعداد واجهة اختيار النظام المحسنة"""
        try:
            # إعداد شريط القوائم
            self.setup_menu_bar()

            # إطار رئيسي مع خلفية محسنة
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # عنوان رئيسي محسن
            title_frame = ttk.Frame(main_frame)
            title_frame.pack(pady=(0, 30))

            title_label = ttk.Label(title_frame,
                                  text="🚀 مولد كروت وسكريبتات MikroTik",
                                  style="Heading.TLabel")
            title_label.pack()

            subtitle_label = ttk.Label(title_frame,
                                     text="اختر نظام التشغيل للبدء",
                                     font=self.fonts['arabic'])
            subtitle_label.pack(pady=(5, 0))

            # إطار الأزرار المحسن
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=20)

            # زر User Manager مع أيقونة
            user_manager_btn = self.create_styled_button(
                button_frame,
                "User Manager",
                command=lambda: self.select_system('user_manager'),
                style="Primary.TButton",
                icon="user",
                width=25
            )
            user_manager_btn.pack(side=tk.LEFT, padx=15, pady=10)

            # زر Hotspot مع أيقونة
            hotspot_btn = self.create_styled_button(
                button_frame,
                "Hotspot",
                command=lambda: self.select_system('hotspot'),
                style="Success.TButton",
                icon="network",
                width=25
            )
            hotspot_btn.pack(side=tk.LEFT, padx=15, pady=10)

            # إطار المعلومات
            info_frame = ttk.LabelFrame(main_frame, text="معلومات النظام", padding=15)
            info_frame.pack(fill=tk.X, pady=(30, 0))

            # معلومات النظامين
            systems_info = ttk.Frame(info_frame)
            systems_info.pack(fill=tk.X)

            # معلومات User Manager
            um_frame = ttk.Frame(systems_info)
            um_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

            ttk.Label(um_frame, text="👤 User Manager",
                     font=self.fonts['arabic_heading']).pack(anchor="w")
            ttk.Label(um_frame, text="• إدارة المستخدمين في MikroTik",
                     font=self.fonts['arabic']).pack(anchor="w", padx=(10, 0))
            ttk.Label(um_frame, text="• دعم الإصدارات v6 و v7",
                     font=self.fonts['arabic']).pack(anchor="w", padx=(10, 0))
            ttk.Label(um_frame, text="• إنشاء حسابات متقدمة",
                     font=self.fonts['arabic']).pack(anchor="w", padx=(10, 0))

            # معلومات Hotspot
            hs_frame = ttk.Frame(systems_info)
            hs_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

            ttk.Label(hs_frame, text="🌐 Hotspot",
                     font=self.fonts['arabic_heading']).pack(anchor="w")
            ttk.Label(hs_frame, text="• إدارة نقاط الاتصال اللاسلكي",
                     font=self.fonts['arabic']).pack(anchor="w", padx=(10, 0))
            ttk.Label(hs_frame, text="• تحديد حدود البيانات والوقت",
                     font=self.fonts['arabic']).pack(anchor="w", padx=(10, 0))
            ttk.Label(hs_frame, text="• إنشاء كروت ضيوف",
                     font=self.fonts['arabic']).pack(anchor="w", padx=(10, 0))

            # شريط الحالة
            status_frame = ttk.Frame(main_frame)
            status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))

            ttk.Label(status_frame, text="✅ جاهز للاستخدام",
                     style="Success.TLabel").pack(side=tk.LEFT)

            # معلومات الإصدار
            ttk.Label(status_frame, text="الإصدار 2.0",
                     font=self.fonts['small']).pack(side=tk.RIGHT)

            self.logger.info("تم إعداد واجهة اختيار النظام المحسنة")

        except Exception as e:
            self.logger.error(f"خطأ في إعداد واجهة الاختيار: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في إعداد الواجهة: {str(e)}")

    def import_data(self):
        """استيراد البيانات من ملف خارجي"""
        try:
            file_types = [
                ("CSV files", "*.csv"),
                ("Excel files", "*.xlsx"),
                ("JSON files", "*.json"),
                ("All files", "*.*")
            ]

            filename = filedialog.askopenfilename(
                title="اختر ملف البيانات للاستيراد",
                filetypes=file_types
            )

            if not filename:
                return

            file_ext = Path(filename).suffix.lower()

            if file_ext == '.csv':
                self.import_from_csv(filename)
            elif file_ext == '.xlsx' and PANDAS_AVAILABLE:
                self.import_from_excel(filename)
            elif file_ext == '.json':
                self.import_from_json(filename)
            else:
                messagebox.showerror("خطأ", "نوع الملف غير مدعوم")

        except Exception as e:
            self.logger.error(f"خطأ في استيراد البيانات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في استيراد البيانات: {str(e)}")

    def import_from_csv(self, filename):
        """استيراد البيانات من ملف CSV"""
        try:
            import csv
            with open(filename, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                imported_count = 0

                conn = sqlite3.connect('mikrotik_cards.db')
                cursor = conn.cursor()

                for row in reader:
                    if self.system_type == 'user_manager':
                        cursor.execute('''INSERT INTO user_manager_credentials
                                         (username, password, profile, comment, location, email, price)
                                         VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                      (row.get('username', ''), row.get('password', ''),
                                       row.get('profile', ''), row.get('comment', ''),
                                       row.get('location', ''), row.get('email', ''),
                                       row.get('price', '')))
                    else:
                        cursor.execute('''INSERT INTO hotspot_credentials
                                         (username, password, profile, comment, location,
                                          limit_bytes, limit_unit, days, email_template, price)
                                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                      (row.get('username', ''), row.get('password', ''),
                                       row.get('profile', ''), row.get('comment', ''),
                                       row.get('location', ''), row.get('limit_bytes', ''),
                                       row.get('limit_unit', ''), row.get('days', ''),
                                       row.get('email_template', ''), row.get('price', '')))
                    imported_count += 1

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", f"تم استيراد {imported_count} سجل بنجاح")
                self.log_operation("استيراد", f"تم استيراد {imported_count} سجل من CSV", "نجح")

        except Exception as e:
            self.logger.error(f"خطأ في استيراد CSV: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في استيراد CSV: {str(e)}")

    def import_from_excel(self, filename):
        """استيراد البيانات من ملف Excel"""
        try:
            if not PANDAS_AVAILABLE:
                messagebox.showerror("خطأ", "مكتبة pandas غير متوفرة لاستيراد ملفات Excel")
                return

            df = pd.read_excel(filename)
            imported_count = 0

            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()

            for _, row in df.iterrows():
                if self.system_type == 'user_manager':
                    cursor.execute('''INSERT INTO user_manager_credentials
                                     (username, password, profile, comment, location, email, price)
                                     VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                  (str(row.get('username', '')), str(row.get('password', '')),
                                   str(row.get('profile', '')), str(row.get('comment', '')),
                                   str(row.get('location', '')), str(row.get('email', '')),
                                   str(row.get('price', ''))))
                else:
                    cursor.execute('''INSERT INTO hotspot_credentials
                                     (username, password, profile, comment, location,
                                      limit_bytes, limit_unit, days, email_template, price)
                                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                  (str(row.get('username', '')), str(row.get('password', '')),
                                   str(row.get('profile', '')), str(row.get('comment', '')),
                                   str(row.get('location', '')), str(row.get('limit_bytes', '')),
                                   str(row.get('limit_unit', '')), str(row.get('days', '')),
                                   str(row.get('email_template', '')), str(row.get('price', ''))))
                imported_count += 1

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم استيراد {imported_count} سجل بنجاح")
            self.log_operation("استيراد", f"تم استيراد {imported_count} سجل من Excel", "نجح")

        except Exception as e:
            self.logger.error(f"خطأ في استيراد Excel: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في استيراد Excel: {str(e)}")

    def import_from_json(self, filename):
        """استيراد البيانات من ملف JSON"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                data = json.load(file)

            if not isinstance(data, list):
                messagebox.showerror("خطأ", "ملف JSON يجب أن يحتوي على قائمة من السجلات")
                return

            imported_count = 0
            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()

            for record in data:
                if self.system_type == 'user_manager':
                    cursor.execute('''INSERT INTO user_manager_credentials
                                     (username, password, profile, comment, location, email, price)
                                     VALUES (?, ?, ?, ?, ?, ?, ?)''',
                                  (record.get('username', ''), record.get('password', ''),
                                   record.get('profile', ''), record.get('comment', ''),
                                   record.get('location', ''), record.get('email', ''),
                                   record.get('price', '')))
                else:
                    cursor.execute('''INSERT INTO hotspot_credentials
                                     (username, password, profile, comment, location,
                                      limit_bytes, limit_unit, days, email_template, price)
                                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                  (record.get('username', ''), record.get('password', ''),
                                   record.get('profile', ''), record.get('comment', ''),
                                   record.get('location', ''), record.get('limit_bytes', ''),
                                   record.get('limit_unit', ''), record.get('days', ''),
                                   record.get('email_template', ''), record.get('price', '')))
                imported_count += 1

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم استيراد {imported_count} سجل بنجاح")
            self.log_operation("استيراد", f"تم استيراد {imported_count} سجل من JSON", "نجح")

        except Exception as e:
            self.logger.error(f"خطأ في استيراد JSON: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في استيراد JSON: {str(e)}")

    def export_data(self):
        """تصدير البيانات إلى ملف خارجي"""
        try:
            export_window = tk.Toplevel(self.root)
            export_window.title("تصدير البيانات")
            export_window.geometry("300x200")
            export_window.transient(self.root)
            export_window.grab_set()

            ttk.Label(export_window, text="اختر نوع الملف للتصدير:", font=("Arial", 10, "bold")).pack(pady=10)

            export_type = tk.StringVar(value="csv")

            ttk.Radiobutton(export_window, text="CSV", variable=export_type, value="csv").pack(anchor="w", padx=20)
            if PANDAS_AVAILABLE:
                ttk.Radiobutton(export_window, text="Excel", variable=export_type, value="excel").pack(anchor="w", padx=20)
            ttk.Radiobutton(export_window, text="JSON", variable=export_type, value="json").pack(anchor="w", padx=20)

            button_frame = ttk.Frame(export_window)
            button_frame.pack(pady=20)

            ttk.Button(button_frame, text="تصدير",
                      command=lambda: self.perform_export(export_type.get(), export_window)).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="إلغاء", command=export_window.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في نافذة التصدير: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في التصدير: {str(e)}")

    def perform_export(self, export_type, window):
        """تنفيذ عملية التصدير"""
        try:
            window.destroy()

            # تحديد نوع الملف والامتداد
            if export_type == "csv":
                file_ext = ".csv"
                file_types = [("CSV files", "*.csv")]
            elif export_type == "excel":
                file_ext = ".xlsx"
                file_types = [("Excel files", "*.xlsx")]
            else:  # json
                file_ext = ".json"
                file_types = [("JSON files", "*.json")]

            # اختيار مكان الحفظ
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"mikrotik_export_{timestamp}{file_ext}"

            filename = filedialog.asksaveasfilename(
                defaultextension=file_ext,
                filetypes=file_types,
                initialfile=default_name
            )

            if not filename:
                return

            # تصدير البيانات
            if export_type == "csv":
                self.export_to_csv(filename)
            elif export_type == "excel":
                self.export_to_excel(filename)
            else:
                self.export_to_json(filename)

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ التصدير: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في التصدير: {str(e)}")

    def select_system(self, system_type):
        """اختيار نوع النظام مع معالجة الأخطاء"""
        try:
            self.system_type = system_type
            self.logger.info(f"تم اختيار النظام: {system_type}")

            for widget in self.root.winfo_children():
                widget.destroy()

            self.setup_gui()
            self.load_settings()

            self.log_operation("اختيار النظام", f"تم اختيار نظام {system_type}", "نجح")

        except Exception as e:
            self.logger.error(f"خطأ في اختيار النظام: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في اختيار النظام: {str(e)}")

    def export_to_csv(self, filename):
        """تصدير البيانات إلى ملف CSV"""
        try:
            import csv

            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()

            if self.system_type == 'user_manager':
                cursor.execute("SELECT * FROM user_manager_credentials")
                fieldnames = ['id', 'username', 'password', 'profile', 'comment', 'location', 'email', 'price', 'created_date', 'updated_date']
            else:
                cursor.execute("SELECT * FROM hotspot_credentials")
                fieldnames = ['id', 'username', 'password', 'profile', 'comment', 'location', 'limit_bytes', 'limit_unit', 'days', 'email_template', 'price', 'created_date', 'updated_date']

            rows = cursor.fetchall()
            conn.close()

            with open(filename, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(fieldnames)
                writer.writerows(rows)

            messagebox.showinfo("نجح", f"تم تصدير {len(rows)} سجل إلى CSV بنجاح")
            self.log_operation("تصدير", f"تم تصدير {len(rows)} سجل إلى CSV", "نجح")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير CSV: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير CSV: {str(e)}")

    def export_to_excel(self, filename):
        """تصدير البيانات إلى ملف Excel"""
        try:
            if not PANDAS_AVAILABLE:
                messagebox.showerror("خطأ", "مكتبة pandas غير متوفرة لتصدير ملفات Excel")
                return

            conn = sqlite3.connect('mikrotik_cards.db')

            if self.system_type == 'user_manager':
                df = pd.read_sql_query("SELECT * FROM user_manager_credentials", conn)
            else:
                df = pd.read_sql_query("SELECT * FROM hotspot_credentials", conn)

            conn.close()

            df.to_excel(filename, index=False, engine='openpyxl')

            messagebox.showinfo("نجح", f"تم تصدير {len(df)} سجل إلى Excel بنجاح")
            self.log_operation("تصدير", f"تم تصدير {len(df)} سجل إلى Excel", "نجح")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير Excel: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير Excel: {str(e)}")

    def export_to_json(self, filename):
        """تصدير البيانات إلى ملف JSON"""
        try:
            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()

            if self.system_type == 'user_manager':
                cursor.execute("SELECT * FROM user_manager_credentials")
                columns = [description[0] for description in cursor.description]
            else:
                cursor.execute("SELECT * FROM hotspot_credentials")
                columns = [description[0] for description in cursor.description]

            rows = cursor.fetchall()
            conn.close()

            # تحويل البيانات إلى قائمة من القواميس
            data = []
            for row in rows:
                data.append(dict(zip(columns, row)))

            with open(filename, 'w', encoding='utf-8') as file:
                json.dump(data, file, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", f"تم تصدير {len(data)} سجل إلى JSON بنجاح")
            self.log_operation("تصدير", f"تم تصدير {len(data)} سجل إلى JSON", "نجح")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير JSON: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير JSON: {str(e)}")

    def cleanup_database(self):
        """تنظيف قاعدة البيانات"""
        try:
            cleanup_window = tk.Toplevel(self.root)
            cleanup_window.title("تنظيف قاعدة البيانات")
            cleanup_window.geometry("400x300")
            cleanup_window.transient(self.root)
            cleanup_window.grab_set()

            ttk.Label(cleanup_window, text="خيارات التنظيف:", font=("Arial", 12, "bold")).pack(pady=10)

            # خيارات التنظيف
            self.cleanup_old_logs = tk.BooleanVar()
            self.cleanup_old_backups = tk.BooleanVar()
            self.cleanup_duplicates = tk.BooleanVar()

            ttk.Checkbutton(cleanup_window, text="حذف السجلات القديمة (أكثر من 30 يوم)",
                           variable=self.cleanup_old_logs).pack(anchor="w", padx=20, pady=5)
            ttk.Checkbutton(cleanup_window, text="حذف النسخ الاحتياطية القديمة (أكثر من 7 أيام)",
                           variable=self.cleanup_old_backups).pack(anchor="w", padx=20, pady=5)
            ttk.Checkbutton(cleanup_window, text="حذف السجلات المكررة",
                           variable=self.cleanup_duplicates).pack(anchor="w", padx=20, pady=5)

            # معلومات إضافية
            info_frame = ttk.LabelFrame(cleanup_window, text="معلومات", padding=10)
            info_frame.pack(fill=tk.X, padx=20, pady=10)

            # عرض إحصائيات قاعدة البيانات
            stats_text = self.get_database_stats()
            ttk.Label(info_frame, text=stats_text, justify=tk.LEFT).pack(anchor="w")

            # أزرار التحكم
            button_frame = ttk.Frame(cleanup_window)
            button_frame.pack(pady=20)

            ttk.Button(button_frame, text="تنظيف",
                      command=lambda: self.perform_cleanup(cleanup_window)).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="إلغاء", command=cleanup_window.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في نافذة التنظيف: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في التنظيف: {str(e)}")

    def get_database_stats(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()

            # عدد سجلات User Manager
            cursor.execute("SELECT COUNT(*) FROM user_manager_credentials")
            user_manager_count = cursor.fetchone()[0]

            # عدد سجلات Hotspot
            cursor.execute("SELECT COUNT(*) FROM hotspot_credentials")
            hotspot_count = cursor.fetchone()[0]

            # عدد سجلات العمليات
            cursor.execute("SELECT COUNT(*) FROM operation_log")
            log_count = cursor.fetchone()[0]

            # حجم قاعدة البيانات
            db_size = Path("mikrotik_cards.db").stat().st_size / 1024 / 1024  # MB

            conn.close()

            stats = f"""إحصائيات قاعدة البيانات:
• سجلات User Manager: {user_manager_count}
• سجلات Hotspot: {hotspot_count}
• سجلات العمليات: {log_count}
• حجم قاعدة البيانات: {db_size:.2f} MB"""

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return "خطأ في الحصول على الإحصائيات"

    def perform_cleanup(self, window):
        """تنفيذ عملية التنظيف"""
        try:
            cleaned_items = 0

            # إنشاء نسخة احتياطية قبل التنظيف
            backup_file = self.create_backup()
            if not backup_file:
                messagebox.showerror("خطأ", "فشل في إنشاء نسخة احتياطية. تم إلغاء التنظيف.")
                return

            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()

            # حذف السجلات القديمة
            if self.cleanup_old_logs.get():
                cutoff_date = datetime.now() - timedelta(days=30)
                cursor.execute("DELETE FROM operation_log WHERE timestamp < ?", (cutoff_date,))
                deleted_logs = cursor.rowcount
                cleaned_items += deleted_logs
                self.logger.info(f"تم حذف {deleted_logs} سجل قديم")

            # حذف السجلات المكررة
            if self.cleanup_duplicates.get():
                # حذف المكررات في User Manager
                cursor.execute("""DELETE FROM user_manager_credentials
                                 WHERE id NOT IN (
                                     SELECT MIN(id) FROM user_manager_credentials
                                     GROUP BY username, password, profile
                                 )""")
                deleted_um = cursor.rowcount

                # حذف المكررات في Hotspot
                cursor.execute("""DELETE FROM hotspot_credentials
                                 WHERE id NOT IN (
                                     SELECT MIN(id) FROM hotspot_credentials
                                     GROUP BY username, password, profile
                                 )""")
                deleted_hs = cursor.rowcount

                cleaned_items += deleted_um + deleted_hs
                self.logger.info(f"تم حذف {deleted_um + deleted_hs} سجل مكرر")

            conn.commit()
            conn.close()

            # حذف النسخ الاحتياطية القديمة
            if self.cleanup_old_backups.get():
                deleted_backups = self.cleanup_old_backup_files()
                cleaned_items += deleted_backups

            # تحسين قاعدة البيانات
            self.optimize_database()

            window.destroy()

            messagebox.showinfo("نجح", f"تم تنظيف قاعدة البيانات بنجاح\nتم حذف {cleaned_items} عنصر")
            self.log_operation("تنظيف قاعدة البيانات", f"تم حذف {cleaned_items} عنصر", "نجح")

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ التنظيف: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في التنظيف: {str(e)}")

    def cleanup_old_backup_files(self):
        """حذف ملفات النسخ الاحتياطية القديمة"""
        try:
            backup_dir = Path("backups")
            if not backup_dir.exists():
                return 0

            cutoff_date = datetime.now() - timedelta(days=7)
            deleted_count = 0

            for backup_file in backup_dir.glob("*.db"):
                if backup_file.stat().st_mtime < cutoff_date.timestamp():
                    backup_file.unlink()
                    deleted_count += 1
                    self.logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup_file}")

            return deleted_count

        except Exception as e:
            self.logger.error(f"خطأ في حذف النسخ الاحتياطية القديمة: {str(e)}")
            return 0

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()

            # تنفيذ VACUUM لتحسين قاعدة البيانات
            cursor.execute("VACUUM")

            # إعادة تحليل الإحصائيات
            cursor.execute("ANALYZE")

            conn.close()
            self.logger.info("تم تحسين قاعدة البيانات")

        except Exception as e:
            self.logger.error(f"خطأ في تحسين قاعدة البيانات: {str(e)}")

    def show_logs(self):
        """عرض سجلات العمليات"""
        try:
            logs_window = tk.Toplevel(self.root)
            logs_window.title("سجلات العمليات")
            logs_window.geometry("800x600")
            logs_window.transient(self.root)

            # إنشاء Treeview لعرض السجلات
            columns = ("الوقت", "نوع العملية", "الوصف", "الحالة")
            logs_tree = ttk.Treeview(logs_window, columns=columns, show="headings")

            for col in columns:
                logs_tree.heading(col, text=col)
                logs_tree.column(col, width=150)

            # إضافة شريط التمرير
            scrollbar = ttk.Scrollbar(logs_window, orient="vertical", command=logs_tree.yview)
            logs_tree.configure(yscrollcommand=scrollbar.set)

            # تحميل البيانات
            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()
            cursor.execute("SELECT timestamp, operation_type, description, status FROM operation_log ORDER BY timestamp DESC LIMIT 1000")
            logs = cursor.fetchall()
            conn.close()

            for log in logs:
                logs_tree.insert("", "end", values=log)

            logs_tree.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # إضافة أزرار التحكم
            button_frame = ttk.Frame(logs_window)
            button_frame.pack(side="bottom", fill="x", padx=10, pady=5)

            ttk.Button(button_frame, text="تحديث", command=lambda: self.refresh_logs(logs_tree)).pack(side="left", padx=5)
            ttk.Button(button_frame, text="تصدير السجلات", command=self.export_logs).pack(side="left", padx=5)
            ttk.Button(button_frame, text="إغلاق", command=logs_window.destroy).pack(side="right", padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في عرض السجلات: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في عرض السجلات: {str(e)}")

    def refresh_logs(self, tree):
        """تحديث عرض السجلات"""
        try:
            # مسح البيانات الحالية
            for item in tree.get_children():
                tree.delete(item)

            # تحميل البيانات الجديدة
            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()
            cursor.execute("SELECT timestamp, operation_type, description, status FROM operation_log ORDER BY timestamp DESC LIMIT 1000")
            logs = cursor.fetchall()
            conn.close()

            for log in logs:
                tree.insert("", "end", values=log)

        except Exception as e:
            self.logger.error(f"خطأ في تحديث السجلات: {str(e)}")

    def export_logs(self):
        """تصدير السجلات"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialfile=f"operation_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )

            if not filename:
                return

            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM operation_log ORDER BY timestamp DESC")
            logs = cursor.fetchall()

            # الحصول على أسماء الأعمدة
            columns = [description[0] for description in cursor.description]
            conn.close()

            import csv
            with open(filename, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow(columns)
                writer.writerows(logs)

            messagebox.showinfo("نجح", f"تم تصدير {len(logs)} سجل بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير السجلات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير السجلات: {str(e)}")

    def setup_gui(self):
        self.main_canvas = tk.Canvas(self.root)
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.main_canvas.yview)
        self.main_frame = ttk.Frame(self.main_canvas)

        self.main_frame.bind("<Configure>", lambda e: self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all")))
        self.main_canvas.create_window((0, 0), window=self.main_frame, anchor="nw")
        self.main_canvas.configure(yscrollcommand=self.scrollbar.set)

        self.main_canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        self.main_canvas.bind_all("<MouseWheel>", self._on_mousewheel)

        status_frame = ttk.Frame(self.main_frame)
        status_frame.pack(fill=tk.X, pady=5)
        self.connection_status_label = ttk.Label(status_frame, textvariable=self.connection_status_var, style="Disconnected.TLabel")
        self.connection_status_label.pack(side=tk.LEFT, padx=5)
        self.create_styled_button(status_frame, "اختبار الاتصال",
                                 command=self.test_api_connection,
                                 icon="test", style="Warning.TButton").pack(side=tk.LEFT, padx=5)

        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        if self.system_type == 'user_manager':
            self.setup_user_manager_tabs()
        elif self.system_type == 'hotspot':
            self.setup_hotspot_tabs()

    def _on_mousewheel(self, event):
        self.main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def setup_user_manager_tabs(self):
        self.generation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.generation_frame, text="إعدادات التوليد")
        self.setup_user_manager_generation_tab()

        self.pdf_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.pdf_frame, text="إعدادات PDF")
        self.setup_pdf_tab()

        # إضافة تبويب عرض الكروت لـ User Manager
        self.user_manager_cards_view_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.user_manager_cards_view_frame, text="عرض الكروت")
        self.setup_user_manager_cards_view_tab()

    def setup_hotspot_tabs(self):
        self.mikrotik_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.mikrotik_frame, text="إعدادات MikroTik Hotspot")
        self.setup_hotspot_mikrotik_tab()

        self.pdf_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.pdf_frame, text="إعدادات PDF")
        self.setup_pdf_tab()

        # إضافة تبويب عرض الكروت لـ Hotspot
        self.hotspot_cards_view_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.hotspot_cards_view_frame, text="عرض الكروت")
        self.setup_hotspot_cards_view_tab()

    def setup_user_manager_generation_tab(self):
        # إطار موحد لإعدادات API و MikroTik
        unified_frame = ttk.LabelFrame(self.generation_frame, text="إعدادات API و MikroTik", padding=10)
        unified_frame.pack(fill=tk.X, pady=5)

        # الصف الأول: إعدادات API
        ttk.Label(unified_frame, text="Host (IP or DDNS):").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.api_ip_entry = ttk.Entry(unified_frame, width=12)
        self.api_ip_entry.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(unified_frame, text="اسم المستخدم:").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.api_username_entry = ttk.Entry(unified_frame, width=12)
        self.api_username_entry.grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(unified_frame, text="كلمة المرور:").grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.api_password_entry = ttk.Entry(unified_frame, width=12, show="*")
        self.api_password_entry.grid(row=0, column=5, padx=5, pady=5)

        ttk.Label(unified_frame, text="المنفذ:").grid(row=0, column=6, padx=5, pady=5, sticky="e")
        self.api_port_entry = ttk.Entry(unified_frame, width=8)
        self.api_port_entry.grid(row=0, column=7, padx=5, pady=5)

        # الصف الثاني: إعدادات MikroTik الأساسية
        ttk.Label(unified_frame, text="العميل:").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.customer_entry = ttk.Entry(unified_frame, width=12)
        self.customer_entry.grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(unified_frame, text="إصدار MikroTik:").grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.version_combo = ttk.Combobox(unified_frame, values=["v6", "v7"], state="readonly", width=10)
        self.version_combo.grid(row=1, column=3, padx=5, pady=5)

        ttk.Label(unified_frame, text="البروفايل:").grid(row=1, column=4, padx=5, pady=5, sticky="e")
        self.profile_combo = ttk.Combobox(unified_frame, values=self.profiles, width=12)
        self.profile_combo.grid(row=1, column=5, padx=5, pady=5)
        self.profile_combo.bind("<KeyRelease>", self.on_profile_typing)

        # زر جلب البروفايلات بجوار البروفايل
        self.fetch_profiles_button = self.create_styled_button(unified_frame, "جلب",
                                                              command=self.fetch_profiles,
                                                              icon="refresh", style="Primary.TButton")
        self.fetch_profiles_button.grid(row=1, column=6, padx=5, pady=5)
        if not ROUTEROS_AVAILABLE:
            self.fetch_profiles_button.configure(state="disabled")

        # خيار caller-id-bind
        ttk.Checkbutton(unified_frame, text="caller-id-bind-on-first-use", variable=self.caller_id_bind_var).grid(row=1, column=7, padx=5, pady=5, sticky="w")

        # الصف الثالث: الأزرار
        buttons_frame = ttk.Frame(unified_frame)
        buttons_frame.grid(row=2, column=0, columnspan=8, pady=10)

        self.test_api_button = self.create_styled_button(buttons_frame, "اختبار الاتصال",
                                                        command=self.test_api_connection,
                                                        icon="test", style="Warning.TButton")
        self.test_api_button.pack(side=tk.LEFT, padx=5)

        self.send_mikrotik_button = self.create_styled_button(buttons_frame, "إرسال إلى MikroTik",
                                                             command=self.send_to_mikrotik_with_pdf,
                                                             icon="network", style="Success.TButton")
        self.send_mikrotik_button.pack(side=tk.LEFT, padx=5)

        if not ROUTEROS_AVAILABLE:
            self.test_api_button.configure(state="disabled")
            self.send_mikrotik_button.configure(state="disabled")

        # إطار الإعدادات الإضافية (يشمل الآن توليد الحسابات)
        additional_frame = ttk.LabelFrame(self.generation_frame, text="الإعدادات الإضافية وتوليد الحسابات", padding=10)
        additional_frame.pack(fill=tk.X, pady=5)

        ttk.Label(additional_frame, text="التعليق:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.comment_entry = ttk.Entry(additional_frame, width=12)
        self.comment_entry.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(additional_frame, text="الموقع:").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.location_entry = ttk.Entry(additional_frame, width=12)
        self.location_entry.grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(additional_frame, text="البريد الإلكتروني:").grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.email_entry = ttk.Entry(additional_frame, textvariable=self.user_email, width=12)
        self.email_entry.grid(row=0, column=5, padx=5, pady=5)

        ttk.Label(additional_frame, text="السعر:").grid(row=0, column=6, padx=5, pady=5, sticky="e")
        self.price_entry = ttk.Entry(additional_frame, width=8)
        self.price_entry.grid(row=0, column=7, padx=5, pady=5)

        # الصف الثاني: إعدادات توليد الحسابات
        ttk.Label(additional_frame, text="نوع الاسم/كلمة السر:").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.cred_type_combo = ttk.Combobox(additional_frame, values=["أرقام وحروف", "أرقام فقط", "حروف فقط"], state="readonly", width=10)
        self.cred_type_combo.grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(additional_frame, text="تطابق الاسم/كلمة السر:").grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.cred_match_combo = ttk.Combobox(additional_frame, values=["مختلفة", "متشابهة", "كلمة سر فارغة"], state="readonly", width=10)
        self.cred_match_combo.grid(row=1, column=3, padx=5, pady=5)
        self.cred_match_combo.bind("<<ComboboxSelected>>", self.toggle_pass_suffix)

        ttk.Label(additional_frame, text="طول الكود:").grid(row=1, column=4, padx=5, pady=5, sticky="e")
        self.length_entry = ttk.Entry(additional_frame, width=8)
        self.length_entry.grid(row=1, column=5, padx=5, pady=5)

        ttk.Label(additional_frame, text="عدد الحسابات:").grid(row=1, column=6, padx=5, pady=5, sticky="e")
        self.count_entry = ttk.Entry(additional_frame, width=8)
        self.count_entry.grid(row=1, column=7, padx=5, pady=5)

        # الصف الثالث: إعدادات إضافية للتوليد
        ttk.Label(additional_frame, text="البادئة:").grid(row=2, column=0, padx=5, pady=5, sticky="e")
        self.prefix_entry = ttk.Entry(additional_frame, width=10)
        self.prefix_entry.grid(row=2, column=1, padx=5, pady=5)

        ttk.Label(additional_frame, text="نهاية الاسم:").grid(row=2, column=2, padx=5, pady=5, sticky="e")
        self.suffix_entry = ttk.Entry(additional_frame, width=10)
        self.suffix_entry.grid(row=2, column=3, padx=5, pady=5)

        self.pass_suffix_label = ttk.Label(additional_frame, text="نهاية كلمة السر:")
        self.pass_suffix_label.grid(row=2, column=4, padx=5, pady=5, sticky="e")
        self.pass_suffix_entry = ttk.Entry(additional_frame, width=10)
        self.pass_suffix_entry.grid(row=2, column=5, padx=5, pady=5)

        ttk.Label(additional_frame, text="تأخير السكريبت (ms):").grid(row=2, column=6, padx=5, pady=5, sticky="e")
        self.delay_entry = ttk.Entry(additional_frame, width=8)
        self.delay_entry.grid(row=2, column=7, padx=5, pady=5)

        # إضافة إطار القوالب
        template_frame = ttk.LabelFrame(additional_frame, text="القوالب", padding=5)
        template_frame.grid(row=3, column=0, columnspan=8, sticky="ew", padx=5, pady=5)

        self.template_combo = ttk.Combobox(template_frame, width=20)
        self.template_combo.pack(side=tk.LEFT, padx=5, pady=5)
        self.template_combo.bind("<<ComboboxSelected>>", self.load_template)

        self.create_styled_button(template_frame, "حفظ القالب",
                                 command=self.save_template,
                                 icon="save", style="Primary.TButton").pack(side=tk.LEFT, padx=5, pady=5)
        self.create_styled_button(template_frame, "حذف القالب",
                                 command=self.delete_template,
                                 icon="delete", style="Error.TButton").pack(side=tk.LEFT, padx=5, pady=5)



        # إضافة زر لعرض عدد الكروت فقط
        button_frame = ttk.Frame(self.generation_frame)
        button_frame.pack(pady=5)
        self.show_user_count_button = ttk.Button(button_frame, text="عرض عدد الكروت", command=self.fetch_user_count)
        self.show_user_count_button.pack(side=tk.LEFT, padx=5)
        if not ROUTEROS_AVAILABLE:
            self.show_user_count_button.configure(state="disabled")

        # إضافة منطقة السكريبت والتوليد إلى تبويب User Manager
        script_frame = ttk.LabelFrame(self.generation_frame, text="السكريبت المولد والتحكم", padding=5)
        script_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(script_frame, text="السكريبت المولد:").pack(anchor="w")
        self.output_text = tk.Text(script_frame, height=4, width=10)
        self.output_text.pack(fill=tk.BOTH, expand=True, pady=5)

        self.progress_bar = ttk.Progressbar(script_frame, orient="horizontal", length=300, mode="determinate", variable=self.progress_var)
        self.progress_bar.pack(pady=5)

        generation_button_frame = ttk.Frame(script_frame)
        generation_button_frame.pack(pady=5)

        self.create_styled_button(generation_button_frame, "توليد الحسابات",
                                 command=self.generate_all,
                                 icon="generate", style="Primary.TButton").pack(side=tk.LEFT, padx=5)

        # أزرار الحفظ بجوار توليد الحسابات
        self.create_styled_button(generation_button_frame, "حفظ كنص",
                                 command=self.save_as_txt,
                                 icon="save", style="Primary.TButton").pack(side=tk.LEFT, padx=5)
        self.create_styled_button(generation_button_frame, "حفظ سكريبت MikroTik",
                                 command=self.save_as_mikrotik,
                                 icon="script", style="Primary.TButton").pack(side=tk.LEFT, padx=5)
        self.create_styled_button(generation_button_frame, "حفظ كـ PDF",
                                 command=self.save_as_pdf,
                                 icon="pdf", style="Error.TButton").pack(side=tk.LEFT, padx=5)

        self.create_styled_button(generation_button_frame, "توليد سريع جداً",
                                 command=self.generate_very_fast,
                                 icon="generate", style="Success.TButton").pack(side=tk.LEFT, padx=5)

        # إضافة إطار للخيارات الإضافية للسريع جدا
        fast_options_frame = ttk.Frame(generation_button_frame)
        fast_options_frame.pack(side=tk.LEFT, padx=5)

        # إضافة خيار تشغيل سكربت قبل التنظيف
        ttk.Checkbutton(fast_options_frame, text="تشغيل سكربت قبل التنظيف", variable=self.run_script_before_cleanup_var).pack(side=tk.LEFT)
        self.script_to_run_entry = ttk.Entry(fast_options_frame, width=15)
        self.script_to_run_entry.pack(side=tk.LEFT, padx=5)
        self.script_to_run_entry.insert(0, "اسم_السكربت")

        ttk.Button(generation_button_frame, text="السريع جدا", command=self.generate_and_send_fast_with_pdf).pack(side=tk.LEFT, padx=5)



    def setup_hotspot_mikrotik_tab(self):
        # إطار موحد لإعدادات API و MikroTik Hotspot
        unified_settings_frame = ttk.LabelFrame(self.mikrotik_frame, text="إعدادات API و MikroTik Hotspot", padding=10)
        unified_settings_frame.pack(fill=tk.X, pady=5)

        # الصف الأول: إعدادات API
        ttk.Label(unified_settings_frame, text="Host (IP or DDNS):").grid(row=0, column=0, padx=5, pady=2, sticky="e")
        self.api_ip_entry = ttk.Entry(unified_settings_frame, width=12)
        self.api_ip_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(unified_settings_frame, text="اسم المستخدم:").grid(row=0, column=2, padx=5, pady=2, sticky="e")
        self.api_username_entry = ttk.Entry(unified_settings_frame, width=12)
        self.api_username_entry.grid(row=0, column=3, padx=5, pady=2)

        ttk.Label(unified_settings_frame, text="كلمة المرور:").grid(row=0, column=4, padx=5, pady=2, sticky="e")
        self.api_password_entry = ttk.Entry(unified_settings_frame, width=12, show="*")
        self.api_password_entry.grid(row=0, column=5, padx=5, pady=2)

        ttk.Label(unified_settings_frame, text="المنفذ:").grid(row=0, column=6, padx=5, pady=2, sticky="e")
        self.api_port_entry = ttk.Entry(unified_settings_frame, width=8)
        self.api_port_entry.grid(row=0, column=7, padx=5, pady=2)

        # الصف الثاني: إعدادات MikroTik الأساسية
        ttk.Label(unified_settings_frame, text="اسم السيرفر:").grid(row=1, column=0, padx=5, pady=2, sticky="e")
        self.server_entry = ttk.Entry(unified_settings_frame, width=12)
        self.server_entry.grid(row=1, column=1, padx=5, pady=2)

        ttk.Label(unified_settings_frame, text="إصدار MikroTik:").grid(row=1, column=2, padx=5, pady=2, sticky="e")
        self.version_combo = ttk.Combobox(unified_settings_frame, values=["v6", "v7"], state="readonly", width=10)
        self.version_combo.grid(row=1, column=3, padx=5, pady=2)

        ttk.Label(unified_settings_frame, text="البروفايل:").grid(row=1, column=4, padx=5, pady=2, sticky="e")
        self.profile_combo = ttk.Combobox(unified_settings_frame, values=self.profiles, width=12)
        self.profile_combo.grid(row=1, column=5, padx=5, pady=2)
        self.profile_combo.bind("<KeyRelease>", self.on_profile_typing)

        # زر جلب البروفايلات بجوار البروفايل
        self.fetch_profiles_button = ttk.Button(unified_settings_frame, text="جلب البروفيلات", command=self.fetch_profiles)
        self.fetch_profiles_button.grid(row=1, column=6, padx=5, pady=2)
        if not ROUTEROS_AVAILABLE:
            self.fetch_profiles_button.configure(state="disabled")

        # الصف الثالث: الأزرار
        buttons_frame = ttk.Frame(unified_settings_frame)
        buttons_frame.grid(row=2, column=0, columnspan=8, pady=5)

        self.test_api_button = ttk.Button(buttons_frame, text="اختبار الاتصال", command=self.test_api_connection)
        self.test_api_button.pack(side=tk.LEFT, padx=5)

        self.send_mikrotik_button = ttk.Button(buttons_frame, text="إرسال إلى MikroTik", command=self.send_to_mikrotik_with_pdf)
        self.send_mikrotik_button.pack(side=tk.LEFT, padx=5)

        if not ROUTEROS_AVAILABLE:
            self.test_api_button.configure(state="disabled")
            self.send_mikrotik_button.configure(state="disabled")

        # إطار الإعدادات الإضافية (يشمل الآن توليد الحسابات)
        additional_frame = ttk.LabelFrame(self.mikrotik_frame, text="الإعدادات الإضافية وتوليد الحسابات", padding=10)
        additional_frame.pack(fill=tk.X, pady=5)

        # الصف الأول: الإعدادات الأساسية
        ttk.Label(additional_frame, text="التعليق:").grid(row=0, column=0, padx=5, pady=2, sticky="e")
        self.comment_entry = ttk.Entry(additional_frame, width=10)
        self.comment_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(additional_frame, text="الموقع:").grid(row=0, column=2, padx=5, pady=2, sticky="e")
        self.location_entry = ttk.Entry(additional_frame, width=10)
        self.location_entry.grid(row=0, column=3, padx=5, pady=2)

        ttk.Label(additional_frame, text="السعر:").grid(row=0, column=4, padx=5, pady=2, sticky="e")
        self.price_entry = ttk.Entry(additional_frame, width=8)
        self.price_entry.grid(row=0, column=5, padx=5, pady=2)

        ttk.Label(additional_frame, text="نموذج الإيميل:").grid(row=0, column=6, padx=5, pady=2, sticky="e")
        self.email_template_entry = ttk.Entry(additional_frame, width=10)
        self.email_template_entry.grid(row=0, column=7, padx=5, pady=2)

        # الصف الثاني: إعدادات الحدود والأيام
        ttk.Label(additional_frame, text="الحد الأقصى للبيانات:").grid(row=1, column=0, padx=5, pady=2, sticky="e")
        self.limit_bytes_entry = ttk.Entry(additional_frame, width=8)
        self.limit_bytes_entry.grid(row=1, column=1, padx=5, pady=2)

        self.limit_unit_combo = ttk.Combobox(additional_frame, values=["MB", "GB"], state="readonly", width=5)
        self.limit_unit_combo.grid(row=1, column=2, padx=5, pady=2)
        self.limit_unit_combo.set("GB")

        ttk.Label(additional_frame, text="عدد الأيام:").grid(row=1, column=3, padx=5, pady=2, sticky="e")
        self.days_entry = ttk.Entry(additional_frame, width=8)
        self.days_entry.grid(row=1, column=4, padx=5, pady=2)

        # الصف الثالث: إعدادات توليد الحسابات
        ttk.Label(additional_frame, text="نوع الاسم/كلمة السر:").grid(row=2, column=0, padx=5, pady=2, sticky="e")
        self.cred_type_combo = ttk.Combobox(additional_frame, values=["أرقام وحروف", "أرقام فقط", "حروف فقط"], state="readonly", width=10)
        self.cred_type_combo.grid(row=2, column=1, padx=5, pady=2)

        ttk.Label(additional_frame, text="تطابق الاسم/كلمة السر:").grid(row=2, column=2, padx=5, pady=2, sticky="e")
        self.cred_match_combo = ttk.Combobox(additional_frame, values=["مختلفة", "متشابهة", "كلمة سر فارغة"], state="readonly", width=10)
        self.cred_match_combo.grid(row=2, column=3, padx=5, pady=2)
        self.cred_match_combo.bind("<<ComboboxSelected>>", self.toggle_pass_suffix)

        ttk.Label(additional_frame, text="طول الكود:").grid(row=2, column=4, padx=5, pady=2, sticky="e")
        self.length_entry = ttk.Entry(additional_frame, width=8)
        self.length_entry.grid(row=2, column=5, padx=5, pady=2)

        ttk.Label(additional_frame, text="عدد الحسابات:").grid(row=2, column=6, padx=5, pady=2, sticky="e")
        self.count_entry = ttk.Entry(additional_frame, width=8)
        self.count_entry.grid(row=2, column=7, padx=5, pady=2)

        # الصف الرابع: إعدادات إضافية للتوليد
        ttk.Label(additional_frame, text="البادئة:").grid(row=3, column=0, padx=5, pady=2, sticky="e")
        self.prefix_entry = ttk.Entry(additional_frame, width=10)
        self.prefix_entry.grid(row=3, column=1, padx=5, pady=2)

        ttk.Label(additional_frame, text="نهاية الاسم:").grid(row=3, column=2, padx=5, pady=2, sticky="e")
        self.suffix_entry = ttk.Entry(additional_frame, width=10)
        self.suffix_entry.grid(row=3, column=3, padx=5, pady=2)

        self.pass_suffix_label = ttk.Label(additional_frame, text="نهاية كلمة السر:")
        self.pass_suffix_label.grid(row=3, column=4, padx=5, pady=2, sticky="e")
        self.pass_suffix_entry = ttk.Entry(additional_frame, width=10)
        self.pass_suffix_entry.grid(row=3, column=5, padx=5, pady=2)

        ttk.Label(additional_frame, text="تأخير السكريبت (ms):").grid(row=3, column=6, padx=5, pady=2, sticky="e")
        self.delay_entry = ttk.Entry(additional_frame, width=8)
        self.delay_entry.grid(row=3, column=7, padx=5, pady=2)

        # Templates
        template_frame = ttk.LabelFrame(additional_frame, text="القوالب", padding=5)
        template_frame.grid(row=4, column=0, columnspan=8, sticky="ew", padx=5, pady=5)

        self.template_combo = ttk.Combobox(template_frame, width=20)
        self.template_combo.pack(side=tk.LEFT, padx=5, pady=5)
        self.template_combo.bind("<<ComboboxSelected>>", self.load_template)

        ttk.Button(template_frame, text="حفظ القالب", command=self.save_template).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(template_frame, text="حذف القالب", command=self.delete_template).pack(side=tk.LEFT, padx=5, pady=5)



        # إضافة منطقة السكريبت والتوليد إلى تبويب Hotspot
        script_frame = ttk.LabelFrame(self.mikrotik_frame, text="السكريبت المولد والتحكم", padding=5)
        script_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(script_frame, text="السكريبت المولد:").pack(anchor="w")
        self.output_text = tk.Text(script_frame, height=4, width=10)
        self.output_text.pack(fill=tk.BOTH, expand=True, pady=5)

        self.progress_bar = ttk.Progressbar(script_frame, orient="horizontal", length=300, mode="determinate", variable=self.progress_var)
        self.progress_bar.pack(pady=5)

        generation_button_frame = ttk.Frame(script_frame)
        generation_button_frame.pack(pady=5)

        # الصف الأول من الأزرار - التوليد الأساسي وأزرار الحفظ
        basic_gen_frame = ttk.Frame(generation_button_frame)
        basic_gen_frame.pack(pady=2)

        ttk.Button(basic_gen_frame, text="📝 توليد الحسابات", command=self.generate_all).pack(side=tk.LEFT, padx=5)

        # زر التوليد السريع بجانب توليد الحسابات مباشرة (فقط في نظام Hotspot)
        ttk.Button(basic_gen_frame, text="⚡ التوليد السريع", command=self.generate_hotspot_fast_only).pack(side=tk.LEFT, padx=5)

        # زر إرسال الكروت كسكربت بجانب التوليد السريع (فقط في نظام Hotspot)
        self.send_as_script_button = ttk.Button(basic_gen_frame, text="📤 إرسال الكروت كسكربت",
                                               command=self.send_cards_as_script)
        self.send_as_script_button.pack(side=tk.LEFT, padx=5)
        # الزر فعال دائماً في نظام Hotspot فقط
        self.send_as_script_button.configure(state="normal")

        # أزرار الحفظ بجوار توليد الحسابات
        ttk.Button(basic_gen_frame, text="💾 حفظ كنص", command=self.save_as_txt).pack(side=tk.LEFT, padx=5)
        ttk.Button(basic_gen_frame, text="📜 حفظ سكريبت MikroTik", command=self.save_as_mikrotik).pack(side=tk.LEFT, padx=5)
        ttk.Button(basic_gen_frame, text="📄 حفظ كـ PDF", command=self.save_as_pdf).pack(side=tk.LEFT, padx=5)


        # الصف الثاني من الأزرار - الوضع السريع المحسن
        fast_mode_frame = ttk.Frame(generation_button_frame)
        fast_mode_frame.pack(pady=2)





        # إطار الخيارات الإضافية للوضع السريع
        fast_options_frame = ttk.LabelFrame(generation_button_frame, text="⚙️ خيارات الوضع السريع", padding=5)
        fast_options_frame.pack(fill=tk.X, pady=5)

        # خيار تشغيل سكربت قبل التنظيف
        cleanup_frame = ttk.Frame(fast_options_frame)
        cleanup_frame.pack(fill=tk.X, pady=2)

        ttk.Checkbutton(cleanup_frame, text="تشغيل سكربت قبل التنظيف:",
                       variable=self.run_script_before_cleanup_var).pack(side=tk.LEFT)
        self.script_to_run_entry = ttk.Entry(cleanup_frame, width=20)
        self.script_to_run_entry.pack(side=tk.LEFT, padx=5)
        self.script_to_run_entry.insert(0, "اسم_السكربت")

        # معلومات الوضع السريع
        info_frame = ttk.Frame(fast_options_frame)
        info_frame.pack(fill=tk.X, pady=2)

        info_label = ttk.Label(info_frame,
                              text="💡 الوضع السريع: يستخدم arrays محسنة لإضافة المستخدمين بسرعة عالية مع دعم كامل لجميع إعدادات Hotspot",
                              font=('Arial', 8), foreground='blue')
        info_label.pack(side=tk.LEFT)



    def setup_pdf_tab(self):
        pdf_settings_frame = ttk.LabelFrame(self.pdf_frame, text="إعدادات PDF", padding=10)
        pdf_settings_frame.pack(fill=tk.X, pady=5)

        ttk.Button(pdf_settings_frame, text="رفع صورة الخلفية", command=self.load_background_image).grid(row=0, column=0, padx=5, pady=5)

        ttk.Label(pdf_settings_frame, text="الأعمدة:").grid(row=0, column=1, padx=5, pady=5, sticky="e")
        self.columns_entry = ttk.Entry(pdf_settings_frame, width=10)
        self.columns_entry.grid(row=0, column=2, padx=5, pady=5)
        self.columns_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(pdf_settings_frame, text="الصفوف:").grid(row=0, column=3, padx=5, pady=5, sticky="e")
        self.rows_entry = ttk.Entry(pdf_settings_frame, width=10)
        self.rows_entry.grid(row=0, column=4, padx=5, pady=5)
        self.rows_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(pdf_settings_frame, text="المسافة (mm):").grid(row=0, column=5, padx=5, pady=5, sticky="e")
        self.spacing_entry = ttk.Entry(pdf_settings_frame, width=10)
        self.spacing_entry.grid(row=0, column=6, padx=5, pady=5)
        self.spacing_entry.bind("<KeyRelease>", self.update_preview_event)

        self.print_username_var = tk.BooleanVar()
        ttk.Checkbutton(pdf_settings_frame, text="طباعة اسم المستخدم", variable=self.print_username_var, command=self.toggle_username_inputs).grid(row=1, column=0, columnspan=2, padx=5, pady=5)

        self.print_password_var = tk.BooleanVar()
        ttk.Checkbutton(pdf_settings_frame, text="طباعة كلمة السر", variable=self.print_password_var, command=self.toggle_password_inputs).grid(row=1, column=2, columnspan=2, padx=5, pady=5)

        self.use_serial_var = tk.BooleanVar()
        ttk.Checkbutton(pdf_settings_frame, text="طباعة رقم تسلسلي", variable=self.use_serial_var, command=self.toggle_serial_inputs).grid(row=1, column=4, columnspan=2, padx=5, pady=5)

        self.use_date_var = tk.BooleanVar()
        ttk.Checkbutton(pdf_settings_frame, text="طباعة التاريخ", variable=self.use_date_var, command=self.toggle_date_inputs).grid(row=1, column=6, columnspan=2, padx=5, pady=5)

        self.use_qr_var = tk.BooleanVar()
        ttk.Checkbutton(pdf_settings_frame, text="طباعة رمز QR", variable=self.use_qr_var, command=self.toggle_qr_inputs).grid(row=2, column=0, columnspan=2, padx=5, pady=5)

        self.use_price_var = tk.BooleanVar()
        ttk.Checkbutton(pdf_settings_frame, text="طباعة السعر", variable=self.use_price_var, command=self.toggle_price_inputs).grid(row=2, column=2, columnspan=2, padx=5, pady=5)

        self.username_frame = ttk.LabelFrame(pdf_settings_frame, text="إعدادات نص اسم المستخدم", padding=10)
        self.username_frame.grid(row=3, column=0, columnspan=4, padx=5, pady=5, sticky="ew")

        ttk.Label(self.username_frame, text="حجم النص (pt):").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.username_size_entry = ttk.Entry(self.username_frame, width=10)
        self.username_size_entry.grid(row=0, column=1, padx=5, pady=5)
        self.username_size_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.username_frame, text="لون النص:").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.username_color_entry = ttk.Entry(self.username_frame, width=10)
        self.username_color_entry.grid(row=0, column=3, padx=5, pady=5)
        self.username_color_entry.bind("<KeyRelease>", self.update_preview_event)

        self.username_bold_var = tk.BooleanVar()
        ttk.Checkbutton(self.username_frame, text="نص غامق", variable=self.username_bold_var, command=self.update_preview).grid(row=0, column=4, padx=5, pady=5)

        ttk.Label(self.username_frame, text="موضع أفقي (mm):").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.username_x_entry = ttk.Entry(self.username_frame, width=10)
        self.username_x_entry.grid(row=1, column=1, padx=5, pady=5)
        self.username_x_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.username_frame, text="موضع عمودي (mm):").grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.username_y_entry = ttk.Entry(self.username_frame, width=10)
        self.username_y_entry.grid(row=1, column=3, padx=5, pady=5)
        self.username_y_entry.bind("<KeyRelease>", self.update_preview_event)

        self.password_frame = ttk.LabelFrame(pdf_settings_frame, text="إعدادات نص كلمة السر", padding=10)
        self.password_frame.grid(row=3, column=4, columnspan=4, padx=5, pady=5, sticky="ew")

        ttk.Label(self.password_frame, text="حجم النص (pt):").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.password_size_entry = ttk.Entry(self.password_frame, width=10)
        self.password_size_entry.grid(row=0, column=1, padx=5, pady=5)
        self.password_size_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.password_frame, text="لون النص:").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.password_color_entry = ttk.Entry(self.password_frame, width=10)
        self.password_color_entry.grid(row=0, column=3, padx=5, pady=5)
        self.password_color_entry.bind("<KeyRelease>", self.update_preview_event)

        self.password_bold_var = tk.BooleanVar()
        ttk.Checkbutton(self.password_frame, text="نص غامق", variable=self.password_bold_var, command=self.update_preview).grid(row=0, column=4, padx=5, pady=5)

        ttk.Label(self.password_frame, text="موضع أفقي (mm):").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.password_x_entry = ttk.Entry(self.password_frame, width=10)
        self.password_x_entry.grid(row=1, column=1, padx=5, pady=5)
        self.password_x_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.password_frame, text="موضع عمودي (mm):").grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.password_y_entry = ttk.Entry(self.password_frame, width=10)
        self.password_y_entry.grid(row=1, column=3, padx=5, pady=5)
        self.password_y_entry.bind("<KeyRelease>", self.update_preview_event)

        self.serial_frame = ttk.LabelFrame(pdf_settings_frame, text="إعدادات الرقم التسلسلي", padding=10)
        self.serial_frame.grid(row=4, column=0, columnspan=4, padx=5, pady=5, sticky="ew")
        self.serial_frame.grid_remove()

        ttk.Label(self.serial_frame, text="يبدأ من (اختياري):").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.serial_start_entry = ttk.Entry(self.serial_frame, width=10)
        self.serial_start_entry.grid(row=0, column=1, padx=5, pady=5)
        self.serial_start_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.serial_frame, text="حجم النص (pt):").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.serial_size_entry = ttk.Entry(self.serial_frame, width=10)
        self.serial_size_entry.grid(row=0, column=3, padx=5, pady=5)
        self.serial_size_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.serial_frame, text="لون النص:").grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.serial_color_entry = ttk.Entry(self.serial_frame, width=10)
        self.serial_color_entry.grid(row=0, column=5, padx=5, pady=5)
        self.serial_color_entry.bind("<KeyRelease>", self.update_preview_event)

        self.serial_bold_var = tk.BooleanVar()
        ttk.Checkbutton(self.serial_frame, text="نص غامق", variable=self.serial_bold_var, command=self.update_preview).grid(row=0, column=6, padx=5, pady=5)

        ttk.Label(self.serial_frame, text="موضع أفقي (mm):").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.serial_x_entry = ttk.Entry(self.serial_frame, width=10)
        self.serial_x_entry.grid(row=1, column=1, padx=5, pady=5)
        self.serial_x_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.serial_frame, text="موضع عمودي (mm):").grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.serial_y_entry = ttk.Entry(self.serial_frame, width=10)
        self.serial_y_entry.grid(row=1, column=3, padx=5, pady=5)
        self.serial_y_entry.bind("<KeyRelease>", self.update_preview_event)

        self.date_frame = ttk.LabelFrame(pdf_settings_frame, text="إعدادات التاريخ", padding=10)
        self.date_frame.grid(row=4, column=4, columnspan=4, padx=5, pady=5, sticky="ew")
        self.date_frame.grid_remove()

        ttk.Label(self.date_frame, text="حجم النص (pt):").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.date_size_entry = ttk.Entry(self.date_frame, width=10)
        self.date_size_entry.grid(row=0, column=1, padx=5, pady=5)
        self.date_size_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.date_frame, text="لون النص:").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.date_color_entry = ttk.Entry(self.date_frame, width=10)
        self.date_color_entry.grid(row=0, column=3, padx=5, pady=5)
        self.date_color_entry.bind("<KeyRelease>", self.update_preview_event)

        self.date_bold_var = tk.BooleanVar()
        ttk.Checkbutton(self.date_frame, text="نص غامق", variable=self.date_bold_var, command=self.update_preview).grid(row=0, column=4, padx=5, pady=5)

        ttk.Label(self.date_frame, text="موضع أفقي (mm):").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.date_x_entry = ttk.Entry(self.date_frame, width=10)
        self.date_x_entry.grid(row=1, column=1, padx=5, pady=5)
        self.date_x_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.date_frame, text="موضع عمودي (mm):").grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.date_y_entry = ttk.Entry(self.date_frame, width=10)
        self.date_y_entry.grid(row=1, column=3, padx=5, pady=5)
        self.date_y_entry.bind("<KeyRelease>", self.update_preview_event)

        self.qr_frame = ttk.LabelFrame(pdf_settings_frame, text="إعدادات رمز QR", padding=10)
        self.qr_frame.grid(row=5, column=0, columnspan=4, padx=5, pady=5, sticky="ew")
        self.qr_frame.grid_remove()

        ttk.Label(self.qr_frame, text="حجم رمز QR (mm):").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.qr_size_entry = ttk.Entry(self.qr_frame, width=10)
        self.qr_size_entry.grid(row=0, column=1, padx=5, pady=5)
        self.qr_size_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.qr_frame, text="موضع أفقي (mm):").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.qr_x_entry = ttk.Entry(self.qr_frame, width=10)
        self.qr_x_entry.grid(row=0, column=3, padx=5, pady=5)
        self.qr_x_entry.bind("<KeyRelease>", self.update_preview_event)

        ttk.Label(self.qr_frame, text="موضع عمودي (mm):").grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.qr_y_entry = ttk.Entry(self.qr_frame, width=10)
        self.qr_y_entry.grid(row=0, column=5, padx=5, pady=5)
        self.qr_y_entry.bind("<KeyRelease>", self.update_preview_event)

        self.price_frame = ttk.LabelFrame(pdf_settings_frame, text="إعدادات نص السعر", padding=10)
        self.price_frame.grid(row=5, column=4, columnspan=4, padx=5, pady=5, sticky="ew")
        self.price_frame.grid_remove()

        ttk.Label(self.price_frame, text="السعر:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.price_value_entry = ttk.Entry(self.price_frame, width=10)
        self.price_value_entry.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(self.price_frame, text="حجم النص (pt):").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.price_size_entry = ttk.Entry(self.price_frame, width=10)
        self.price_size_entry.grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(self.price_frame, text="لون النص:").grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.price_color_entry = ttk.Entry(self.price_frame, width=10)
        self.price_color_entry.grid(row=0, column=5, padx=5, pady=5)

        self.price_bold_var = tk.BooleanVar()
        ttk.Checkbutton(self.price_frame, text="نص غامق", variable=self.price_bold_var).grid(row=0, column=6, padx=5, pady=5)

        ttk.Label(self.price_frame, text="موضع أفقي (mm):").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.price_x_entry = ttk.Entry(self.price_frame, width=10)
        self.price_x_entry.grid(row=1, column=1, padx=5, pady=5)

        ttk.Label(self.price_frame, text="موضع عمودي (mm):").grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.price_y_entry = ttk.Entry(self.price_frame, width=10)
        self.price_y_entry.grid(row=1, column=3, padx=5, pady=5)

        # إضافة معاينة الكرت إلى تبويب PDF
        preview_frame = ttk.LabelFrame(self.pdf_frame, text="معاينة الكرت", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(preview_frame, text="معاينة الكرت (اسحب العناصر لتغيير مواقعها):").pack()
        self.preview_canvas = tk.Canvas(preview_frame, width=300, height=150, bg="white", highlightthickness=1, highlightbackground="black")
        self.preview_canvas.pack(pady=5)

        self.preview_canvas.bind("<Button-1>", self.start_drag)
        self.preview_canvas.bind("<B1-Motion>", self.on_drag)
        self.preview_canvas.bind("<ButtonRelease-1>", self.stop_drag)

    def setup_user_manager_cards_view_tab(self):
        """إعداد تبويب عرض الكروت لـ User Manager"""
        try:
            # إطار رئيسي للتبويب
            main_frame = ttk.Frame(self.user_manager_cards_view_frame)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # إطار البحث والفلترة
            search_frame = ttk.LabelFrame(main_frame, text="🔍 البحث والفلترة المتقدمة", padding=10)
            search_frame.pack(fill=tk.X, pady=(0, 10))

            # الصف الأول: حقول البحث الأساسية
            search_row1 = ttk.Frame(search_frame)
            search_row1.pack(fill=tk.X, pady=2)

            ttk.Label(search_row1, text="اسم المستخدم:").pack(side=tk.LEFT, padx=5)
            self.um_search_username = ttk.Entry(search_row1, width=15)
            self.um_search_username.pack(side=tk.LEFT, padx=5)
            self.um_search_username.bind("<KeyRelease>", self.um_on_search_change)

            ttk.Label(search_row1, text="البروفايل:").pack(side=tk.LEFT, padx=5)
            self.um_search_profile = ttk.Entry(search_row1, width=15)
            self.um_search_profile.pack(side=tk.LEFT, padx=5)
            self.um_search_profile.bind("<KeyRelease>", self.um_on_search_change)

            # زر جلب البروفايلات بجانب حقل البروفايل مباشرة
            ttk.Button(search_row1, text="🔄 جلب البروفايلات",
                      command=self.um_fetch_profiles).pack(side=tk.LEFT, padx=2)

            ttk.Label(search_row1, text="التعليق:").pack(side=tk.LEFT, padx=5)
            self.um_search_comment = ttk.Entry(search_row1, width=15)
            self.um_search_comment.pack(side=tk.LEFT, padx=5)
            self.um_search_comment.bind("<KeyRelease>", self.um_on_search_change)

            ttk.Label(search_row1, text="الموقع:").pack(side=tk.LEFT, padx=5)
            self.um_search_location = ttk.Entry(search_row1, width=15)
            self.um_search_location.pack(side=tk.LEFT, padx=5)
            self.um_search_location.bind("<KeyRelease>", self.um_on_search_change)

            # الصف الثاني: حقول البحث الإضافية
            search_row2 = ttk.Frame(search_frame)
            search_row2.pack(fill=tk.X, pady=2)

            ttk.Label(search_row2, text="الإيميل:").pack(side=tk.LEFT, padx=5)
            self.um_search_email = ttk.Entry(search_row2, width=15)
            self.um_search_email.pack(side=tk.LEFT, padx=5)
            self.um_search_email.bind("<KeyRelease>", self.um_on_search_change)

            ttk.Label(search_row2, text="الحالة:").pack(side=tk.LEFT, padx=5)
            self.um_search_status = ttk.Combobox(search_row2, values=["الكل", "نشط", "غير نشط"],
                                               state="readonly", width=12)
            self.um_search_status.pack(side=tk.LEFT, padx=5)
            self.um_search_status.set("الكل")
            self.um_search_status.bind("<<ComboboxSelected>>", self.um_on_search_change)

            # الصف الثالث: أزرار البحث والتحديث أسفل حقل الإيميل مباشرة
            search_buttons_row = ttk.Frame(search_frame)
            search_buttons_row.pack(fill=tk.X, pady=2)

            ttk.Button(search_buttons_row, text="🔄 تحديث البيانات",
                      command=self.um_fetch_all_users).pack(side=tk.LEFT, padx=5)
            ttk.Button(search_buttons_row, text="🗑️ مسح البحث",
                      command=self.um_clear_search).pack(side=tk.LEFT, padx=5)

            # إطار الإحصائيات
            stats_frame = ttk.LabelFrame(main_frame, text="📊 الإحصائيات", padding=5)
            stats_frame.pack(fill=tk.X, pady=(0, 10))

            self.um_total_label = ttk.Label(stats_frame, text="المجموع: 0")
            self.um_total_label.pack(side=tk.LEFT, padx=10)

            self.um_active_label = ttk.Label(stats_frame, text="نشط: 0")
            self.um_active_label.pack(side=tk.LEFT, padx=10)

            self.um_inactive_label = ttk.Label(stats_frame, text="غير نشط: 0")
            self.um_inactive_label.pack(side=tk.LEFT, padx=10)

            self.um_filtered_label = ttk.Label(stats_frame, text="المفلتر: 0")
            self.um_filtered_label.pack(side=tk.LEFT, padx=10)

            # إطار التحديد المتقدم والعمليات الجماعية
            selection_frame = ttk.LabelFrame(main_frame, text="🎯 التحديد والعمليات الجماعية", padding=5)
            selection_frame.pack(fill=tk.X, pady=(0, 10))

            # checkbox تحديد الكل
            self.um_select_all_var = tk.BooleanVar()
            self.um_select_all_checkbox = ttk.Checkbutton(
                selection_frame,
                text="تحديد الكل",
                variable=self.um_select_all_var,
                command=self.um_toggle_select_all
            )
            self.um_select_all_checkbox.pack(side=tk.LEFT, padx=5)

            # عداد المحددين
            self.um_selected_count_label = ttk.Label(selection_frame, text="المحدد: 0")
            self.um_selected_count_label.pack(side=tk.LEFT, padx=10)

            # أزرار العمليات على المحددين
            ttk.Button(selection_frame, text="✅ تفعيل المحددين",
                      command=self.um_enable_selected_users).pack(side=tk.RIGHT, padx=2)
            ttk.Button(selection_frame, text="❌ تعطيل المحددين",
                      command=self.um_disable_selected_users).pack(side=tk.RIGHT, padx=2)

            # زر تعديل المستخدم بجانب زر الحذف مباشرة
            self.um_edit_user_button = ttk.Button(selection_frame, text="✏️ تعديل المحدد",
                                                 command=self.um_edit_selected_user)
            self.um_edit_user_button.pack(side=tk.RIGHT, padx=2)

            ttk.Button(selection_frame, text="🗑️ حذف المحددين",
                      command=self.um_delete_selected_users).pack(side=tk.RIGHT, padx=2)
            ttk.Button(selection_frame, text="📤 تصدير المحددين",
                      command=self.um_export_selected_users).pack(side=tk.RIGHT, padx=2)

            # إطار الجدول
            tree_frame = ttk.Frame(main_frame)
            tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # تحديد الأعمدة لـ User Manager
            columns = ("Select", "Username", "Password", "Profile", "Comment", "Location", "Email", "Status")
            headings = {
                "Select": "تحديد",
                "Username": "اسم المستخدم",
                "Password": "كلمة السر",
                "Profile": "البروفايل",
                "Comment": "التعليق",
                "Location": "الموقع",
                "Email": "الإيميل",
                "Status": "الحالة"
            }
            widths = {
                "Select": 60,
                "Username": 120,
                "Password": 100,
                "Profile": 100,
                "Comment": 150,
                "Location": 80,
                "Email": 150,
                "Status": 80
            }

            # إنشاء الجدول
            self.um_cards_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

            # إعداد العناوين والعرض
            for col in columns:
                self.um_cards_tree.heading(col, text=headings[col])
                self.um_cards_tree.column(col, width=widths[col], minwidth=50)

            # إضافة scrollbars
            um_v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.um_cards_tree.yview)
            self.um_cards_tree.configure(yscrollcommand=um_v_scrollbar.set)

            um_h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.um_cards_tree.xview)
            self.um_cards_tree.configure(xscrollcommand=um_h_scrollbar.set)

            # ترتيب العناصر
            self.um_cards_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            um_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            um_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

            # ربط الأحداث
            self.um_cards_tree.bind("<Double-1>", self.um_on_user_double_click)
            self.um_cards_tree.bind("<Button-3>", self.um_show_context_menu)
            self.um_cards_tree.bind("<Button-1>", self.um_on_tree_click)

            # إطار أزرار التصدير والعمليات
            export_frame = ttk.LabelFrame(main_frame, text="📁 التصدير والعمليات", padding=5)
            export_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(export_frame, text="📊 تصدير نتائج البحث",
                      command=self.um_export_search_results).pack(side=tk.LEFT, padx=5)
            ttk.Button(export_frame, text="📋 نسخ اسم المستخدم",
                      command=self.um_copy_username).pack(side=tk.LEFT, padx=5)
            ttk.Button(export_frame, text="🔑 نسخ كلمة السر",
                      command=self.um_copy_password).pack(side=tk.LEFT, padx=5)
            ttk.Button(export_frame, text="ℹ️ عرض التفاصيل",
                      command=self.um_show_selected_user_details).pack(side=tk.LEFT, padx=5)

            # تهيئة المتغيرات
            self.um_all_users_data = []
            self.um_filtered_users_data = []
            self.um_selected_users_data = []

            self.logger.info("تم إعداد تبويب عرض الكروت لـ User Manager بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في إعداد تبويب عرض الكروت لـ User Manager: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في إعداد تبويب عرض الكروت: {str(e)}")

    def setup_hotspot_cards_view_tab(self):
        """إعداد تبويب عرض الكروت لـ Hotspot"""
        try:
            # إطار رئيسي للتبويب
            main_frame = ttk.Frame(self.hotspot_cards_view_frame)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # إطار البحث والفلترة
            search_frame = ttk.LabelFrame(main_frame, text="🔍 البحث والفلترة المتقدمة", padding=10)
            search_frame.pack(fill=tk.X, pady=(0, 10))

            # الصف الأول: حقول البحث الأساسية
            search_row1 = ttk.Frame(search_frame)
            search_row1.pack(fill=tk.X, pady=2)

            ttk.Label(search_row1, text="اسم المستخدم:").pack(side=tk.LEFT, padx=5)
            self.hs_search_username = ttk.Entry(search_row1, width=15)
            self.hs_search_username.pack(side=tk.LEFT, padx=5)
            self.hs_search_username.bind("<KeyRelease>", self.hs_on_search_change)

            ttk.Label(search_row1, text="البروفايل:").pack(side=tk.LEFT, padx=5)
            self.hs_search_profile = ttk.Entry(search_row1, width=15)
            self.hs_search_profile.pack(side=tk.LEFT, padx=5)
            self.hs_search_profile.bind("<KeyRelease>", self.hs_on_search_change)

            # زر جلب البروفايلات بجانب حقل البروفايل مباشرة
            ttk.Button(search_row1, text="🔄 جلب البروفايلات",
                      command=self.hs_fetch_profiles).pack(side=tk.LEFT, padx=2)

            ttk.Label(search_row1, text="التعليق:").pack(side=tk.LEFT, padx=5)
            self.hs_search_comment = ttk.Entry(search_row1, width=15)
            self.hs_search_comment.pack(side=tk.LEFT, padx=5)
            self.hs_search_comment.bind("<KeyRelease>", self.hs_on_search_change)

            ttk.Label(search_row1, text="الخادم:").pack(side=tk.LEFT, padx=5)
            self.hs_search_server = ttk.Entry(search_row1, width=15)
            self.hs_search_server.pack(side=tk.LEFT, padx=5)
            self.hs_search_server.bind("<KeyRelease>", self.hs_on_search_change)

            # الصف الثاني: حقول البحث الإضافية
            search_row2 = ttk.Frame(search_frame)
            search_row2.pack(fill=tk.X, pady=2)

            ttk.Label(search_row2, text="الإيميل:").pack(side=tk.LEFT, padx=5)
            self.hs_search_email = ttk.Entry(search_row2, width=15)
            self.hs_search_email.pack(side=tk.LEFT, padx=5)
            self.hs_search_email.bind("<KeyRelease>", self.hs_on_search_change)

            ttk.Label(search_row2, text="الحالة:").pack(side=tk.LEFT, padx=5)
            self.hs_search_status = ttk.Combobox(search_row2, values=["الكل", "نشط", "غير نشط"],
                                               state="readonly", width=12)
            self.hs_search_status.pack(side=tk.LEFT, padx=5)
            self.hs_search_status.set("الكل")
            self.hs_search_status.bind("<<ComboboxSelected>>", self.hs_on_search_change)

            # الصف الثالث: أزرار البحث والتحديث أسفل حقل الإيميل مباشرة
            search_buttons_row = ttk.Frame(search_frame)
            search_buttons_row.pack(fill=tk.X, pady=2)

            ttk.Button(search_buttons_row, text="🔄 تحديث البيانات",
                      command=self.hs_fetch_all_users).pack(side=tk.LEFT, padx=5)
            ttk.Button(search_buttons_row, text="🗑️ مسح البحث",
                      command=self.hs_clear_search).pack(side=tk.LEFT, padx=5)

            # إطار الإحصائيات
            stats_frame = ttk.LabelFrame(main_frame, text="📊 الإحصائيات", padding=5)
            stats_frame.pack(fill=tk.X, pady=(0, 10))

            self.hs_total_label = ttk.Label(stats_frame, text="المجموع: 0")
            self.hs_total_label.pack(side=tk.LEFT, padx=10)

            self.hs_active_label = ttk.Label(stats_frame, text="نشط: 0")
            self.hs_active_label.pack(side=tk.LEFT, padx=10)

            self.hs_inactive_label = ttk.Label(stats_frame, text="غير نشط: 0")
            self.hs_inactive_label.pack(side=tk.LEFT, padx=10)

            self.hs_filtered_label = ttk.Label(stats_frame, text="المفلتر: 0")
            self.hs_filtered_label.pack(side=tk.LEFT, padx=10)

            # إطار التحديد المتقدم والعمليات الجماعية
            selection_frame = ttk.LabelFrame(main_frame, text="🎯 التحديد والعمليات الجماعية", padding=5)
            selection_frame.pack(fill=tk.X, pady=(0, 10))

            # checkbox تحديد الكل
            self.hs_select_all_var = tk.BooleanVar()
            self.hs_select_all_checkbox = ttk.Checkbutton(
                selection_frame,
                text="تحديد الكل",
                variable=self.hs_select_all_var,
                command=self.hs_toggle_select_all
            )
            self.hs_select_all_checkbox.pack(side=tk.LEFT, padx=5)

            # عداد المحددين
            self.hs_selected_count_label = ttk.Label(selection_frame, text="المحدد: 0")
            self.hs_selected_count_label.pack(side=tk.LEFT, padx=10)

            # أزرار العمليات على المحددين
            ttk.Button(selection_frame, text="✅ تفعيل المحددين",
                      command=self.hs_enable_selected_users).pack(side=tk.RIGHT, padx=2)
            ttk.Button(selection_frame, text="❌ تعطيل المحددين",
                      command=self.hs_disable_selected_users).pack(side=tk.RIGHT, padx=2)

            # زر تعديل المستخدم بجانب زر الحذف مباشرة
            self.hs_edit_user_button = ttk.Button(selection_frame, text="✏️ تعديل المحدد",
                                                 command=self.hs_edit_selected_user)
            self.hs_edit_user_button.pack(side=tk.RIGHT, padx=2)

            ttk.Button(selection_frame, text="🗑️ حذف المحددين",
                      command=self.hs_delete_selected_users).pack(side=tk.RIGHT, padx=2)
            ttk.Button(selection_frame, text="📤 تصدير المحددين",
                      command=self.hs_export_selected_users).pack(side=tk.RIGHT, padx=2)

            # إطار الجدول
            tree_frame = ttk.Frame(main_frame)
            tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # تحديد الأعمدة لـ Hotspot
            columns = ("Select", "Username", "Password", "Profile", "Server", "Comment", "Email", "Status")
            headings = {
                "Select": "تحديد",
                "Username": "اسم المستخدم",
                "Password": "كلمة السر",
                "Profile": "البروفايل",
                "Server": "الخادم",
                "Comment": "التعليق",
                "Email": "الإيميل",
                "Status": "الحالة"
            }
            widths = {
                "Select": 60,
                "Username": 120,
                "Password": 100,
                "Profile": 100,
                "Server": 100,
                "Comment": 150,
                "Email": 150,
                "Status": 80
            }

            # إنشاء الجدول
            self.hs_cards_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

            # إعداد العناوين والعرض
            for col in columns:
                self.hs_cards_tree.heading(col, text=headings[col])
                self.hs_cards_tree.column(col, width=widths[col], minwidth=50)

            # إضافة scrollbars
            hs_v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.hs_cards_tree.yview)
            self.hs_cards_tree.configure(yscrollcommand=hs_v_scrollbar.set)

            hs_h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.hs_cards_tree.xview)
            self.hs_cards_tree.configure(xscrollcommand=hs_h_scrollbar.set)

            # ترتيب العناصر
            self.hs_cards_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            hs_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            hs_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

            # ربط الأحداث
            self.hs_cards_tree.bind("<Double-1>", self.hs_on_user_double_click)
            self.hs_cards_tree.bind("<Button-3>", self.hs_show_context_menu)
            self.hs_cards_tree.bind("<Button-1>", self.hs_on_tree_click)

            # إطار أزرار التصدير والعمليات
            export_frame = ttk.LabelFrame(main_frame, text="📁 التصدير والعمليات", padding=5)
            export_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(export_frame, text="📊 تصدير نتائج البحث",
                      command=self.hs_export_search_results).pack(side=tk.LEFT, padx=5)
            ttk.Button(export_frame, text="📋 نسخ اسم المستخدم",
                      command=self.hs_copy_username).pack(side=tk.LEFT, padx=5)
            ttk.Button(export_frame, text="🔑 نسخ كلمة السر",
                      command=self.hs_copy_password).pack(side=tk.LEFT, padx=5)
            ttk.Button(export_frame, text="ℹ️ عرض التفاصيل",
                      command=self.hs_show_selected_user_details).pack(side=tk.LEFT, padx=5)

            # تهيئة المتغيرات
            self.hs_all_users_data = []
            self.hs_filtered_users_data = []
            self.hs_selected_users_data = []

            self.logger.info("تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في إعداد تبويب عرض الكروت لـ Hotspot: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في إعداد تبويب عرض الكروت لـ Hotspot: {str(e)}")

    # ===== دوال User Manager Cards View =====

    def um_on_search_change(self, event=None):
        """تنفيذ البحث عند تغيير أي حقل بحث في User Manager"""
        try:
            if not self.um_all_users_data:
                return

            # جمع معايير البحث
            search_criteria = {
                'username': self.um_search_username.get().strip().lower(),
                'profile': self.um_search_profile.get().strip().lower(),
                'comment': self.um_search_comment.get().strip().lower(),
                'location': self.um_search_location.get().strip().lower(),
                'email': self.um_search_email.get().strip().lower(),
                'status': self.um_search_status.get()
            }

            # تطبيق الفلترة
            self.um_filtered_users_data = []
            for user in self.um_all_users_data:
                match = True

                # فحص كل معيار بحث
                if search_criteria['username'] and search_criteria['username'] not in user.get('username', '').lower():
                    match = False
                if search_criteria['profile'] and search_criteria['profile'] not in user.get('profile', '').lower():
                    match = False
                if search_criteria['comment'] and search_criteria['comment'] not in user.get('comment', '').lower():
                    match = False
                if search_criteria['location'] and search_criteria['location'] not in user.get('location', '').lower():
                    match = False
                if search_criteria['email'] and search_criteria['email'] not in user.get('email', '').lower():
                    match = False
                if search_criteria['status'] != "الكل" and search_criteria['status'] != user.get('status', ''):
                    match = False

                if match:
                    self.um_filtered_users_data.append(user)

            # تحديث العرض
            self.um_update_treeview_display()
            self.um_update_search_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في البحث (User Manager): {str(e)}")

    def um_clear_search(self):
        """مسح جميع حقول البحث في User Manager"""
        try:
            self.um_search_username.delete(0, tk.END)
            self.um_search_profile.delete(0, tk.END)
            self.um_search_comment.delete(0, tk.END)
            self.um_search_location.delete(0, tk.END)
            self.um_search_email.delete(0, tk.END)
            self.um_search_status.set("الكل")

            # إعادة تعيين البيانات المفلترة
            self.um_filtered_users_data = self.um_all_users_data.copy()
            self.um_update_treeview_display()
            self.um_update_search_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في مسح البحث (User Manager): {str(e)}")

    def um_update_treeview_display(self):
        """تحديث عرض الجدول في User Manager"""
        try:
            # مسح الجدول الحالي
            for item in self.um_cards_tree.get_children():
                self.um_cards_tree.delete(item)

            # إضافة البيانات المفلترة
            for user in self.um_filtered_users_data:
                # تحديد رمز التحديد
                is_selected = user in self.um_selected_users_data
                select_symbol = "☑" if is_selected else "☐"

                values = (
                    select_symbol,
                    user.get('username', ''),
                    user.get('password', ''),
                    user.get('profile', ''),
                    user.get('comment', ''),
                    user.get('location', ''),
                    user.get('email', ''),
                    user.get('status', 'نشط')
                )

                self.um_cards_tree.insert("", "end", values=values)

        except Exception as e:
            self.logger.error(f"خطأ في تحديث عرض الجدول (User Manager): {str(e)}")

    def um_update_search_statistics(self):
        """تحديث إحصائيات البحث في User Manager"""
        try:
            total = len(self.um_all_users_data)
            filtered = len(self.um_filtered_users_data)

            # حساب النشطين وغير النشطين
            active = len([u for u in self.um_all_users_data if u.get('status', '') == 'نشط'])
            inactive = total - active

            # تحديث التسميات
            self.um_total_label.configure(text=f"المجموع: {total}")
            self.um_active_label.configure(text=f"نشط: {active}")
            self.um_inactive_label.configure(text=f"غير نشط: {inactive}")
            self.um_filtered_label.configure(text=f"المفلتر: {filtered}")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات (User Manager): {str(e)}")

    def um_fetch_all_users(self):
        """جلب جميع المستخدمين من MikroTik User Manager"""
        try:
            api = self.connect_api()
            if not api:
                return

            self.logger.info("بدء جلب مستخدمي User Manager")

            # إنشاء نافذة التقدم
            progress_window = tk.Toplevel(self.root)
            progress_window.title("جلب البيانات")
            progress_window.geometry("400x150")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # إعداد نافذة التقدم
            ttk.Label(progress_window, text="جاري جلب بيانات User Manager...",
                     font=self.fonts['arabic']).pack(pady=20)

            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=tk.X)
            progress_bar.start()

            status_label = ttk.Label(progress_window, text="الاتصال بالخادم...",
                                   font=self.fonts['arabic'])
            status_label.pack(pady=5)

            # تحديث الواجهة
            self.root.update()

            # جلب البيانات
            users_data = self.um_fetch_user_manager_users(api, status_label)

            # حفظ البيانات
            self.um_all_users_data = users_data
            self.um_filtered_users_data = users_data.copy()
            self.um_selected_users_data.clear()

            # تحديث الجدول والإحصائيات
            self.um_update_treeview_display()
            self.um_update_search_statistics()
            self.um_update_selected_count()

            # إغلاق نافذة التقدم
            progress_window.destroy()

            messagebox.showinfo("نجح", f"تم جلب {len(users_data)} مستخدم من User Manager بنجاح")
            self.logger.info(f"تم جلب {len(users_data)} مستخدم من User Manager بنجاح")

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()

            error_msg = str(e)
            self.logger.error(f"خطأ في جلب مستخدمي User Manager: {error_msg}")
            messagebox.showerror("خطأ", f"فشل في جلب المستخدمين: {error_msg}")

    def um_fetch_user_manager_users(self, api, status_label):
        """جلب مستخدمي User Manager مع معالجة آمنة"""
        try:
            status_label.configure(text="جلب مستخدمي User Manager...")
            self.root.update()

            # تحديد المسار حسب الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'  # افتراضي

            self.logger.info(f"جلب مستخدمي User Manager - الإصدار: {version_value}")

            # تحديد المسار والحصول على البيانات
            if version_value == "v6":
                resource_path = '/tool/user-manager/user'
                status_label.configure(text="جلب البيانات من User Manager v6...")
            else:
                resource_path = '/user-manager/user'
                status_label.configure(text="جلب البيانات من User Manager v7...")

            self.root.update()
            self.logger.info(f"استخدام المسار: {resource_path}")

            # جلب البيانات من MikroTik
            users = api.get_resource(resource_path).get()
            self.logger.info(f"تم جلب {len(users)} مستخدم من User Manager")

            status_label.configure(text=f"معالجة {len(users)} مستخدم...")
            self.root.update()

            users_data = []
            for i, user in enumerate(users):
                # تحديث حالة التقدم كل 10 مستخدمين
                if i % 10 == 0:
                    status_label.configure(text=f"معالجة المستخدم {i+1}/{len(users)}...")
                    self.root.update()

                try:
                    # معالجة آمنة لجميع الحقول
                    username = user.get('username', user.get('name', ''))
                    password = user.get('password', '')
                    profile = user.get('profile', '')
                    comment = user.get('comment', '')
                    location = user.get('location', '')
                    email = user.get('email', '')

                    # معالجة حالة المستخدم
                    disabled = user.get('disabled', 'false')
                    if isinstance(disabled, bool):
                        status = 'غير نشط' if disabled else 'نشط'
                    else:
                        status = 'غير نشط' if str(disabled).lower() == 'true' else 'نشط'

                    user_data = {
                        'username': str(username) if username else '',
                        'password': str(password) if password else '',
                        'profile': str(profile) if profile else '',
                        'comment': str(comment) if comment else '',
                        'location': str(location) if location else '',
                        'email': str(email) if email else '',
                        'status': status
                    }

                except Exception as user_error:
                    self.logger.warning(f"خطأ في معالجة المستخدم {i+1}: {str(user_error)}")
                    # إنشاء بيانات افتراضية في حالة الخطأ
                    user_data = {
                        'username': f'user_{i+1}',
                        'password': '',
                        'profile': '',
                        'comment': '',
                        'location': '',
                        'email': '',
                        'status': 'نشط'
                    }

                users_data.append(user_data)

            self.logger.info(f"تم معالجة {len(users_data)} مستخدم بنجاح")
            status_label.configure(text="اكتمل جلب البيانات...")
            self.root.update()

            return users_data

        except Exception as e:
            error_msg = f"خطأ في جلب مستخدمي User Manager: {str(e)}"
            self.logger.error(error_msg)
            status_label.configure(text="فشل في جلب البيانات!")
            self.root.update()
            raise Exception(error_msg)

    def um_on_tree_click(self, event):
        """معالجة النقر على الجدول في User Manager"""
        try:
            # تحديد العنصر المنقور عليه
            item = self.um_cards_tree.identify_row(event.y)
            if not item:
                return

            # الحصول على بيانات المستخدم
            values = self.um_cards_tree.item(item, 'values')
            if not values:
                return

            # البحث عن المستخدم في البيانات المفلترة
            username = values[1]  # تجاهل عمود التحديد
            user_data = None
            for user in self.um_filtered_users_data:
                if user.get('username', '') == username:
                    user_data = user
                    break

            if not user_data:
                return

            # تبديل حالة التحديد
            if user_data in self.um_selected_users_data:
                self.um_selected_users_data.remove(user_data)
            else:
                self.um_selected_users_data.append(user_data)

            # تحديث العرض
            self.um_update_treeview_display()
            self.um_update_selected_count()

        except Exception as e:
            self.logger.error(f"خطأ في النقر على الجدول (User Manager): {str(e)}")

    def um_toggle_select_all(self):
        """تبديل تحديد جميع المستخدمين في User Manager"""
        try:
            if self.um_select_all_var.get():
                # تحديد الكل
                self.um_selected_users_data = self.um_filtered_users_data.copy()
            else:
                # إلغاء تحديد الكل
                self.um_selected_users_data.clear()

            # تحديث العرض
            self.um_update_treeview_display()
            self.um_update_selected_count()

        except Exception as e:
            self.logger.error(f"خطأ في تحديد الكل (User Manager): {str(e)}")

    def um_update_selected_count(self):
        """تحديث عداد المستخدمين المحددين في User Manager"""
        try:
            count = len(self.um_selected_users_data)
            self.um_selected_count_label.configure(text=f"المحدد: {count}")

            # تحديث حالة checkbox تحديد الكل
            if count == 0:
                self.um_select_all_var.set(False)
            elif count == len(self.um_filtered_users_data):
                self.um_select_all_var.set(True)
            else:
                # حالة جزئية
                self.um_select_all_var.set(False)

            # تحديث حالة زر التعديل - يكون فعال فقط عند تحديد مستخدم واحد بالضبط
            if hasattr(self, 'um_edit_user_button'):
                if count == 1:
                    self.um_edit_user_button.configure(state="normal")
                else:
                    self.um_edit_user_button.configure(state="disabled")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث عداد المحددين (User Manager): {str(e)}")

    def um_fetch_profiles(self):
        """جلب البروفايلات من MikroTik لتبويب User Manager"""
        try:
            api = self.connect_api()
            if not api:
                messagebox.showerror("خطأ", "فشل في الاتصال بـ MikroTik")
                return

            # تحديد الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            self.logger.info(f"بدء جلب البروفايلات من User Manager {version_value}")

            # تحديد المسار حسب الإصدار
            if version_value == "v6":
                resource_path = '/tool/user-manager/profile'
            else:
                resource_path = '/user-manager/profile'

            # جلب البروفايلات
            profiles = api.get_resource(resource_path).get()
            profile_names = [profile.get('name', '') for profile in profiles if profile.get('name')]

            if profile_names:
                # تحديث قائمة البروفايلات في حقل البحث
                # يمكن إضافة combobox للبروفايلات لاحقاً إذا لزم الأمر
                messagebox.showinfo("نجح", f"تم جلب {len(profile_names)} بروفايل بنجاح")
                self.logger.info(f"تم جلب البروفايلات: {', '.join(profile_names)}")

                # يمكن إضافة المزيد من الوظائف هنا مثل تحديث قائمة منسدلة للبروفايلات

            else:
                messagebox.showwarning("تحذير", "لم يتم العثور على أي بروفايلات")
                self.logger.warning("لم يتم العثور على أي بروفايلات في User Manager")

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"خطأ في جلب البروفايلات (User Manager): {error_msg}")

            # تحليل نوع الخطأ وتقديم رسالة مناسبة
            if "permission" in error_msg.lower() or "access" in error_msg.lower():
                messagebox.showerror("خطأ في الصلاحيات",
                                   f"فشل في جلب البروفايلات بسبب عدم وجود صلاحيات كافية\n\n"
                                   f"تأكد من أن المستخدم لديه صلاحية الوصول إلى User Manager\n\n"
                                   f"تفاصيل الخطأ: {error_msg}")
            elif "not found" in error_msg.lower():
                messagebox.showerror("خطأ في النظام",
                                   f"User Manager غير مفعل أو غير موجود\n\n"
                                   f"تأكد من تفعيل User Manager في MikroTik\n\n"
                                   f"تفاصيل الخطأ: {error_msg}")
            else:
                messagebox.showerror("خطأ", f"فشل في جلب البروفايلات: {error_msg}")

    def um_enable_selected_users(self):
        """تفعيل المستخدمين المحددين في User Manager"""
        try:
            if not self.um_selected_users_data:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي مستخدمين للتفعيل")
                return

            count = len(self.um_selected_users_data)
            response = messagebox.askyesno(
                "تأكيد التفعيل",
                f"هل تريد تفعيل {count} مستخدم؟"
            )

            if response:
                success_count = self.um_change_users_status(self.um_selected_users_data, enable=True)

                if success_count > 0:
                    messagebox.showinfo("نجح", f"تم تفعيل {success_count} مستخدم بنجاح")
                    # تحديث البيانات المحلية
                    for user in self.um_selected_users_data:
                        user['status'] = 'نشط'
                    self.um_update_treeview_display()
                    self.um_update_search_statistics()
                else:
                    messagebox.showwarning("تحذير", "لم يتم تفعيل أي مستخدم")

        except Exception as e:
            self.logger.error(f"خطأ في تفعيل المستخدمين المحددين (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تفعيل المستخدمين: {str(e)}")

    def um_disable_selected_users(self):
        """تعطيل المستخدمين المحددين في User Manager"""
        try:
            if not self.um_selected_users_data:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي مستخدمين للتعطيل")
                return

            count = len(self.um_selected_users_data)
            response = messagebox.askyesno(
                "تأكيد التعطيل",
                f"هل تريد تعطيل {count} مستخدم؟"
            )

            if response:
                success_count = self.um_change_users_status(self.um_selected_users_data, enable=False)

                if success_count > 0:
                    messagebox.showinfo("نجح", f"تم تعطيل {success_count} مستخدم بنجاح")
                    # تحديث البيانات المحلية
                    for user in self.um_selected_users_data:
                        user['status'] = 'غير نشط'
                    self.um_update_treeview_display()
                    self.um_update_search_statistics()
                else:
                    messagebox.showwarning("تحذير", "لم يتم تعطيل أي مستخدم")

        except Exception as e:
            self.logger.error(f"خطأ في تعطيل المستخدمين المحددين (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تعطيل المستخدمين: {str(e)}")

    def um_change_users_status(self, users_to_change, enable=True):
        """تغيير حالة المستخدمين في MikroTik User Manager باستخدام الأوامر الصحيحة"""
        try:
            api = self.connect_api()
            if not api:
                self.logger.error("فشل في الاتصال بـ MikroTik API")
                return 0

            success_count = 0
            failed_users = []
            connection_errors = 0
            permission_errors = 0
            not_found_errors = 0

            # تحديد الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            action_text = "تفعيل" if enable else "تعطيل"
            self.logger.info(f"بدء {action_text} {len(users_to_change)} مستخدم في User Manager {version_value}")

            for i, user in enumerate(users_to_change, 1):
                try:
                    username = user.get('username', '')
                    if not username:
                        self.logger.warning(f"تم تجاهل المستخدم #{i} بدون اسم مستخدم")
                        continue

                    self.logger.info(f"معالجة المستخدم {i}/{len(users_to_change)}: {username}")

                    # تحديد المسار حسب الإصدار
                    if version_value == "v6":
                        resource_path = '/tool/user-manager/user'
                        search_param = 'username'
                    else:
                        resource_path = '/user-manager/user'
                        search_param = 'name'

                    resource = api.get_resource(resource_path)
                    user_found = False

                    try:
                        # البحث عن المستخدم
                        users_found = resource.get(**{search_param: username})

                        if users_found:
                            user_found = True
                            for existing_user in users_found:
                                user_id = existing_user['id']
                                self.logger.info(f"تم العثور على المستخدم {username} بـ ID: {user_id}")

                                # تحويل القيمة Boolean إلى string كما يتوقع MikroTik API
                                disabled_value = "true" if not enable else "false"

                                # تغيير حالة المستخدم باستخدام القيم الصحيحة
                                resource.set(id=user_id, disabled=disabled_value)
                                success_count += 1
                                self.logger.info(f"تم {action_text} المستخدم بنجاح: {username} (ID: {user_id})")
                                break  # نحتاج فقط لتعديل أول مطابقة

                        else:
                            # محاولة البحث بطريقة أخرى
                            all_users = resource.get()
                            for existing_user in all_users:
                                user_name = existing_user.get('name', existing_user.get('username', ''))
                                if user_name == username:
                                    user_id = existing_user['id']
                                    disabled_value = "true" if not enable else "false"
                                    resource.set(id=user_id, disabled=disabled_value)
                                    success_count += 1
                                    self.logger.info(f"تم {action_text} المستخدم بنجاح (بحث شامل): {username}")
                                    user_found = True
                                    break

                        if not user_found:
                            failed_users.append(username)
                            not_found_errors += 1
                            self.logger.error(f"لم يتم العثور على المستخدم: {username}")

                    except Exception as search_error:
                        failed_users.append(username)
                        error_msg = str(search_error)

                        # تصنيف نوع الخطأ
                        if "permission" in error_msg.lower() or "access" in error_msg.lower():
                            permission_errors += 1
                            self.logger.error(f"خطأ في الصلاحيات للمستخدم {username}: {error_msg}")
                        elif "connection" in error_msg.lower() or "timeout" in error_msg.lower():
                            connection_errors += 1
                            self.logger.error(f"خطأ في الاتصال للمستخدم {username}: {error_msg}")
                        else:
                            self.logger.error(f"خطأ في معالجة المستخدم {username}: {error_msg}")

                except Exception as user_error:
                    failed_users.append(username)
                    self.logger.error(f"خطأ عام في معالجة المستخدم {username}: {str(user_error)}")

            # تسجيل النتائج التفصيلية
            self.logger.info(f"انتهى {action_text} المستخدمين:")
            self.logger.info(f"  - نجح: {success_count}")
            self.logger.info(f"  - فشل: {len(failed_users)}")
            self.logger.info(f"  - أخطاء الاتصال: {connection_errors}")
            self.logger.info(f"  - أخطاء الصلاحيات: {permission_errors}")
            self.logger.info(f"  - مستخدمون غير موجودون: {not_found_errors}")

            if failed_users:
                self.logger.warning(f"المستخدمون الذين فشل {action_text}هم: {', '.join(failed_users)}")

            return success_count

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"خطأ عام في تغيير حالة المستخدمين: {error_msg}")
            return 0

    def um_delete_selected_users(self):
        """حذف المستخدمين المحددين في User Manager"""
        try:
            if not self.um_selected_users_data:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي مستخدمين للحذف")
                return

            count = len(self.um_selected_users_data)
            response = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف {count} مستخدم؟\nهذا الإجراء لا يمكن التراجع عنه."
            )

            if response:
                success_count = self.um_delete_users_from_mikrotik(self.um_selected_users_data)

                if success_count > 0:
                    messagebox.showinfo("نجح", f"تم حذف {success_count} مستخدم بنجاح")

                    # إزالة من البيانات المحلية
                    for user in self.um_selected_users_data:
                        if user in self.um_all_users_data:
                            self.um_all_users_data.remove(user)
                        if user in self.um_filtered_users_data:
                            self.um_filtered_users_data.remove(user)

                    # مسح التحديد
                    self.um_selected_users_data.clear()

                    # تحديث الجدول
                    self.um_update_treeview_display()
                    self.um_update_search_statistics()
                    self.um_update_selected_count()
                else:
                    messagebox.showwarning("تحذير", "لم يتم حذف أي مستخدم")

        except Exception as e:
            self.logger.error(f"خطأ في حذف المستخدمين المحددين (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في حذف المستخدمين: {str(e)}")

    def um_delete_users_from_mikrotik(self, users_to_delete):
        """حذف المستخدمين من MikroTik User Manager"""
        try:
            api = self.connect_api()
            if not api:
                return 0

            success_count = 0

            # تحديد المسار حسب الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            if version_value == "v6":
                resource_path = '/tool/user-manager/user'
            else:
                resource_path = '/user-manager/user'

            resource = api.get_resource(resource_path)

            for user in users_to_delete:
                try:
                    username = user.get('username', '')
                    if username:
                        # البحث عن المستخدم في MikroTik
                        if version_value == "v6":
                            existing_users = resource.get(username=username)
                        else:
                            existing_users = resource.get(name=username)

                        for existing_user in existing_users:
                            # حذف المستخدم
                            resource.remove(id=existing_user['id'])
                            success_count += 1
                            self.logger.info(f"تم حذف المستخدم: {username}")

                except Exception as user_error:
                    self.logger.error(f"خطأ في حذف المستخدم {username}: {str(user_error)}")
                    continue

            return success_count

        except Exception as e:
            self.logger.error(f"خطأ في حذف المستخدمين من MikroTik: {str(e)}")
            return 0

    def um_export_selected_users(self):
        """تصدير المستخدمين المحددين في User Manager"""
        try:
            if not self.um_selected_users_data:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي مستخدمين للتصدير")
                return

            # حفظ البيانات المفلترة الحالية
            original_filtered = self.um_filtered_users_data.copy()

            # تعيين المحددين كبيانات مفلترة مؤقتاً
            self.um_filtered_users_data = self.um_selected_users_data.copy()

            # استخدام دالة التصدير الموجودة
            self.um_export_search_results()

            # استعادة البيانات المفلترة الأصلية
            self.um_filtered_users_data = original_filtered

        except Exception as e:
            self.logger.error(f"خطأ في تصدير المستخدمين المحددين (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير المستخدمين: {str(e)}")

    def um_export_search_results(self):
        """تصدير نتائج البحث في User Manager"""
        try:
            if not self.um_filtered_users_data:
                messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
                return

            # نافذة اختيار نوع التصدير
            export_window = tk.Toplevel(self.root)
            export_window.title("تصدير نتائج البحث")
            export_window.geometry("350x200")
            export_window.transient(self.root)
            export_window.grab_set()

            ttk.Label(export_window, text="اختر نوع الملف للتصدير:",
                     font=self.fonts['arabic_heading']).pack(pady=10)

            export_type = tk.StringVar(value="csv")

            # خيارات التصدير
            ttk.Radiobutton(export_window, text="CSV (Excel)",
                           variable=export_type, value="csv").pack(anchor="w", padx=20, pady=2)
            ttk.Radiobutton(export_window, text="JSON (البيانات المنظمة)",
                           variable=export_type, value="json").pack(anchor="w", padx=20, pady=2)
            ttk.Radiobutton(export_window, text="TXT (نص عادي)",
                           variable=export_type, value="txt").pack(anchor="w", padx=20, pady=2)

            # معلومات التصدير
            info_label = ttk.Label(export_window,
                                 text=f"سيتم تصدير {len(self.um_filtered_users_data)} مستخدم",
                                 font=self.fonts['arabic'])
            info_label.pack(pady=10)

            # أزرار التحكم
            button_frame = ttk.Frame(export_window)
            button_frame.pack(pady=20)

            ttk.Button(button_frame, text="تصدير",
                      command=lambda: self.um_perform_export_search_results(export_type.get(), export_window)).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="إلغاء",
                      command=export_window.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في نافذة التصدير (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التصدير: {str(e)}")

    def um_perform_export_search_results(self, export_type, window):
        """تنفيذ تصدير نتائج البحث في User Manager"""
        try:
            window.destroy()

            # تحديد امتداد الملف
            if export_type == "csv":
                file_ext = ".csv"
                file_types = [("CSV files", "*.csv")]
            elif export_type == "json":
                file_ext = ".json"
                file_types = [("JSON files", "*.json")]
            else:  # txt
                file_ext = ".txt"
                file_types = [("Text files", "*.txt")]

            # اختيار مكان الحفظ
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"user_manager_search_results_{timestamp}{file_ext}"

            filename = filedialog.asksaveasfilename(
                defaultextension=file_ext,
                filetypes=file_types,
                initialfile=default_name
            )

            if not filename:
                return

            # تصدير البيانات
            if export_type == "csv":
                self.um_export_search_to_csv(filename)
            elif export_type == "json":
                self.um_export_search_to_json(filename)
            else:
                self.um_export_search_to_txt(filename)

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ التصدير (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في التصدير: {str(e)}")

    def um_export_search_to_csv(self, filename):
        """تصدير نتائج البحث إلى CSV في User Manager"""
        try:
            import csv

            with open(filename, 'w', newline='', encoding='utf-8') as file:
                fieldnames = ['username', 'password', 'profile', 'comment', 'location', 'email', 'status']
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()

                for user in self.um_filtered_users_data:
                    row = {field: user.get(field, '') for field in fieldnames}
                    writer.writerow(row)

            messagebox.showinfo("نجح", f"تم تصدير {len(self.um_filtered_users_data)} مستخدم إلى CSV بنجاح")
            self.logger.info(f"تم تصدير {len(self.um_filtered_users_data)} مستخدم إلى CSV")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير CSV (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير CSV: {str(e)}")

    def um_export_search_to_json(self, filename):
        """تصدير نتائج البحث إلى JSON في User Manager"""
        try:
            from datetime import datetime
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'system_type': 'user_manager',
                    'total_users': len(self.um_filtered_users_data),
                    'search_criteria': getattr(self, 'um_last_search_criteria', {})
                },
                'users': self.um_filtered_users_data
            }

            with open(filename, 'w', encoding='utf-8') as file:
                json.dump(export_data, file, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", f"تم تصدير {len(self.um_filtered_users_data)} مستخدم إلى JSON بنجاح")
            self.logger.info(f"تم تصدير {len(self.um_filtered_users_data)} مستخدم إلى JSON")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير JSON (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير JSON: {str(e)}")

    def um_export_search_to_txt(self, filename):
        """تصدير نتائج البحث إلى TXT في User Manager"""
        try:
            from datetime import datetime
            with open(filename, 'w', encoding='utf-8') as file:
                # كتابة معلومات التصدير
                file.write(f"تقرير مستخدمي User Manager\n")
                file.write(f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                file.write(f"عدد المستخدمين: {len(self.um_filtered_users_data)}\n")
                file.write("=" * 80 + "\n\n")

                # كتابة بيانات المستخدمين
                for i, user in enumerate(self.um_filtered_users_data, 1):
                    file.write(f"المستخدم #{i}:\n")
                    file.write(f"  اسم المستخدم: {user.get('username', '')}\n")
                    file.write(f"  كلمة السر: {user.get('password', '')}\n")
                    file.write(f"  البروفايل: {user.get('profile', '')}\n")
                    file.write(f"  التعليق: {user.get('comment', '')}\n")
                    file.write(f"  الموقع: {user.get('location', '')}\n")
                    file.write(f"  الإيميل: {user.get('email', '')}\n")
                    file.write(f"  الحالة: {user.get('status', '')}\n")
                    file.write("-" * 40 + "\n")

            messagebox.showinfo("نجح", f"تم تصدير {len(self.um_filtered_users_data)} مستخدم إلى TXT بنجاح")
            self.logger.info(f"تم تصدير {len(self.um_filtered_users_data)} مستخدم إلى TXT")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير TXT (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير TXT: {str(e)}")

    def um_on_user_double_click(self, event):
        """معالجة النقر المزدوج على مستخدم في User Manager"""
        try:
            selection = self.um_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.um_cards_tree.item(item, 'values')

            if not values:
                return

            # إنشاء نافذة تفاصيل المستخدم
            self.um_show_user_details(values)

        except Exception as e:
            self.logger.error(f"خطأ في معالجة النقر المزدوج (User Manager): {str(e)}")

    def um_show_context_menu(self, event):
        """عرض القائمة السياقية في User Manager"""
        try:
            # تحديد العنصر المنقور عليه
            item = self.um_cards_tree.identify_row(event.y)
            if item:
                self.um_cards_tree.selection_set(item)

                # إنشاء القائمة السياقية
                context_menu = tk.Menu(self.root, tearoff=0)
                context_menu.add_command(label="عرض التفاصيل", command=self.um_show_selected_user_details)
                context_menu.add_separator()
                context_menu.add_command(label="تفعيل المستخدم", command=self.um_enable_single_user)
                context_menu.add_command(label="تعطيل المستخدم", command=self.um_disable_single_user)
                context_menu.add_separator()
                context_menu.add_command(label="نسخ اسم المستخدم", command=self.um_copy_username)
                context_menu.add_command(label="نسخ كلمة السر", command=self.um_copy_password)
                context_menu.add_separator()
                context_menu.add_command(label="حذف المستخدم", command=self.um_delete_single_user)

                # عرض القائمة
                context_menu.tk_popup(event.x_root, event.y_root)

        except Exception as e:
            self.logger.error(f"خطأ في عرض القائمة السياقية (User Manager): {str(e)}")

    def um_show_user_details(self, values):
        """عرض تفاصيل المستخدم في نافذة منفصلة في User Manager"""
        try:
            details_window = tk.Toplevel(self.root)
            details_window.title("تفاصيل المستخدم - User Manager")
            details_window.geometry("500x400")
            details_window.transient(self.root)
            details_window.grab_set()

            # إطار التفاصيل
            details_frame = ttk.LabelFrame(details_window, text="معلومات المستخدم", padding=15)
            details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # عرض التفاصيل (تجاهل عمود التحديد الأول)
            labels = ["اسم المستخدم:", "كلمة السر:", "البروفايل:", "التعليق:", "الموقع:", "الإيميل:", "الحالة:"]
            user_values = values[1:]  # تجاهل عمود التحديد

            for i, (label, value) in enumerate(zip(labels, user_values)):
                ttk.Label(details_frame, text=label, font=self.fonts['arabic']).grid(
                    row=i, column=0, sticky="e", padx=5, pady=5)

                # إنشاء حقل نص قابل للتحديد
                value_entry = ttk.Entry(details_frame, width=40, font=self.fonts['arabic'])
                value_entry.grid(row=i, column=1, sticky="w", padx=5, pady=5)
                value_entry.insert(0, str(value))
                value_entry.configure(state="readonly")

            # أزرار التحكم
            button_frame = ttk.Frame(details_window)
            button_frame.pack(pady=10)

            ttk.Button(button_frame, text="إغلاق", command=details_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في عرض تفاصيل المستخدم (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في عرض التفاصيل: {str(e)}")

    def um_show_selected_user_details(self):
        """عرض تفاصيل المستخدم المحدد في User Manager"""
        try:
            selection = self.um_cards_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى تحديد مستخدم أولاً")
                return

            item = selection[0]
            values = self.um_cards_tree.item(item, 'values')
            self.um_show_user_details(values)

        except Exception as e:
            self.logger.error(f"خطأ في عرض تفاصيل المستخدم المحدد (User Manager): {str(e)}")

    def um_copy_username(self):
        """نسخ اسم المستخدم إلى الحافظة في User Manager"""
        try:
            selection = self.um_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.um_cards_tree.item(item, 'values')
            username = values[1]  # تجاهل عمود التحديد

            self.root.clipboard_clear()
            self.root.clipboard_append(username)
            messagebox.showinfo("نجح", f"تم نسخ اسم المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في نسخ اسم المستخدم (User Manager): {str(e)}")

    def um_copy_password(self):
        """نسخ كلمة السر إلى الحافظة في User Manager"""
        try:
            selection = self.um_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.um_cards_tree.item(item, 'values')
            password = values[2]  # تجاهل عمود التحديد

            self.root.clipboard_clear()
            self.root.clipboard_append(password)
            messagebox.showinfo("نجح", f"تم نسخ كلمة السر: {password}")

        except Exception as e:
            self.logger.error(f"خطأ في نسخ كلمة السر (User Manager): {str(e)}")

    def um_enable_single_user(self):
        """تفعيل مستخدم واحد في User Manager"""
        try:
            selection = self.um_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.um_cards_tree.item(item, 'values')
            username = values[1]

            # البحث عن المستخدم في البيانات
            user_data = None
            for user in self.um_filtered_users_data:
                if user.get('username', '') == username:
                    user_data = user
                    break

            if user_data:
                success_count = self.um_change_users_status([user_data], enable=True)
                if success_count > 0:
                    user_data['status'] = 'نشط'
                    self.um_update_treeview_display()
                    self.um_update_search_statistics()
                    messagebox.showinfo("نجح", f"تم تفعيل المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في تفعيل المستخدم الواحد (User Manager): {str(e)}")

    def um_disable_single_user(self):
        """تعطيل مستخدم واحد في User Manager"""
        try:
            selection = self.um_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.um_cards_tree.item(item, 'values')
            username = values[1]

            # البحث عن المستخدم في البيانات
            user_data = None
            for user in self.um_filtered_users_data:
                if user.get('username', '') == username:
                    user_data = user
                    break

            if user_data:
                success_count = self.um_change_users_status([user_data], enable=False)
                if success_count > 0:
                    user_data['status'] = 'غير نشط'
                    self.um_update_treeview_display()
                    self.um_update_search_statistics()
                    messagebox.showinfo("نجح", f"تم تعطيل المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في تعطيل المستخدم الواحد (User Manager): {str(e)}")

    def um_delete_single_user(self):
        """حذف مستخدم واحد في User Manager"""
        try:
            selection = self.um_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.um_cards_tree.item(item, 'values')
            username = values[1]

            response = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم: {username}؟\nهذا الإجراء لا يمكن التراجع عنه."
            )

            if response:
                # البحث عن المستخدم في البيانات
                user_data = None
                for user in self.um_filtered_users_data:
                    if user.get('username', '') == username:
                        user_data = user
                        break

                if user_data:
                    success_count = self.um_delete_users_from_mikrotik([user_data])
                    if success_count > 0:
                        # إزالة من البيانات المحلية
                        if user_data in self.um_all_users_data:
                            self.um_all_users_data.remove(user_data)
                        if user_data in self.um_filtered_users_data:
                            self.um_filtered_users_data.remove(user_data)
                        if user_data in self.um_selected_users_data:
                            self.um_selected_users_data.remove(user_data)

                        self.um_update_treeview_display()
                        self.um_update_search_statistics()
                        self.um_update_selected_count()
                        messagebox.showinfo("نجح", f"تم حذف المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في حذف المستخدم الواحد (User Manager): {str(e)}")

    def um_edit_selected_user(self):
        """تعديل المستخدم المحدد في User Manager"""
        try:
            # التحقق من وجود مستخدم واحد محدد بالضبط
            if len(self.um_selected_users_data) != 1:
                if len(self.um_selected_users_data) == 0:
                    messagebox.showwarning("تحذير", "يرجى تحديد مستخدم واحد للتعديل")
                else:
                    messagebox.showwarning("تحذير", f"يرجى تحديد مستخدم واحد فقط للتعديل\nعدد المحددين حالياً: {len(self.um_selected_users_data)}")
                return

            # الحصول على بيانات المستخدم المحدد
            user_data = self.um_selected_users_data[0]
            self.logger.info(f"بدء تعديل المستخدم: {user_data.get('username', '')}")

            # فتح نافذة التعديل
            self.um_open_edit_user_window(user_data)

        except Exception as e:
            self.logger.error(f"خطأ في تعديل المستخدم المحدد (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التعديل: {str(e)}")

    def um_open_edit_user_window(self, user_data):
        """فتح نافذة تعديل المستخدم في User Manager"""
        try:
            # إنشاء نافذة التعديل
            edit_window = tk.Toplevel(self.root)
            edit_window.title(f"تعديل المستخدم: {user_data.get('username', '')}")
            edit_window.geometry("600x500")
            edit_window.transient(self.root)
            edit_window.grab_set()

            # إطار رئيسي
            main_frame = ttk.Frame(edit_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            # عنوان النافذة
            title_label = ttk.Label(main_frame,
                                   text=f"تعديل بيانات المستخدم: {user_data.get('username', '')}",
                                   font=self.fonts['arabic_heading'])
            title_label.pack(pady=(0, 20))

            # إطار الحقول
            fields_frame = ttk.LabelFrame(main_frame, text="بيانات المستخدم", padding=15)
            fields_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            # متغيرات الحقول
            edit_vars = {}

            # تحويل حد البيانات من bytes إلى GB للعرض
            limit_bytes_gb = ""
            if user_data.get('limit_bytes_total'):
                try:
                    limit_bytes = int(user_data.get('limit_bytes_total', 0))
                    if limit_bytes > 0:
                        limit_bytes_gb = str(round(limit_bytes / (1024 * 1024 * 1024), 2))
                except (ValueError, TypeError):
                    limit_bytes_gb = ""

            # إنشاء الحقول (مخصص لـ User Manager)
            fields_info = [
                ("username", "اسم المستخدم:", user_data.get('username', ''), "entry"),
                ("password", "كلمة السر:", user_data.get('password', ''), "entry"),
                ("profile", "البروفايل:", user_data.get('profile', ''), "combobox"),
                ("comment", "التعليق:", user_data.get('comment', ''), "entry"),
                ("location", "الموقع:", user_data.get('location', ''), "entry"),
                ("email", "الإيميل:", user_data.get('email', ''), "entry"),
                ("limit_bytes_gb", "حد البيانات (GB):", limit_bytes_gb, "entry_with_hint"),
                ("status", "الحالة:", user_data.get('status', 'نشط'), "combobox")
            ]

            row = 0
            for field_name, label_text, current_value, field_type in fields_info:
                # تسمية الحقل
                ttk.Label(fields_frame, text=label_text, font=self.fonts['arabic']).grid(
                    row=row, column=0, sticky="e", padx=(0, 10), pady=8)

                # إنشاء الحقل حسب النوع
                if field_type == "entry":
                    var = tk.StringVar(value=current_value)
                    entry = ttk.Entry(fields_frame, textvariable=var, width=40, font=self.fonts['arabic'])
                    entry.grid(row=row, column=1, sticky="w", pady=8)
                    edit_vars[field_name] = var

                elif field_type == "entry_with_hint":
                    # حقل حد البيانات مع نص توضيحي
                    var = tk.StringVar(value=current_value)
                    entry = ttk.Entry(fields_frame, textvariable=var, width=40, font=self.fonts['arabic'])
                    entry.grid(row=row, column=1, sticky="w", pady=8)
                    edit_vars[field_name] = var

                    # إضافة نص توضيحي
                    hint_text = "اتركه فارغاً لعدم وجود حد، أو أدخل القيمة بالجيجابايت (مثال: 5.5)"
                    hint_label = ttk.Label(fields_frame, text=hint_text,
                                         font=('Arial', 8), foreground='gray')
                    hint_label.grid(row=row, column=2, sticky="w", padx=(10, 0), pady=8)

                elif field_type == "combobox":
                    var = tk.StringVar(value=current_value)

                    if field_name == "profile":
                        # جلب البروفايلات المتاحة
                        profiles = self.um_get_available_profiles()
                        combobox = ttk.Combobox(fields_frame, textvariable=var, values=profiles,
                                              width=37, font=self.fonts['arabic'])
                    elif field_name == "status":
                        # خيارات الحالة
                        status_options = ["نشط", "غير نشط"]
                        combobox = ttk.Combobox(fields_frame, textvariable=var, values=status_options,
                                              state="readonly", width=37, font=self.fonts['arabic'])

                    combobox.grid(row=row, column=1, sticky="w", pady=8)
                    edit_vars[field_name] = var

                row += 1

            # إطار الأزرار
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X, pady=(15, 0))

            # أزرار التحكم
            ttk.Button(buttons_frame, text="💾 حفظ التغييرات",
                      command=lambda: self.um_save_user_changes(user_data, edit_vars, edit_window)).pack(side=tk.LEFT, padx=5)

            ttk.Button(buttons_frame, text="🔄 جلب البروفايلات",
                      command=lambda: self.um_refresh_profiles_in_edit(edit_vars['profile'])).pack(side=tk.LEFT, padx=5)

            ttk.Button(buttons_frame, text="❌ إلغاء",
                      command=edit_window.destroy).pack(side=tk.RIGHT, padx=5)

            # معلومات إضافية
            info_frame = ttk.LabelFrame(main_frame, text="معلومات", padding=10)
            info_frame.pack(fill=tk.X, pady=(15, 0))

            info_text = f"• سيتم حفظ التغييرات في MikroTik User Manager\n"
            info_text += f"• يمكن تعديل جميع الحقول بحرية (لا توجد قيود صارمة)\n"
            info_text += f"• حد البيانات: أدخل القيمة بالجيجابايت أو اتركه فارغاً\n"
            info_text += f"• الحقول المطلوبة: اسم المستخدم والبروفايل فقط"

            ttk.Label(info_frame, text=info_text, font=self.fonts['arabic']).pack(anchor="w")

            self.logger.info(f"تم فتح نافذة تعديل المستخدم: {user_data.get('username', '')}")

        except Exception as e:
            self.logger.error(f"خطأ في فتح نافذة التعديل (User Manager): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التعديل: {str(e)}")

    def um_get_available_profiles(self):
        """جلب البروفايلات المتاحة لـ User Manager"""
        try:
            api = self.connect_api()
            if not api:
                self.logger.warning("فشل في الاتصال لجلب البروفايلات")
                return ["default"]

            # تحديد المسار حسب الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            # تحديد المسار حسب الإصدار
            if version_value == "v6":
                resource_path = '/tool/user-manager/profile'
            else:
                resource_path = '/user-manager/profile'

            # جلب البروفايلات
            profiles = api.get_resource(resource_path).get()
            profile_names = [profile.get('name', '') for profile in profiles if profile.get('name')]

            if profile_names:
                self.logger.info(f"تم جلب {len(profile_names)} بروفايل لنافذة التعديل")
                return profile_names
            else:
                self.logger.warning("لم يتم العثور على أي بروفايلات")
                return ["default"]

        except Exception as e:
            self.logger.error(f"خطأ في جلب البروفايلات للتعديل: {str(e)}")
            return ["default"]

    def um_refresh_profiles_in_edit(self, profile_var):
        """تحديث قائمة البروفايلات في نافذة التعديل"""
        try:
            profiles = self.um_get_available_profiles()

            # البحث عن combobox البروفايل وتحديثه
            # هذا يتطلب الوصول إلى widget، سنستخدم طريقة بديلة
            messagebox.showinfo("تحديث البروفايلات",
                              f"تم جلب {len(profiles)} بروفايل\n\n" +
                              "البروفايلات المتاحة:\n" +
                              "\n".join(f"• {profile}" for profile in profiles[:10]))

            self.logger.info("تم تحديث قائمة البروفايلات في نافذة التعديل")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث البروفايلات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحديث البروفايلات: {str(e)}")

    def um_save_user_changes(self, original_user_data, edit_vars, edit_window):
        """حفظ تغييرات المستخدم في MikroTik User Manager"""
        try:
            # جمع البيانات الجديدة
            new_data = {}
            for field_name, var in edit_vars.items():
                new_data[field_name] = var.get().strip()

            # التحقق من صحة البيانات
            validation_result = self.um_validate_user_data(new_data, original_user_data)
            if not validation_result['valid']:
                messagebox.showerror("خطأ في البيانات", validation_result['message'])
                return

            # تأكيد الحفظ
            changes_summary = self.um_get_changes_summary(original_user_data, new_data)
            if not changes_summary['has_changes']:
                messagebox.showinfo("لا توجد تغييرات", "لم يتم إجراء أي تغييرات على بيانات المستخدم")
                return

            response = messagebox.askyesno(
                "تأكيد الحفظ",
                f"هل تريد حفظ التغييرات التالية؟\n\n{changes_summary['summary']}"
            )

            if not response:
                return

            # إنشاء نافذة التقدم
            progress_window = self.create_progress_window("حفظ التغييرات", "جاري حفظ تغييرات المستخدم...")

            try:
                # حفظ التغييرات في MikroTik
                success = self.um_update_user_in_mikrotik(original_user_data, new_data, progress_window)

                if success:
                    # تحديث البيانات المحلية
                    self.um_update_local_user_data(original_user_data, new_data)

                    # تحديث الجدول
                    self.um_update_treeview_display()
                    self.um_update_search_statistics()

                    progress_window.destroy()
                    edit_window.destroy()

                    messagebox.showinfo("نجح", f"تم حفظ تغييرات المستخدم بنجاح: {new_data['username']}")
                    self.logger.info(f"تم حفظ تغييرات المستخدم بنجاح: {new_data['username']}")

                else:
                    progress_window.destroy()
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات في MikroTik")

            except Exception as save_error:
                progress_window.destroy()
                raise save_error

        except Exception as e:
            self.logger.error(f"خطأ في حفظ تغييرات المستخدم: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في حفظ التغييرات: {str(e)}")

    def um_validate_user_data(self, new_data, original_data):
        """التحقق من صحة بيانات المستخدم في User Manager (مع قيود مرنة)"""
        try:
            # التحقق من اسم المستخدم (مطلوب فقط)
            if not new_data.get('username', '').strip():
                return {'valid': False, 'message': 'اسم المستخدم مطلوب ولا يمكن أن يكون فارغاً'}

            # التحقق من البروفايل (مطلوب فقط)
            profile = new_data.get('profile', '').strip()
            if not profile:
                return {'valid': False, 'message': 'البروفايل مطلوب ولا يمكن أن يكون فارغاً'}

            # التحقق من حد البيانات إذا تم إدخاله
            limit_bytes_gb = new_data.get('limit_bytes_gb', '').strip()
            if limit_bytes_gb:
                try:
                    limit_gb = float(limit_bytes_gb)
                    if limit_gb < 0:
                        return {'valid': False, 'message': 'حد البيانات يجب أن يكون رقماً موجباً'}
                    if limit_gb > 1000:  # حد أقصى معقول 1000 GB
                        return {'valid': False, 'message': 'حد البيانات كبير جداً (الحد الأقصى 1000 GB)'}
                except ValueError:
                    return {'valid': False, 'message': 'حد البيانات يجب أن يكون رقماً صحيحاً (مثال: 5.5)'}

            # التحقق من الإيميل إذا تم إدخاله (تحقق مرن)
            email = new_data.get('email', '').strip()
            if email:
                # تحقق بسيط ومرن من صحة الإيميل
                if '@' not in email:
                    return {'valid': False, 'message': 'صيغة الإيميل يجب أن تحتوي على @'}

            # التحقق من الحالة
            status = new_data.get('status', '').strip()
            if status and status not in ['نشط', 'غير نشط']:
                return {'valid': False, 'message': 'الحالة يجب أن تكون "نشط" أو "غير نشط"'}

            return {'valid': True, 'message': 'البيانات صحيحة'}

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من صحة البيانات: {str(e)}")
            return {'valid': False, 'message': f'خطأ في التحقق من البيانات: {str(e)}'}

    def um_get_changes_summary(self, original_data, new_data):
        """الحصول على ملخص التغييرات"""
        try:
            changes = []
            has_changes = False

            # مقارنة الحقول العادية (مخصص لـ User Manager)
            fields_to_compare = ['username', 'password', 'profile', 'comment', 'location', 'email', 'status']

            for field in fields_to_compare:
                original_value = original_data.get(field, '').strip()
                new_value = new_data.get(field, '').strip()

                if original_value != new_value:
                    has_changes = True
                    field_names = {
                        'username': 'اسم المستخدم',
                        'password': 'كلمة السر',
                        'profile': 'البروفايل',
                        'comment': 'التعليق',
                        'location': 'الموقع',
                        'email': 'الإيميل',
                        'status': 'الحالة'
                    }

                    field_name = field_names.get(field, field)

                    # إخفاء كلمة السر في الملخص
                    if field == 'password':
                        if original_value and new_value:
                            changes.append(f"• {field_name}: تم تغيير كلمة السر")
                        elif not original_value and new_value:
                            changes.append(f"• {field_name}: تم إضافة كلمة سر")
                        elif original_value and not new_value:
                            changes.append(f"• {field_name}: تم حذف كلمة السر")
                    else:
                        changes.append(f"• {field_name}: من '{original_value}' إلى '{new_value}'")

            # مقارنة حد البيانات
            original_limit_gb = ""
            if original_data.get('limit_bytes_total'):
                try:
                    limit_bytes = int(original_data.get('limit_bytes_total', 0))
                    if limit_bytes > 0:
                        original_limit_gb = str(round(limit_bytes / (1024 * 1024 * 1024), 2))
                except (ValueError, TypeError):
                    original_limit_gb = ""

            new_limit_gb = new_data.get('limit_bytes_gb', '').strip()

            if original_limit_gb != new_limit_gb:
                has_changes = True
                if not original_limit_gb and new_limit_gb:
                    changes.append(f"• حد البيانات: تم إضافة حد {new_limit_gb} GB")
                elif original_limit_gb and not new_limit_gb:
                    changes.append(f"• حد البيانات: تم إزالة الحد ({original_limit_gb} GB)")
                elif original_limit_gb and new_limit_gb:
                    changes.append(f"• حد البيانات: من {original_limit_gb} GB إلى {new_limit_gb} GB")

            summary = '\n'.join(changes) if changes else 'لا توجد تغييرات'

            return {
                'has_changes': has_changes,
                'summary': summary,
                'changes_count': len(changes)
            }

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ملخص التغييرات: {str(e)}")
            return {'has_changes': False, 'summary': 'خطأ في تحليل التغييرات', 'changes_count': 0}

    def um_update_user_in_mikrotik(self, original_data, new_data, progress_window):
        """تحديث بيانات المستخدم في MikroTik User Manager"""
        try:
            progress_window.update_status("🌐 الاتصال بـ MikroTik...")

            api = self.connect_api()
            if not api:
                raise Exception("فشل في الاتصال بـ MikroTik")

            # تحديد الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            progress_window.update_status("🔍 البحث عن المستخدم...")

            # البحث عن المستخدم في MikroTik
            if version_value == "v6":
                resource_path = '/tool/user-manager/user'
            else:
                resource_path = '/user-manager/user'

            resource = api.get_resource(resource_path)

            original_username = original_data.get('username', '')
            users_found = resource.get(username=original_username)

            if not users_found:
                raise Exception(f"لم يتم العثور على المستخدم: {original_username}")

            user_to_update = users_found[0]
            user_id = user_to_update['id']

            progress_window.update_status("💾 حفظ التغييرات...")

            # إعداد البيانات للتحديث
            update_params = {'id': user_id}

            # تحديث الحقول المتغيرة
            if new_data['username'] != original_data.get('username', ''):
                update_params['username'] = new_data['username']

            if new_data['password'] != original_data.get('password', ''):
                update_params['password'] = new_data['password']

            if new_data['profile'] != original_data.get('profile', ''):
                update_params['profile'] = new_data['profile']

            if new_data['comment'] != original_data.get('comment', ''):
                update_params['comment'] = new_data['comment']

            if new_data['location'] != original_data.get('location', ''):
                update_params['location'] = new_data['location']

            if new_data['email'] != original_data.get('email', ''):
                update_params['email'] = new_data['email']

            # تحديث حد البيانات
            original_limit_gb = ""
            if original_data.get('limit_bytes_total'):
                try:
                    limit_bytes = int(original_data.get('limit_bytes_total', 0))
                    if limit_bytes > 0:
                        original_limit_gb = str(round(limit_bytes / (1024 * 1024 * 1024), 2))
                except (ValueError, TypeError):
                    original_limit_gb = ""

            new_limit_gb = new_data.get('limit_bytes_gb', '').strip()

            if original_limit_gb != new_limit_gb:
                if new_limit_gb:
                    try:
                        # تحويل من GB إلى bytes
                        limit_gb = float(new_limit_gb)
                        limit_bytes = int(limit_gb * 1024 * 1024 * 1024)
                        update_params['limit-bytes-total'] = str(limit_bytes)
                        self.logger.info(f"تحديث حد البيانات: {new_limit_gb} GB = {limit_bytes} bytes")
                    except ValueError:
                        self.logger.warning(f"قيمة حد البيانات غير صحيحة: {new_limit_gb}")
                else:
                    # إزالة حد البيانات
                    update_params['limit-bytes-total'] = ""
                    self.logger.info("إزالة حد البيانات")

            # تحديث الحالة
            if new_data['status'] != original_data.get('status', ''):
                disabled_value = "true" if new_data['status'] == 'غير نشط' else "false"
                update_params['disabled'] = disabled_value

            # تطبيق التحديثات
            if len(update_params) > 1:  # أكثر من مجرد ID
                resource.set(**update_params)
                self.logger.info(f"تم تحديث المستخدم في MikroTik: {new_data['username']}")

                progress_window.update_status("✅ تم الحفظ بنجاح")
                return True
            else:
                self.logger.info("لا توجد تغييرات لحفظها في MikroTik")
                return True

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"خطأ في تحديث المستخدم في MikroTik: {error_msg}")

            # تحليل نوع الخطأ
            if "not found" in error_msg.lower():
                raise Exception(f"المستخدم غير موجود في MikroTik: {original_data.get('username', '')}")
            elif "permission" in error_msg.lower():
                raise Exception("ليس لديك صلاحية لتعديل المستخدمين في MikroTik")
            elif "duplicate" in error_msg.lower() or "already exists" in error_msg.lower():
                raise Exception(f"اسم المستخدم موجود مسبقاً: {new_data['username']}")
            else:
                raise Exception(f"خطأ في MikroTik: {error_msg}")

    def um_update_local_user_data(self, original_data, new_data):
        """تحديث البيانات المحلية للمستخدم"""
        try:
            # تحديث البيانات في جميع القوائم
            for user_list in [self.um_all_users_data, self.um_filtered_users_data, self.um_selected_users_data]:
                for user in user_list:
                    if user == original_data:
                        # تحديث البيانات العادية
                        for field, value in new_data.items():
                            if field != 'limit_bytes_gb':  # تجاهل حقل GB المؤقت
                                user[field] = value

                        # تحديث حد البيانات بالـ bytes
                        limit_gb = new_data.get('limit_bytes_gb', '').strip()
                        if limit_gb:
                            try:
                                limit_gb_float = float(limit_gb)
                                limit_bytes = int(limit_gb_float * 1024 * 1024 * 1024)
                                user['limit_bytes_total'] = str(limit_bytes)
                            except ValueError:
                                # إزالة حد البيانات إذا كانت القيمة غير صحيحة
                                user.pop('limit_bytes_total', None)
                        else:
                            # إزالة حد البيانات إذا كان الحقل فارغاً
                            user.pop('limit_bytes_total', None)

                        break

            self.logger.info(f"تم تحديث البيانات المحلية للمستخدم: {new_data['username']}")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث البيانات المحلية: {str(e)}")

    # ===== نهاية دوال User Manager Cards View =====

    # ===== دوال Hotspot Cards View =====

    def hs_on_search_change(self, event=None):
        """تنفيذ البحث عند تغيير أي حقل بحث في Hotspot"""
        try:
            if not self.hs_all_users_data:
                return

            # جمع معايير البحث
            search_criteria = {
                'username': self.hs_search_username.get().strip().lower(),
                'profile': self.hs_search_profile.get().strip().lower(),
                'comment': self.hs_search_comment.get().strip().lower(),
                'server': self.hs_search_server.get().strip().lower(),
                'email': self.hs_search_email.get().strip().lower(),
                'status': self.hs_search_status.get()
            }

            # تطبيق الفلترة
            self.hs_filtered_users_data = []
            for user in self.hs_all_users_data:
                match = True

                # فحص كل معيار بحث
                if search_criteria['username'] and search_criteria['username'] not in user.get('username', '').lower():
                    match = False
                if search_criteria['profile'] and search_criteria['profile'] not in user.get('profile', '').lower():
                    match = False
                if search_criteria['comment'] and search_criteria['comment'] not in user.get('comment', '').lower():
                    match = False
                if search_criteria['server'] and search_criteria['server'] not in user.get('server', '').lower():
                    match = False
                if search_criteria['email'] and search_criteria['email'] not in user.get('email', '').lower():
                    match = False
                if search_criteria['status'] != "الكل" and search_criteria['status'] != user.get('status', ''):
                    match = False

                if match:
                    self.hs_filtered_users_data.append(user)

            # تحديث العرض
            self.hs_update_treeview_display()
            self.hs_update_search_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في البحث (Hotspot): {str(e)}")

    def hs_clear_search(self):
        """مسح جميع حقول البحث في Hotspot"""
        try:
            self.hs_search_username.delete(0, tk.END)
            self.hs_search_profile.delete(0, tk.END)
            self.hs_search_comment.delete(0, tk.END)
            self.hs_search_server.delete(0, tk.END)
            self.hs_search_email.delete(0, tk.END)
            self.hs_search_status.set("الكل")

            # إعادة تعيين البيانات المفلترة
            self.hs_filtered_users_data = self.hs_all_users_data.copy()
            self.hs_update_treeview_display()
            self.hs_update_search_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في مسح البحث (Hotspot): {str(e)}")

    def hs_update_treeview_display(self):
        """تحديث عرض الجدول في Hotspot"""
        try:
            # مسح الجدول الحالي
            for item in self.hs_cards_tree.get_children():
                self.hs_cards_tree.delete(item)

            # إضافة البيانات المفلترة
            for user in self.hs_filtered_users_data:
                # تحديد رمز التحديد
                is_selected = user in self.hs_selected_users_data
                select_symbol = "☑" if is_selected else "☐"

                values = (
                    select_symbol,
                    user.get('username', ''),
                    user.get('password', ''),
                    user.get('profile', ''),
                    user.get('server', ''),
                    user.get('comment', ''),
                    user.get('email', ''),
                    user.get('status', 'نشط')
                )

                self.hs_cards_tree.insert("", "end", values=values)

        except Exception as e:
            self.logger.error(f"خطأ في تحديث عرض الجدول (Hotspot): {str(e)}")

    def hs_update_search_statistics(self):
        """تحديث إحصائيات البحث في Hotspot"""
        try:
            total = len(self.hs_all_users_data)
            filtered = len(self.hs_filtered_users_data)

            # حساب النشطين وغير النشطين
            active = len([u for u in self.hs_all_users_data if u.get('status', '') == 'نشط'])
            inactive = total - active

            # تحديث التسميات
            self.hs_total_label.configure(text=f"المجموع: {total}")
            self.hs_active_label.configure(text=f"نشط: {active}")
            self.hs_inactive_label.configure(text=f"غير نشط: {inactive}")
            self.hs_filtered_label.configure(text=f"المفلتر: {filtered}")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات (Hotspot): {str(e)}")

    def hs_fetch_all_users(self):
        """جلب جميع المستخدمين من MikroTik Hotspot"""
        try:
            api = self.connect_api()
            if not api:
                return

            self.logger.info("بدء جلب مستخدمي Hotspot")

            # إنشاء نافذة التقدم
            progress_window = tk.Toplevel(self.root)
            progress_window.title("جلب البيانات")
            progress_window.geometry("400x150")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # إعداد نافذة التقدم
            ttk.Label(progress_window, text="جاري جلب بيانات Hotspot...",
                     font=self.fonts['arabic']).pack(pady=20)

            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=tk.X)
            progress_bar.start()

            status_label = ttk.Label(progress_window, text="الاتصال بالخادم...",
                                   font=self.fonts['arabic'])
            status_label.pack(pady=5)

            # تحديث الواجهة
            self.root.update()

            # جلب البيانات
            users_data = self.hs_fetch_hotspot_users(api, status_label)

            # حفظ البيانات
            self.hs_all_users_data = users_data
            self.hs_filtered_users_data = users_data.copy()
            self.hs_selected_users_data.clear()

            # تحديث الجدول والإحصائيات
            self.hs_update_treeview_display()
            self.hs_update_search_statistics()
            self.hs_update_selected_count()

            # إغلاق نافذة التقدم
            progress_window.destroy()

            messagebox.showinfo("نجح", f"تم جلب {len(users_data)} مستخدم من Hotspot بنجاح")
            self.logger.info(f"تم جلب {len(users_data)} مستخدم من Hotspot بنجاح")

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()

            error_msg = str(e)
            self.logger.error(f"خطأ في جلب مستخدمي Hotspot: {error_msg}")
            messagebox.showerror("خطأ", f"فشل في جلب المستخدمين: {error_msg}")

    def hs_fetch_hotspot_users(self, api, status_label):
        """جلب مستخدمي Hotspot مع معالجة آمنة"""
        try:
            status_label.configure(text="جلب مستخدمي Hotspot...")
            self.root.update()

            # تحديد المسار حسب الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'  # افتراضي

            self.logger.info(f"جلب مستخدمي Hotspot - الإصدار: {version_value}")

            # تحديد المسار والحصول على البيانات
            resource_path = '/ip/hotspot/user'
            status_label.configure(text=f"جلب البيانات من Hotspot {version_value}...")

            self.root.update()
            self.logger.info(f"استخدام المسار: {resource_path}")

            # جلب البيانات من MikroTik
            users = api.get_resource(resource_path).get()
            self.logger.info(f"تم جلب {len(users)} مستخدم من Hotspot")

            status_label.configure(text=f"معالجة {len(users)} مستخدم...")
            self.root.update()

            users_data = []
            for i, user in enumerate(users):
                # تحديث حالة التقدم كل 10 مستخدمين
                if i % 10 == 0:
                    status_label.configure(text=f"معالجة المستخدم {i+1}/{len(users)}...")
                    self.root.update()

                try:
                    # معالجة آمنة لجميع الحقول
                    username = user.get('name', '')
                    password = user.get('password', '')
                    profile = user.get('profile', '')
                    server = user.get('server', '')
                    comment = user.get('comment', '')
                    email = user.get('email', '')

                    # معالجة حالة المستخدم
                    disabled = user.get('disabled', 'false')
                    if isinstance(disabled, bool):
                        status = 'غير نشط' if disabled else 'نشط'
                    else:
                        status = 'غير نشط' if str(disabled).lower() == 'true' else 'نشط'

                    user_data = {
                        'username': str(username) if username else '',
                        'password': str(password) if password else '',
                        'profile': str(profile) if profile else '',
                        'server': str(server) if server else '',
                        'comment': str(comment) if comment else '',
                        'email': str(email) if email else '',
                        'status': status
                    }

                except Exception as user_error:
                    self.logger.warning(f"خطأ في معالجة المستخدم {i+1}: {str(user_error)}")
                    # إنشاء بيانات افتراضية في حالة الخطأ
                    user_data = {
                        'username': f'user_{i+1}',
                        'password': '',
                        'profile': '',
                        'server': '',
                        'comment': '',
                        'email': '',
                        'status': 'نشط'
                    }

                users_data.append(user_data)

            self.logger.info(f"تم معالجة {len(users_data)} مستخدم بنجاح")
            status_label.configure(text="اكتمل جلب البيانات...")
            self.root.update()

            return users_data

        except Exception as e:
            error_msg = f"خطأ في جلب مستخدمي Hotspot: {str(e)}"
            self.logger.error(error_msg)
            status_label.configure(text="فشل في جلب البيانات!")
            self.root.update()
            raise Exception(error_msg)

    def hs_on_tree_click(self, event):
        """معالجة النقر على الجدول في Hotspot"""
        try:
            # تحديد العنصر المنقور عليه
            item = self.hs_cards_tree.identify_row(event.y)
            if not item:
                return

            # الحصول على بيانات المستخدم
            values = self.hs_cards_tree.item(item, 'values')
            if not values:
                return

            # البحث عن المستخدم في البيانات المفلترة
            username = values[1]  # تجاهل عمود التحديد
            user_data = None
            for user in self.hs_filtered_users_data:
                if user.get('username', '') == username:
                    user_data = user
                    break

            if not user_data:
                return

            # تبديل حالة التحديد
            if user_data in self.hs_selected_users_data:
                self.hs_selected_users_data.remove(user_data)
            else:
                self.hs_selected_users_data.append(user_data)

            # تحديث العرض
            self.hs_update_treeview_display()
            self.hs_update_selected_count()

        except Exception as e:
            self.logger.error(f"خطأ في النقر على الجدول (Hotspot): {str(e)}")

    def hs_toggle_select_all(self):
        """تبديل تحديد جميع المستخدمين في Hotspot"""
        try:
            if self.hs_select_all_var.get():
                # تحديد الكل
                self.hs_selected_users_data = self.hs_filtered_users_data.copy()
            else:
                # إلغاء تحديد الكل
                self.hs_selected_users_data.clear()

            # تحديث العرض
            self.hs_update_treeview_display()
            self.hs_update_selected_count()

        except Exception as e:
            self.logger.error(f"خطأ في تحديد الكل (Hotspot): {str(e)}")

    def hs_update_selected_count(self):
        """تحديث عداد المستخدمين المحددين في Hotspot"""
        try:
            count = len(self.hs_selected_users_data)
            self.hs_selected_count_label.configure(text=f"المحدد: {count}")

            # تحديث حالة checkbox تحديد الكل
            if count == 0:
                self.hs_select_all_var.set(False)
            elif count == len(self.hs_filtered_users_data):
                self.hs_select_all_var.set(True)
            else:
                # حالة جزئية
                self.hs_select_all_var.set(False)

            # تحديث حالة زر التعديل - يكون فعال فقط عند تحديد مستخدم واحد بالضبط
            if hasattr(self, 'hs_edit_user_button'):
                if count == 1:
                    self.hs_edit_user_button.configure(state="normal")
                else:
                    self.hs_edit_user_button.configure(state="disabled")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث عداد المحددين (Hotspot): {str(e)}")

    def hs_fetch_profiles(self):
        """جلب البروفايلات من MikroTik لتبويب Hotspot"""
        try:
            api = self.connect_api()
            if not api:
                messagebox.showerror("خطأ", "فشل في الاتصال بـ MikroTik")
                return

            # تحديد الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            self.logger.info(f"بدء جلب البروفايلات من Hotspot {version_value}")

            # تحديد المسار حسب الإصدار
            resource_path = '/ip/hotspot/user/profile'

            # جلب البروفايلات
            profiles = api.get_resource(resource_path).get()
            profile_names = [profile.get('name', '') for profile in profiles if profile.get('name')]

            if profile_names:
                # تحديث قائمة البروفايلات في حقل البحث
                # يمكن إضافة combobox للبروفايلات لاحقاً إذا لزم الأمر
                messagebox.showinfo("نجح", f"تم جلب {len(profile_names)} بروفايل بنجاح")
                self.logger.info(f"تم جلب البروفايلات: {', '.join(profile_names)}")

                # يمكن إضافة المزيد من الوظائف هنا مثل تحديث قائمة منسدلة للبروفايلات

            else:
                messagebox.showwarning("تحذير", "لم يتم العثور على أي بروفايلات")
                self.logger.warning("لم يتم العثور على أي بروفايلات في Hotspot")

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"خطأ في جلب البروفايلات (Hotspot): {error_msg}")

            # تحليل نوع الخطأ وتقديم رسالة مناسبة
            if "permission" in error_msg.lower() or "access" in error_msg.lower():
                messagebox.showerror("خطأ في الصلاحيات",
                                   f"فشل في جلب البروفايلات بسبب عدم وجود صلاحيات كافية\n\n"
                                   f"تأكد من أن المستخدم لديه صلاحية الوصول إلى Hotspot\n\n"
                                   f"تفاصيل الخطأ: {error_msg}")
            elif "not found" in error_msg.lower():
                messagebox.showerror("خطأ في النظام",
                                   f"Hotspot غير مفعل أو غير موجود\n\n"
                                   f"تأكد من تفعيل Hotspot في MikroTik\n\n"
                                   f"تفاصيل الخطأ: {error_msg}")
            else:
                messagebox.showerror("خطأ", f"فشل في جلب البروفايلات: {error_msg}")

    def hs_enable_selected_users(self):
        """تفعيل المستخدمين المحددين في Hotspot"""
        try:
            if not self.hs_selected_users_data:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي مستخدمين للتفعيل")
                return

            count = len(self.hs_selected_users_data)
            response = messagebox.askyesno(
                "تأكيد التفعيل",
                f"هل تريد تفعيل {count} مستخدم؟"
            )

            if response:
                success_count = self.hs_change_users_status(self.hs_selected_users_data, enable=True)

                if success_count > 0:
                    messagebox.showinfo("نجح", f"تم تفعيل {success_count} مستخدم بنجاح")
                    # تحديث البيانات المحلية
                    for user in self.hs_selected_users_data:
                        user['status'] = 'نشط'
                    self.hs_update_treeview_display()
                    self.hs_update_search_statistics()
                else:
                    messagebox.showwarning("تحذير", "لم يتم تفعيل أي مستخدم")

        except Exception as e:
            self.logger.error(f"خطأ في تفعيل المستخدمين المحددين (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تفعيل المستخدمين: {str(e)}")

    def hs_disable_selected_users(self):
        """تعطيل المستخدمين المحددين في Hotspot"""
        try:
            if not self.hs_selected_users_data:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي مستخدمين للتعطيل")
                return

            count = len(self.hs_selected_users_data)
            response = messagebox.askyesno(
                "تأكيد التعطيل",
                f"هل تريد تعطيل {count} مستخدم؟"
            )

            if response:
                success_count = self.hs_change_users_status(self.hs_selected_users_data, enable=False)

                if success_count > 0:
                    messagebox.showinfo("نجح", f"تم تعطيل {success_count} مستخدم بنجاح")
                    # تحديث البيانات المحلية
                    for user in self.hs_selected_users_data:
                        user['status'] = 'غير نشط'
                    self.hs_update_treeview_display()
                    self.hs_update_search_statistics()
                else:
                    messagebox.showwarning("تحذير", "لم يتم تعطيل أي مستخدم")

        except Exception as e:
            self.logger.error(f"خطأ في تعطيل المستخدمين المحددين (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تعطيل المستخدمين: {str(e)}")

    def hs_change_users_status(self, users_to_change, enable=True):
        """تغيير حالة المستخدمين في MikroTik Hotspot باستخدام الأوامر الصحيحة"""
        try:
            api = self.connect_api()
            if not api:
                self.logger.error("فشل في الاتصال بـ MikroTik API")
                return 0

            success_count = 0
            failed_users = []
            connection_errors = 0
            permission_errors = 0
            not_found_errors = 0

            # تحديد الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            action_text = "تفعيل" if enable else "تعطيل"
            self.logger.info(f"بدء {action_text} {len(users_to_change)} مستخدم في Hotspot {version_value}")

            for i, user in enumerate(users_to_change, 1):
                try:
                    username = user.get('username', '')
                    if not username:
                        self.logger.warning(f"تم تجاهل المستخدم #{i} بدون اسم مستخدم")
                        continue

                    self.logger.info(f"معالجة المستخدم {i}/{len(users_to_change)}: {username}")

                    # تحديد المسار حسب الإصدار
                    resource_path = '/ip/hotspot/user'
                    search_param = 'name'

                    resource = api.get_resource(resource_path)
                    user_found = False

                    try:
                        # البحث عن المستخدم
                        users_found = resource.get(**{search_param: username})

                        if users_found:
                            user_found = True
                            for existing_user in users_found:
                                user_id = existing_user['id']
                                self.logger.info(f"تم العثور على المستخدم {username} بـ ID: {user_id}")

                                # تحويل القيمة Boolean إلى string كما يتوقع MikroTik API
                                disabled_value = "true" if not enable else "false"

                                # تغيير حالة المستخدم باستخدام القيم الصحيحة
                                resource.set(id=user_id, disabled=disabled_value)
                                success_count += 1
                                self.logger.info(f"تم {action_text} المستخدم بنجاح: {username} (ID: {user_id})")
                                break  # نحتاج فقط لتعديل أول مطابقة

                        else:
                            # محاولة البحث بطريقة أخرى
                            all_users = resource.get()
                            for existing_user in all_users:
                                user_name = existing_user.get('name', '')
                                if user_name == username:
                                    user_id = existing_user['id']
                                    disabled_value = "true" if not enable else "false"
                                    resource.set(id=user_id, disabled=disabled_value)
                                    success_count += 1
                                    self.logger.info(f"تم {action_text} المستخدم بنجاح (بحث شامل): {username}")
                                    user_found = True
                                    break

                        if not user_found:
                            failed_users.append(username)
                            not_found_errors += 1
                            self.logger.error(f"لم يتم العثور على المستخدم: {username}")

                    except Exception as search_error:
                        failed_users.append(username)
                        error_msg = str(search_error)

                        # تصنيف نوع الخطأ
                        if "permission" in error_msg.lower() or "access" in error_msg.lower():
                            permission_errors += 1
                            self.logger.error(f"خطأ في الصلاحيات للمستخدم {username}: {error_msg}")
                        elif "connection" in error_msg.lower() or "timeout" in error_msg.lower():
                            connection_errors += 1
                            self.logger.error(f"خطأ في الاتصال للمستخدم {username}: {error_msg}")
                        else:
                            self.logger.error(f"خطأ في معالجة المستخدم {username}: {error_msg}")

                except Exception as user_error:
                    failed_users.append(username)
                    self.logger.error(f"خطأ عام في معالجة المستخدم {username}: {str(user_error)}")

            # تسجيل النتائج التفصيلية
            self.logger.info(f"انتهى {action_text} المستخدمين:")
            self.logger.info(f"  - نجح: {success_count}")
            self.logger.info(f"  - فشل: {len(failed_users)}")
            self.logger.info(f"  - أخطاء الاتصال: {connection_errors}")
            self.logger.info(f"  - أخطاء الصلاحيات: {permission_errors}")
            self.logger.info(f"  - مستخدمون غير موجودون: {not_found_errors}")

            if failed_users:
                self.logger.warning(f"المستخدمون الذين فشل {action_text}هم: {', '.join(failed_users)}")

            return success_count

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"خطأ عام في تغيير حالة المستخدمين: {error_msg}")
            return 0

    def hs_delete_selected_users(self):
        """حذف المستخدمين المحددين في Hotspot"""
        try:
            if not self.hs_selected_users_data:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي مستخدمين للحذف")
                return

            count = len(self.hs_selected_users_data)
            response = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف {count} مستخدم؟\nهذا الإجراء لا يمكن التراجع عنه."
            )

            if response:
                success_count = self.hs_delete_users_from_mikrotik(self.hs_selected_users_data)

                if success_count > 0:
                    messagebox.showinfo("نجح", f"تم حذف {success_count} مستخدم بنجاح")

                    # إزالة من البيانات المحلية
                    for user in self.hs_selected_users_data:
                        if user in self.hs_all_users_data:
                            self.hs_all_users_data.remove(user)
                        if user in self.hs_filtered_users_data:
                            self.hs_filtered_users_data.remove(user)

                    # مسح التحديد
                    self.hs_selected_users_data.clear()

                    # تحديث الجدول
                    self.hs_update_treeview_display()
                    self.hs_update_search_statistics()
                    self.hs_update_selected_count()
                else:
                    messagebox.showwarning("تحذير", "لم يتم حذف أي مستخدم")

        except Exception as e:
            self.logger.error(f"خطأ في حذف المستخدمين المحددين (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في حذف المستخدمين: {str(e)}")

    def hs_delete_users_from_mikrotik(self, users_to_delete):
        """حذف المستخدمين من MikroTik Hotspot"""
        try:
            api = self.connect_api()
            if not api:
                return 0

            success_count = 0

            # تحديد المسار حسب الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            resource_path = '/ip/hotspot/user'
            resource = api.get_resource(resource_path)

            for user in users_to_delete:
                try:
                    username = user.get('username', '')
                    if username:
                        # البحث عن المستخدم في MikroTik
                        existing_users = resource.get(name=username)

                        for existing_user in existing_users:
                            # حذف المستخدم
                            resource.remove(id=existing_user['id'])
                            success_count += 1
                            self.logger.info(f"تم حذف المستخدم: {username}")

                except Exception as user_error:
                    self.logger.error(f"خطأ في حذف المستخدم {username}: {str(user_error)}")
                    continue

            return success_count

        except Exception as e:
            self.logger.error(f"خطأ في حذف المستخدمين من MikroTik: {str(e)}")
            return 0

    def hs_export_selected_users(self):
        """تصدير المستخدمين المحددين في Hotspot"""
        try:
            if not self.hs_selected_users_data:
                messagebox.showwarning("تحذير", "لم يتم تحديد أي مستخدمين للتصدير")
                return

            # حفظ البيانات المفلترة الحالية
            original_filtered = self.hs_filtered_users_data.copy()

            # تعيين المحددين كبيانات مفلترة مؤقتاً
            self.hs_filtered_users_data = self.hs_selected_users_data.copy()

            # استخدام دالة التصدير الموجودة
            self.hs_export_search_results()

            # استعادة البيانات المفلترة الأصلية
            self.hs_filtered_users_data = original_filtered

        except Exception as e:
            self.logger.error(f"خطأ في تصدير المستخدمين المحددين (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير المستخدمين: {str(e)}")

    def hs_export_search_results(self):
        """تصدير نتائج البحث في Hotspot"""
        try:
            if not self.hs_filtered_users_data:
                messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
                return

            # نافذة اختيار نوع التصدير
            export_window = tk.Toplevel(self.root)
            export_window.title("تصدير نتائج البحث")
            export_window.geometry("350x200")
            export_window.transient(self.root)
            export_window.grab_set()

            ttk.Label(export_window, text="اختر نوع الملف للتصدير:",
                     font=self.fonts['arabic_heading']).pack(pady=10)

            export_type = tk.StringVar(value="csv")

            # خيارات التصدير
            ttk.Radiobutton(export_window, text="CSV (Excel)",
                           variable=export_type, value="csv").pack(anchor="w", padx=20, pady=2)
            ttk.Radiobutton(export_window, text="JSON (البيانات المنظمة)",
                           variable=export_type, value="json").pack(anchor="w", padx=20, pady=2)
            ttk.Radiobutton(export_window, text="TXT (نص عادي)",
                           variable=export_type, value="txt").pack(anchor="w", padx=20, pady=2)

            # معلومات التصدير
            info_label = ttk.Label(export_window,
                                 text=f"سيتم تصدير {len(self.hs_filtered_users_data)} مستخدم",
                                 font=self.fonts['arabic'])
            info_label.pack(pady=10)

            # أزرار التحكم
            button_frame = ttk.Frame(export_window)
            button_frame.pack(pady=20)

            ttk.Button(button_frame, text="تصدير",
                      command=lambda: self.hs_perform_export_search_results(export_type.get(), export_window)).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="إلغاء",
                      command=export_window.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في نافذة التصدير (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التصدير: {str(e)}")

    def hs_perform_export_search_results(self, export_type, window):
        """تنفيذ تصدير نتائج البحث في Hotspot"""
        try:
            window.destroy()

            # تحديد امتداد الملف
            if export_type == "csv":
                file_ext = ".csv"
                file_types = [("CSV files", "*.csv")]
            elif export_type == "json":
                file_ext = ".json"
                file_types = [("JSON files", "*.json")]
            else:  # txt
                file_ext = ".txt"
                file_types = [("Text files", "*.txt")]

            # اختيار مكان الحفظ
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"hotspot_search_results_{timestamp}{file_ext}"

            filename = filedialog.asksaveasfilename(
                defaultextension=file_ext,
                filetypes=file_types,
                initialfile=default_name
            )

            if not filename:
                return

            # تصدير البيانات
            if export_type == "csv":
                self.hs_export_search_to_csv(filename)
            elif export_type == "json":
                self.hs_export_search_to_json(filename)
            else:
                self.hs_export_search_to_txt(filename)

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ التصدير (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في التصدير: {str(e)}")

    def hs_export_search_to_csv(self, filename):
        """تصدير نتائج البحث إلى CSV في Hotspot"""
        try:
            import csv

            with open(filename, 'w', newline='', encoding='utf-8') as file:
                fieldnames = ['username', 'password', 'profile', 'server', 'comment', 'email', 'status']
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()

                for user in self.hs_filtered_users_data:
                    row = {field: user.get(field, '') for field in fieldnames}
                    writer.writerow(row)

            messagebox.showinfo("نجح", f"تم تصدير {len(self.hs_filtered_users_data)} مستخدم إلى CSV بنجاح")
            self.logger.info(f"تم تصدير {len(self.hs_filtered_users_data)} مستخدم إلى CSV")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير CSV (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير CSV: {str(e)}")

    def hs_export_search_to_json(self, filename):
        """تصدير نتائج البحث إلى JSON في Hotspot"""
        try:
            from datetime import datetime
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'system_type': 'hotspot',
                    'total_users': len(self.hs_filtered_users_data),
                    'search_criteria': getattr(self, 'hs_last_search_criteria', {})
                },
                'users': self.hs_filtered_users_data
            }

            with open(filename, 'w', encoding='utf-8') as file:
                json.dump(export_data, file, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", f"تم تصدير {len(self.hs_filtered_users_data)} مستخدم إلى JSON بنجاح")
            self.logger.info(f"تم تصدير {len(self.hs_filtered_users_data)} مستخدم إلى JSON")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير JSON (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير JSON: {str(e)}")

    def hs_export_search_to_txt(self, filename):
        """تصدير نتائج البحث إلى TXT في Hotspot"""
        try:
            from datetime import datetime
            with open(filename, 'w', encoding='utf-8') as file:
                # كتابة معلومات التصدير
                file.write(f"تقرير مستخدمي Hotspot\n")
                file.write(f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                file.write(f"عدد المستخدمين: {len(self.hs_filtered_users_data)}\n")
                file.write("=" * 80 + "\n\n")

                # كتابة بيانات المستخدمين
                for i, user in enumerate(self.hs_filtered_users_data, 1):
                    file.write(f"المستخدم #{i}:\n")
                    file.write(f"  اسم المستخدم: {user.get('username', '')}\n")
                    file.write(f"  كلمة السر: {user.get('password', '')}\n")
                    file.write(f"  البروفايل: {user.get('profile', '')}\n")
                    file.write(f"  الخادم: {user.get('server', '')}\n")
                    file.write(f"  التعليق: {user.get('comment', '')}\n")
                    file.write(f"  الإيميل: {user.get('email', '')}\n")
                    file.write(f"  الحالة: {user.get('status', '')}\n")
                    file.write("-" * 40 + "\n")

            messagebox.showinfo("نجح", f"تم تصدير {len(self.hs_filtered_users_data)} مستخدم إلى TXT بنجاح")
            self.logger.info(f"تم تصدير {len(self.hs_filtered_users_data)} مستخدم إلى TXT")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير TXT (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير TXT: {str(e)}")

    def hs_on_user_double_click(self, event):
        """معالجة النقر المزدوج على مستخدم في Hotspot"""
        try:
            selection = self.hs_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.hs_cards_tree.item(item, 'values')

            if not values:
                return

            # إنشاء نافذة تفاصيل المستخدم
            self.hs_show_user_details(values)

        except Exception as e:
            self.logger.error(f"خطأ في معالجة النقر المزدوج (Hotspot): {str(e)}")

    def hs_show_context_menu(self, event):
        """عرض القائمة السياقية في Hotspot"""
        try:
            # تحديد العنصر المنقور عليه
            item = self.hs_cards_tree.identify_row(event.y)
            if item:
                self.hs_cards_tree.selection_set(item)

                # إنشاء القائمة السياقية
                context_menu = tk.Menu(self.root, tearoff=0)
                context_menu.add_command(label="عرض التفاصيل", command=self.hs_show_selected_user_details)
                context_menu.add_separator()
                context_menu.add_command(label="تفعيل المستخدم", command=self.hs_enable_single_user)
                context_menu.add_command(label="تعطيل المستخدم", command=self.hs_disable_single_user)
                context_menu.add_separator()
                context_menu.add_command(label="نسخ اسم المستخدم", command=self.hs_copy_username)
                context_menu.add_command(label="نسخ كلمة السر", command=self.hs_copy_password)
                context_menu.add_separator()
                context_menu.add_command(label="حذف المستخدم", command=self.hs_delete_single_user)

                # عرض القائمة
                context_menu.tk_popup(event.x_root, event.y_root)

        except Exception as e:
            self.logger.error(f"خطأ في عرض القائمة السياقية (Hotspot): {str(e)}")

    def hs_show_user_details(self, values):
        """عرض تفاصيل المستخدم في نافذة منفصلة في Hotspot"""
        try:
            details_window = tk.Toplevel(self.root)
            details_window.title("تفاصيل المستخدم - Hotspot")
            details_window.geometry("500x400")
            details_window.transient(self.root)
            details_window.grab_set()

            # إطار التفاصيل
            details_frame = ttk.LabelFrame(details_window, text="معلومات المستخدم", padding=15)
            details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # عرض التفاصيل (تجاهل عمود التحديد الأول)
            labels = ["اسم المستخدم:", "كلمة السر:", "البروفايل:", "الخادم:", "التعليق:", "الإيميل:", "الحالة:"]
            user_values = values[1:]  # تجاهل عمود التحديد

            for i, (label, value) in enumerate(zip(labels, user_values)):
                ttk.Label(details_frame, text=label, font=self.fonts['arabic']).grid(
                    row=i, column=0, sticky="e", padx=5, pady=5)

                # إنشاء حقل نص قابل للتحديد
                value_entry = ttk.Entry(details_frame, width=40, font=self.fonts['arabic'])
                value_entry.grid(row=i, column=1, sticky="w", padx=5, pady=5)
                value_entry.insert(0, str(value))
                value_entry.configure(state="readonly")

            # أزرار التحكم
            button_frame = ttk.Frame(details_window)
            button_frame.pack(pady=10)

            ttk.Button(button_frame, text="إغلاق", command=details_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في عرض تفاصيل المستخدم (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في عرض التفاصيل: {str(e)}")

    def hs_show_selected_user_details(self):
        """عرض تفاصيل المستخدم المحدد في Hotspot"""
        try:
            selection = self.hs_cards_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى تحديد مستخدم أولاً")
                return

            item = selection[0]
            values = self.hs_cards_tree.item(item, 'values')
            self.hs_show_user_details(values)

        except Exception as e:
            self.logger.error(f"خطأ في عرض تفاصيل المستخدم المحدد (Hotspot): {str(e)}")

    def hs_copy_username(self):
        """نسخ اسم المستخدم إلى الحافظة في Hotspot"""
        try:
            selection = self.hs_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.hs_cards_tree.item(item, 'values')
            username = values[1]  # تجاهل عمود التحديد

            self.root.clipboard_clear()
            self.root.clipboard_append(username)
            messagebox.showinfo("نجح", f"تم نسخ اسم المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في نسخ اسم المستخدم (Hotspot): {str(e)}")

    def hs_copy_password(self):
        """نسخ كلمة السر إلى الحافظة في Hotspot"""
        try:
            selection = self.hs_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.hs_cards_tree.item(item, 'values')
            password = values[2]  # تجاهل عمود التحديد

            self.root.clipboard_clear()
            self.root.clipboard_append(password)
            messagebox.showinfo("نجح", f"تم نسخ كلمة السر: {password}")

        except Exception as e:
            self.logger.error(f"خطأ في نسخ كلمة السر (Hotspot): {str(e)}")

    def hs_enable_single_user(self):
        """تفعيل مستخدم واحد في Hotspot"""
        try:
            selection = self.hs_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.hs_cards_tree.item(item, 'values')
            username = values[1]

            # البحث عن المستخدم في البيانات
            user_data = None
            for user in self.hs_filtered_users_data:
                if user.get('username', '') == username:
                    user_data = user
                    break

            if user_data:
                success_count = self.hs_change_users_status([user_data], enable=True)
                if success_count > 0:
                    user_data['status'] = 'نشط'
                    self.hs_update_treeview_display()
                    self.hs_update_search_statistics()
                    messagebox.showinfo("نجح", f"تم تفعيل المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في تفعيل المستخدم الواحد (Hotspot): {str(e)}")

    def hs_disable_single_user(self):
        """تعطيل مستخدم واحد في Hotspot"""
        try:
            selection = self.hs_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.hs_cards_tree.item(item, 'values')
            username = values[1]

            # البحث عن المستخدم في البيانات
            user_data = None
            for user in self.hs_filtered_users_data:
                if user.get('username', '') == username:
                    user_data = user
                    break

            if user_data:
                success_count = self.hs_change_users_status([user_data], enable=False)
                if success_count > 0:
                    user_data['status'] = 'غير نشط'
                    self.hs_update_treeview_display()
                    self.hs_update_search_statistics()
                    messagebox.showinfo("نجح", f"تم تعطيل المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في تعطيل المستخدم الواحد (Hotspot): {str(e)}")

    def hs_delete_single_user(self):
        """حذف مستخدم واحد في Hotspot"""
        try:
            selection = self.hs_cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.hs_cards_tree.item(item, 'values')
            username = values[1]

            response = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم: {username}؟\nهذا الإجراء لا يمكن التراجع عنه."
            )

            if response:
                # البحث عن المستخدم في البيانات
                user_data = None
                for user in self.hs_filtered_users_data:
                    if user.get('username', '') == username:
                        user_data = user
                        break

                if user_data:
                    success_count = self.hs_delete_users_from_mikrotik([user_data])
                    if success_count > 0:
                        # إزالة من البيانات المحلية
                        if user_data in self.hs_all_users_data:
                            self.hs_all_users_data.remove(user_data)
                        if user_data in self.hs_filtered_users_data:
                            self.hs_filtered_users_data.remove(user_data)
                        if user_data in self.hs_selected_users_data:
                            self.hs_selected_users_data.remove(user_data)

                        self.hs_update_treeview_display()
                        self.hs_update_search_statistics()
                        self.hs_update_selected_count()
                        messagebox.showinfo("نجح", f"تم حذف المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في حذف المستخدم الواحد (Hotspot): {str(e)}")

    def hs_edit_selected_user(self):
        """تعديل المستخدم المحدد في Hotspot"""
        try:
            # التحقق من وجود مستخدم واحد محدد بالضبط
            if len(self.hs_selected_users_data) != 1:
                if len(self.hs_selected_users_data) == 0:
                    messagebox.showwarning("تحذير", "يرجى تحديد مستخدم واحد للتعديل")
                else:
                    messagebox.showwarning("تحذير", f"يرجى تحديد مستخدم واحد فقط للتعديل\nعدد المحددين حالياً: {len(self.hs_selected_users_data)}")
                return

            # الحصول على بيانات المستخدم المحدد
            user_data = self.hs_selected_users_data[0]
            self.logger.info(f"بدء تعديل المستخدم: {user_data.get('username', '')}")

            # فتح نافذة التعديل
            self.hs_open_edit_user_window(user_data)

        except Exception as e:
            self.logger.error(f"خطأ في تعديل المستخدم المحدد (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التعديل: {str(e)}")

    def hs_open_edit_user_window(self, user_data):
        """فتح نافذة تعديل المستخدم في Hotspot"""
        try:
            # إنشاء نافذة التعديل
            edit_window = tk.Toplevel(self.root)
            edit_window.title(f"تعديل المستخدم: {user_data.get('username', '')}")
            edit_window.geometry("600x500")
            edit_window.transient(self.root)
            edit_window.grab_set()

            # إطار رئيسي
            main_frame = ttk.Frame(edit_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            # عنوان النافذة
            title_label = ttk.Label(main_frame,
                                   text=f"تعديل بيانات المستخدم: {user_data.get('username', '')}",
                                   font=self.fonts['arabic_heading'])
            title_label.pack(pady=(0, 20))

            # إطار الحقول
            fields_frame = ttk.LabelFrame(main_frame, text="بيانات المستخدم", padding=15)
            fields_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            # متغيرات الحقول
            edit_vars = {}

            # تحويل حد البيانات من bytes إلى GB للعرض
            limit_bytes_gb = ""
            if user_data.get('limit_bytes_total'):
                try:
                    limit_bytes = int(user_data.get('limit_bytes_total', 0))
                    if limit_bytes > 0:
                        limit_bytes_gb = str(round(limit_bytes / (1024 * 1024 * 1024), 2))
                except (ValueError, TypeError):
                    limit_bytes_gb = ""

            # إنشاء الحقول
            fields_info = [
                ("username", "اسم المستخدم:", user_data.get('username', ''), "entry"),
                ("password", "كلمة السر:", user_data.get('password', ''), "entry"),
                ("profile", "البروفايل:", user_data.get('profile', ''), "combobox"),
                ("server", "الخادم:", user_data.get('server', ''), "entry"),
                ("limit_bytes_gb", "حد البيانات (GB):", limit_bytes_gb, "entry_with_hint"),
                ("comment", "التعليق:", user_data.get('comment', ''), "entry"),
                ("email", "الإيميل:", user_data.get('email', ''), "entry"),
                ("status", "الحالة:", user_data.get('status', 'نشط'), "combobox")
            ]

            row = 0
            for field_name, label_text, current_value, field_type in fields_info:
                # تسمية الحقل
                ttk.Label(fields_frame, text=label_text, font=self.fonts['arabic']).grid(
                    row=row, column=0, sticky="e", padx=(0, 10), pady=8)

                # إنشاء الحقل حسب النوع
                if field_type == "entry":
                    var = tk.StringVar(value=current_value)
                    entry = ttk.Entry(fields_frame, textvariable=var, width=40, font=self.fonts['arabic'])
                    entry.grid(row=row, column=1, sticky="w", pady=8)
                    edit_vars[field_name] = var

                elif field_type == "entry_with_hint":
                    # حقل حد البيانات مع نص توضيحي
                    var = tk.StringVar(value=current_value)
                    entry = ttk.Entry(fields_frame, textvariable=var, width=40, font=self.fonts['arabic'])
                    entry.grid(row=row, column=1, sticky="w", pady=8)
                    edit_vars[field_name] = var

                    # إضافة نص توضيحي
                    hint_text = "اتركه فارغاً لعدم وجود حد، أو أدخل القيمة بالجيجابايت (مثال: 5.5)"
                    hint_label = ttk.Label(fields_frame, text=hint_text,
                                         font=('Arial', 8), foreground='gray')
                    hint_label.grid(row=row, column=2, sticky="w", padx=(10, 0), pady=8)

                elif field_type == "combobox":
                    var = tk.StringVar(value=current_value)

                    if field_name == "profile":
                        # جلب البروفايلات المتاحة
                        profiles = self.hs_get_available_profiles()
                        combobox = ttk.Combobox(fields_frame, textvariable=var, values=profiles,
                                              width=37, font=self.fonts['arabic'])
                    elif field_name == "status":
                        # خيارات الحالة
                        status_options = ["نشط", "غير نشط"]
                        combobox = ttk.Combobox(fields_frame, textvariable=var, values=status_options,
                                              state="readonly", width=37, font=self.fonts['arabic'])

                    combobox.grid(row=row, column=1, sticky="w", pady=8)
                    edit_vars[field_name] = var

                row += 1

            # إطار الأزرار
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X, pady=(15, 0))

            # أزرار التحكم
            ttk.Button(buttons_frame, text="💾 حفظ التغييرات",
                      command=lambda: self.hs_save_user_changes(user_data, edit_vars, edit_window)).pack(side=tk.LEFT, padx=5)

            ttk.Button(buttons_frame, text="🔄 جلب البروفايلات",
                      command=lambda: self.hs_refresh_profiles_in_edit(edit_vars['profile'])).pack(side=tk.LEFT, padx=5)

            ttk.Button(buttons_frame, text="❌ إلغاء",
                      command=edit_window.destroy).pack(side=tk.RIGHT, padx=5)

            # معلومات إضافية
            info_frame = ttk.LabelFrame(main_frame, text="معلومات", padding=10)
            info_frame.pack(fill=tk.X, pady=(15, 0))

            info_text = f"• سيتم حفظ التغييرات في MikroTik Hotspot\n"
            info_text += f"• يمكن تعديل جميع الحقول بحرية (لا توجد قيود صارمة)\n"
            info_text += f"• حد البيانات: أدخل القيمة بالجيجابايت أو اتركه فارغاً\n"
            info_text += f"• الحقول المطلوبة: اسم المستخدم والبروفايل فقط"

            ttk.Label(info_frame, text=info_text, font=self.fonts['arabic']).pack(anchor="w")

            self.logger.info(f"تم فتح نافذة تعديل المستخدم: {user_data.get('username', '')}")

        except Exception as e:
            self.logger.error(f"خطأ في فتح نافذة التعديل (Hotspot): {str(e)}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التعديل: {str(e)}")

    def hs_get_available_profiles(self):
        """جلب البروفايلات المتاحة لـ Hotspot"""
        try:
            api = self.connect_api()
            if not api:
                self.logger.warning("فشل في الاتصال لجلب البروفايلات")
                return ["default"]

            # تحديد المسار حسب الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            resource_path = '/ip/hotspot/user/profile'

            # جلب البروفايلات
            profiles = api.get_resource(resource_path).get()
            profile_names = [profile.get('name', '') for profile in profiles if profile.get('name')]

            if profile_names:
                self.logger.info(f"تم جلب {len(profile_names)} بروفايل لنافذة التعديل")
                return profile_names
            else:
                self.logger.warning("لم يتم العثور على أي بروفايلات")
                return ["default"]

        except Exception as e:
            self.logger.error(f"خطأ في جلب البروفايلات للتعديل: {str(e)}")
            return ["default"]

    def hs_refresh_profiles_in_edit(self, profile_var):
        """تحديث قائمة البروفايلات في نافذة التعديل"""
        try:
            profiles = self.hs_get_available_profiles()

            # البحث عن combobox البروفايل وتحديثه
            # هذا يتطلب الوصول إلى widget، سنستخدم طريقة بديلة
            messagebox.showinfo("تحديث البروفايلات",
                              f"تم جلب {len(profiles)} بروفايل\n\n" +
                              "البروفايلات المتاحة:\n" +
                              "\n".join(f"• {profile}" for profile in profiles[:10]))

            self.logger.info("تم تحديث قائمة البروفايلات في نافذة التعديل")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث البروفايلات: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحديث البروفايلات: {str(e)}")

    def hs_save_user_changes(self, original_user_data, edit_vars, edit_window):
        """حفظ تغييرات المستخدم في MikroTik Hotspot"""
        try:
            # جمع البيانات الجديدة
            new_data = {}
            for field_name, var in edit_vars.items():
                new_data[field_name] = var.get().strip()

            # التحقق من صحة البيانات
            validation_result = self.hs_validate_user_data(new_data, original_user_data)
            if not validation_result['valid']:
                messagebox.showerror("خطأ في البيانات", validation_result['message'])
                return

            # تأكيد الحفظ
            changes_summary = self.hs_get_changes_summary(original_user_data, new_data)
            if not changes_summary['has_changes']:
                messagebox.showinfo("لا توجد تغييرات", "لم يتم إجراء أي تغييرات على بيانات المستخدم")
                return

            response = messagebox.askyesno(
                "تأكيد الحفظ",
                f"هل تريد حفظ التغييرات التالية؟\n\n{changes_summary['summary']}"
            )

            if not response:
                return

            # إنشاء نافذة التقدم
            progress_window = self.create_progress_window("حفظ التغييرات", "جاري حفظ تغييرات المستخدم...")

            try:
                # حفظ التغييرات في MikroTik
                success = self.hs_update_user_in_mikrotik(original_user_data, new_data, progress_window)

                if success:
                    # تحديث البيانات المحلية
                    self.hs_update_local_user_data(original_user_data, new_data)

                    # تحديث الجدول
                    self.hs_update_treeview_display()
                    self.hs_update_search_statistics()

                    progress_window.destroy()
                    edit_window.destroy()

                    messagebox.showinfo("نجح", f"تم حفظ تغييرات المستخدم بنجاح: {new_data['username']}")
                    self.logger.info(f"تم حفظ تغييرات المستخدم بنجاح: {new_data['username']}")

                else:
                    progress_window.destroy()
                    messagebox.showerror("خطأ", "فشل في حفظ التغييرات في MikroTik")

            except Exception as save_error:
                progress_window.destroy()
                raise save_error

        except Exception as e:
            self.logger.error(f"خطأ في حفظ تغييرات المستخدم: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في حفظ التغييرات: {str(e)}")

    def hs_validate_user_data(self, new_data, original_data):
        """التحقق من صحة بيانات المستخدم في Hotspot (مع قيود مرنة)"""
        try:
            # التحقق من اسم المستخدم (مطلوب فقط)
            if not new_data.get('username', '').strip():
                return {'valid': False, 'message': 'اسم المستخدم مطلوب ولا يمكن أن يكون فارغاً'}

            # التحقق من البروفايل (مطلوب فقط)
            profile = new_data.get('profile', '').strip()
            if not profile:
                return {'valid': False, 'message': 'البروفايل مطلوب ولا يمكن أن يكون فارغاً'}

            # التحقق من حد البيانات إذا تم إدخاله
            limit_bytes_gb = new_data.get('limit_bytes_gb', '').strip()
            if limit_bytes_gb:
                try:
                    limit_gb = float(limit_bytes_gb)
                    if limit_gb < 0:
                        return {'valid': False, 'message': 'حد البيانات يجب أن يكون رقماً موجباً'}
                    if limit_gb > 1000:  # حد أقصى معقول 1000 GB
                        return {'valid': False, 'message': 'حد البيانات كبير جداً (الحد الأقصى 1000 GB)'}
                except ValueError:
                    return {'valid': False, 'message': 'حد البيانات يجب أن يكون رقماً صحيحاً (مثال: 5.5)'}

            # التحقق من الإيميل إذا تم إدخاله (تحقق مرن)
            email = new_data.get('email', '').strip()
            if email:
                # تحقق بسيط ومرن من صحة الإيميل
                if '@' not in email:
                    return {'valid': False, 'message': 'صيغة الإيميل يجب أن تحتوي على @'}

            # التحقق من الحالة
            status = new_data.get('status', '').strip()
            if status and status not in ['نشط', 'غير نشط']:
                return {'valid': False, 'message': 'الحالة يجب أن تكون "نشط" أو "غير نشط"'}

            return {'valid': True, 'message': 'البيانات صحيحة'}

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من صحة البيانات: {str(e)}")
            return {'valid': False, 'message': f'خطأ في التحقق من البيانات: {str(e)}'}

    def hs_get_changes_summary(self, original_data, new_data):
        """الحصول على ملخص التغييرات"""
        try:
            changes = []
            has_changes = False

            # مقارنة الحقول العادية
            fields_to_compare = ['username', 'password', 'profile', 'server', 'comment', 'email', 'status']

            for field in fields_to_compare:
                original_value = original_data.get(field, '').strip()
                new_value = new_data.get(field, '').strip()

                if original_value != new_value:
                    has_changes = True
                    field_names = {
                        'username': 'اسم المستخدم',
                        'password': 'كلمة السر',
                        'profile': 'البروفايل',
                        'server': 'الخادم',
                        'comment': 'التعليق',
                        'email': 'الإيميل',
                        'status': 'الحالة'
                    }

                    field_name = field_names.get(field, field)

                    # إخفاء كلمة السر في الملخص
                    if field == 'password':
                        if original_value and new_value:
                            changes.append(f"• {field_name}: تم تغيير كلمة السر")
                        elif not original_value and new_value:
                            changes.append(f"• {field_name}: تم إضافة كلمة سر")
                        elif original_value and not new_value:
                            changes.append(f"• {field_name}: تم حذف كلمة السر")
                    else:
                        changes.append(f"• {field_name}: من '{original_value}' إلى '{new_value}'")

            # مقارنة حد البيانات
            original_limit_gb = ""
            if original_data.get('limit_bytes_total'):
                try:
                    limit_bytes = int(original_data.get('limit_bytes_total', 0))
                    if limit_bytes > 0:
                        original_limit_gb = str(round(limit_bytes / (1024 * 1024 * 1024), 2))
                except (ValueError, TypeError):
                    original_limit_gb = ""

            new_limit_gb = new_data.get('limit_bytes_gb', '').strip()

            if original_limit_gb != new_limit_gb:
                has_changes = True
                if not original_limit_gb and new_limit_gb:
                    changes.append(f"• حد البيانات: تم إضافة حد {new_limit_gb} GB")
                elif original_limit_gb and not new_limit_gb:
                    changes.append(f"• حد البيانات: تم إزالة الحد ({original_limit_gb} GB)")
                elif original_limit_gb and new_limit_gb:
                    changes.append(f"• حد البيانات: من {original_limit_gb} GB إلى {new_limit_gb} GB")

            summary = '\n'.join(changes) if changes else 'لا توجد تغييرات'

            return {
                'has_changes': has_changes,
                'summary': summary,
                'changes_count': len(changes)
            }

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ملخص التغييرات: {str(e)}")
            return {'has_changes': False, 'summary': 'خطأ في تحليل التغييرات', 'changes_count': 0}

    def hs_update_user_in_mikrotik(self, original_data, new_data, progress_window):
        """تحديث بيانات المستخدم في MikroTik Hotspot"""
        try:
            progress_window.update_status("🌐 الاتصال بـ MikroTik...")

            api = self.connect_api()
            if not api:
                raise Exception("فشل في الاتصال بـ MikroTik")

            # تحديد الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            progress_window.update_status("🔍 البحث عن المستخدم...")

            # البحث عن المستخدم في MikroTik
            resource_path = '/ip/hotspot/user'
            resource = api.get_resource(resource_path)

            original_username = original_data.get('username', '')
            users_found = resource.get(name=original_username)

            if not users_found:
                raise Exception(f"لم يتم العثور على المستخدم: {original_username}")

            user_to_update = users_found[0]
            user_id = user_to_update['id']

            progress_window.update_status("💾 حفظ التغييرات...")

            # إعداد البيانات للتحديث
            update_params = {'id': user_id}

            # تحديث الحقول المتغيرة
            if new_data['username'] != original_data.get('username', ''):
                update_params['name'] = new_data['username']

            if new_data['password'] != original_data.get('password', ''):
                update_params['password'] = new_data['password']

            if new_data['profile'] != original_data.get('profile', ''):
                update_params['profile'] = new_data['profile']

            if new_data['server'] != original_data.get('server', ''):
                if new_data['server']:
                    update_params['server'] = new_data['server']
                else:
                    # إزالة الخادم إذا كان فارغاً
                    update_params['server'] = ''

            if new_data['comment'] != original_data.get('comment', ''):
                update_params['comment'] = new_data['comment']

            if new_data['email'] != original_data.get('email', ''):
                update_params['email'] = new_data['email']

            # تحديث حد البيانات
            original_limit_gb = ""
            if original_data.get('limit_bytes_total'):
                try:
                    limit_bytes = int(original_data.get('limit_bytes_total', 0))
                    if limit_bytes > 0:
                        original_limit_gb = str(round(limit_bytes / (1024 * 1024 * 1024), 2))
                except (ValueError, TypeError):
                    original_limit_gb = ""

            new_limit_gb = new_data.get('limit_bytes_gb', '').strip()

            if original_limit_gb != new_limit_gb:
                if new_limit_gb:
                    try:
                        # تحويل من GB إلى bytes
                        limit_gb = float(new_limit_gb)
                        limit_bytes = int(limit_gb * 1024 * 1024 * 1024)
                        update_params['limit-bytes-total'] = str(limit_bytes)
                        self.logger.info(f"تحديث حد البيانات: {new_limit_gb} GB = {limit_bytes} bytes")
                    except ValueError:
                        self.logger.warning(f"قيمة حد البيانات غير صحيحة: {new_limit_gb}")
                else:
                    # إزالة حد البيانات
                    update_params['limit-bytes-total'] = ""
                    self.logger.info("إزالة حد البيانات")

            # تحديث الحالة
            if new_data['status'] != original_data.get('status', ''):
                disabled_value = "true" if new_data['status'] == 'غير نشط' else "false"
                update_params['disabled'] = disabled_value

            # تطبيق التحديثات
            if len(update_params) > 1:  # أكثر من مجرد ID
                resource.set(**update_params)
                self.logger.info(f"تم تحديث المستخدم في MikroTik: {new_data['username']}")

                progress_window.update_status("✅ تم الحفظ بنجاح")
                return True
            else:
                self.logger.info("لا توجد تغييرات لحفظها في MikroTik")
                return True

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"خطأ في تحديث المستخدم في MikroTik: {error_msg}")

            # تحليل نوع الخطأ
            if "not found" in error_msg.lower():
                raise Exception(f"المستخدم غير موجود في MikroTik: {original_data.get('username', '')}")
            elif "permission" in error_msg.lower():
                raise Exception("ليس لديك صلاحية لتعديل المستخدمين في MikroTik")
            elif "duplicate" in error_msg.lower() or "already exists" in error_msg.lower():
                raise Exception(f"اسم المستخدم موجود مسبقاً: {new_data['username']}")
            else:
                raise Exception(f"خطأ في MikroTik: {error_msg}")

    def hs_update_local_user_data(self, original_data, new_data):
        """تحديث البيانات المحلية للمستخدم"""
        try:
            # تحديث البيانات في جميع القوائم
            for user_list in [self.hs_all_users_data, self.hs_filtered_users_data, self.hs_selected_users_data]:
                for user in user_list:
                    if user == original_data:
                        # تحديث البيانات العادية
                        for field, value in new_data.items():
                            if field != 'limit_bytes_gb':  # تجاهل حقل GB المؤقت
                                user[field] = value

                        # تحديث حد البيانات بالـ bytes
                        limit_gb = new_data.get('limit_bytes_gb', '').strip()
                        if limit_gb:
                            try:
                                limit_gb_float = float(limit_gb)
                                limit_bytes = int(limit_gb_float * 1024 * 1024 * 1024)
                                user['limit_bytes_total'] = str(limit_bytes)
                            except ValueError:
                                # إزالة حد البيانات إذا كانت القيمة غير صحيحة
                                user.pop('limit_bytes_total', None)
                        else:
                            # إزالة حد البيانات إذا كان الحقل فارغاً
                            user.pop('limit_bytes_total', None)

                        break

            self.logger.info(f"تم تحديث البيانات المحلية للمستخدم: {new_data['username']}")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث البيانات المحلية: {str(e)}")

    # ===== نهاية دوال Hotspot Cards View =====

    def setup_cards_view_tab(self):
        """إعداد تبويب عرض الكروت مع نظام البحث المتقدم"""

        # إطار البحث المتقدم
        search_frame = ttk.LabelFrame(self.cards_view_frame, text="🔍 البحث المتقدم", padding=10)
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        # الصف الأول من حقول البحث
        search_row1 = ttk.Frame(search_frame)
        search_row1.pack(fill=tk.X, pady=2)

        # البحث باسم المستخدم
        ttk.Label(search_row1, text="اسم المستخدم:").grid(row=0, column=0, padx=5, pady=2, sticky="e")
        self.search_username_entry = ttk.Entry(search_row1, width=15)
        self.search_username_entry.grid(row=0, column=1, padx=5, pady=2)
        self.search_username_entry.bind('<KeyRelease>', self.on_search_change)

        # البحث بعنوان MAC
        ttk.Label(search_row1, text="عنوان MAC:").grid(row=0, column=2, padx=5, pady=2, sticky="e")
        self.search_mac_entry = ttk.Entry(search_row1, width=15)
        self.search_mac_entry.grid(row=0, column=3, padx=5, pady=2)
        self.search_mac_entry.bind('<KeyRelease>', self.on_search_change)

        # البحث بالبروفايل
        ttk.Label(search_row1, text="البروفايل:").grid(row=0, column=4, padx=5, pady=2, sticky="e")
        self.search_profile_combo = ttk.Combobox(search_row1, width=12, state="readonly")
        self.search_profile_combo.grid(row=0, column=5, padx=5, pady=2)
        self.search_profile_combo.bind("<<ComboboxSelected>>", self.on_search_change)

        # الصف الثاني من حقول البحث
        search_row2 = ttk.Frame(search_frame)
        search_row2.pack(fill=tk.X, pady=2)

        # البحث بالإيميل
        ttk.Label(search_row2, text="الإيميل:").grid(row=0, column=0, padx=5, pady=2, sticky="e")
        self.search_email_entry = ttk.Entry(search_row2, width=15)
        self.search_email_entry.grid(row=0, column=1, padx=5, pady=2)
        self.search_email_entry.bind('<KeyRelease>', self.on_search_change)

        # البحث بالتعليق
        ttk.Label(search_row2, text="التعليق:").grid(row=0, column=2, padx=5, pady=2, sticky="e")
        self.search_comment_entry = ttk.Entry(search_row2, width=15)
        self.search_comment_entry.grid(row=0, column=3, padx=5, pady=2)
        self.search_comment_entry.bind('<KeyRelease>', self.on_search_change)

        # فلترة الحالة
        ttk.Label(search_row2, text="الحالة:").grid(row=0, column=4, padx=5, pady=2, sticky="e")
        self.search_status_combo = ttk.Combobox(search_row2, width=12, values=["الكل", "نشط", "غير نشط"], state="readonly")
        self.search_status_combo.grid(row=0, column=5, padx=5, pady=2)
        self.search_status_combo.set("الكل")
        self.search_status_combo.bind("<<ComboboxSelected>>", self.on_search_change)

        # أزرار التحكم
        control_frame = ttk.Frame(search_frame)
        control_frame.pack(fill=tk.X, pady=5)

        # زر جلب البروفايلات
        self.fetch_profiles_btn = ttk.Button(
            control_frame, text="🔄 جلب البروفايلات",
            command=self.fetch_profiles_for_search
        )
        self.fetch_profiles_btn.pack(side=tk.LEFT, padx=5)

        # زر جلب المستخدمين
        self.fetch_users_button = ttk.Button(
            control_frame, text="👤 جلب المستخدمين",
            command=self.fetch_all_users
        )
        self.fetch_users_button.pack(side=tk.LEFT, padx=5)

        # زر مسح البحث
        self.clear_search_btn = ttk.Button(
            control_frame, text="🗑️ مسح البحث",
            command=self.clear_search_fields
        )
        self.clear_search_btn.pack(side=tk.LEFT, padx=5)

        # زر تصدير النتائج
        self.export_results_btn = ttk.Button(
            control_frame, text="📤 تصدير النتائج",
            command=self.export_search_results
        )
        self.export_results_btn.pack(side=tk.RIGHT, padx=5)

        # إطار الإحصائيات
        stats_frame = ttk.Frame(search_frame)
        stats_frame.pack(fill=tk.X, pady=2)

        self.stats_label = ttk.Label(stats_frame, text="الإحصائيات: لم يتم جلب البيانات بعد")
        self.stats_label.pack(side=tk.LEFT)

        # تعطيل الأزرار إذا لم تكن مكتبة RouterOS متوفرة
        if not ROUTEROS_AVAILABLE:
            self.fetch_profiles_btn.configure(state="disabled")
            self.fetch_users_button.configure(state="disabled")

        # إعداد الجدول المحسن
        self.setup_enhanced_treeview()

        # متغيرات البحث
        self.all_users_data = []  # جميع البيانات المجلبة
        self.filtered_users_data = []  # البيانات المفلترة
        self.search_history = []  # تاريخ البحث

    def setup_enhanced_treeview(self):
        """إعداد الجدول المحسن لعرض المستخدمين"""
        try:
            # إطار الجدول
            tree_frame = ttk.Frame(self.cards_view_frame)
            tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # تحديد الأعمدة حسب نوع النظام
            if self.system_type == 'user_manager':
                columns = ("Username", "Password", "Profile", "Comment", "Location", "Email", "Status")
                headings = {
                    "Username": "اسم المستخدم",
                    "Password": "كلمة السر",
                    "Profile": "البروفايل",
                    "Comment": "التعليق",
                    "Location": "الموقع",
                    "Email": "الإيميل",
                    "Status": "الحالة"
                }
                widths = {
                    "Username": 120,
                    "Password": 100,
                    "Profile": 100,
                    "Comment": 150,
                    "Location": 80,
                    "Email": 150,
                    "Status": 80
                }
            else:  # hotspot
                columns = ("Username", "Password", "Profile", "MAC", "Limit", "Days", "Email", "Status")
                headings = {
                    "Username": "اسم المستخدم",
                    "Password": "كلمة السر",
                    "Profile": "البروفايل",
                    "MAC": "عنوان MAC",
                    "Limit": "حد البيانات",
                    "Days": "عدد الأيام",
                    "Email": "الإيميل",
                    "Status": "الحالة"
                }
                widths = {
                    "Username": 120,
                    "Password": 100,
                    "Profile": 100,
                    "MAC": 140,
                    "Limit": 100,
                    "Days": 80,
                    "Email": 150,
                    "Status": 80
                }

            # إنشاء الجدول
            self.cards_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

            # إعداد العناوين والأعمدة
            for col in columns:
                self.cards_tree.heading(col, text=headings[col])
                self.cards_tree.column(col, width=widths[col], minwidth=50)

            # إضافة شريط التمرير العمودي
            v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.cards_tree.yview)
            self.cards_tree.configure(yscrollcommand=v_scrollbar.set)

            # إضافة شريط التمرير الأفقي
            h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.cards_tree.xview)
            self.cards_tree.configure(xscrollcommand=h_scrollbar.set)

            # ترتيب العناصر
            self.cards_tree.grid(row=0, column=0, sticky="nsew")
            v_scrollbar.grid(row=0, column=1, sticky="ns")
            h_scrollbar.grid(row=1, column=0, sticky="ew")

            # إعداد التوسع
            tree_frame.grid_rowconfigure(0, weight=1)
            tree_frame.grid_columnconfigure(0, weight=1)

            # ربط الأحداث
            self.cards_tree.bind('<Double-1>', self.on_user_double_click)
            self.cards_tree.bind('<Button-3>', self.show_context_menu)

            self.logger.info("تم إعداد الجدول المحسن بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في إعداد الجدول: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في إعداد الجدول: {str(e)}")

    def on_search_change(self, event=None):
        """تنفيذ البحث عند تغيير أي حقل بحث"""
        try:
            if not self.all_users_data:
                return

            # تأخير البحث لتجنب البحث المستمر أثناء الكتابة
            if hasattr(self, 'search_timer'):
                self.root.after_cancel(self.search_timer)

            self.search_timer = self.root.after(300, self.perform_search)

        except Exception as e:
            self.logger.error(f"خطأ في معالجة تغيير البحث: {str(e)}")

    def perform_search(self):
        """تنفيذ البحث الفعلي"""
        try:
            if not self.all_users_data:
                return

            # الحصول على معايير البحث
            search_criteria = {
                'username': self.search_username_entry.get().strip().lower(),
                'mac': self.search_mac_entry.get().strip().lower(),
                'profile': self.search_profile_combo.get().strip().lower(),
                'email': self.search_email_entry.get().strip().lower(),
                'comment': self.search_comment_entry.get().strip().lower(),
                'status': self.search_status_combo.get().strip()
            }

            # تصفية البيانات
            self.filtered_users_data = []
            for user in self.all_users_data:
                if self.matches_search_criteria(user, search_criteria):
                    self.filtered_users_data.append(user)

            # تحديث الجدول
            self.update_treeview_display()

            # تحديث الإحصائيات
            self.update_search_statistics()

            # حفظ في تاريخ البحث
            self.save_search_to_history(search_criteria)

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ البحث: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

    def matches_search_criteria(self, user, criteria):
        """فحص ما إذا كان المستخدم يطابق معايير البحث"""
        try:
            # البحث باسم المستخدم
            if criteria['username'] and criteria['username'] not in user.get('username', '').lower():
                return False

            # البحث بعنوان MAC
            if criteria['mac'] and criteria['mac'] not in user.get('mac', '').lower():
                return False

            # البحث بالبروفايل
            if criteria['profile'] and criteria['profile'] != user.get('profile', '').lower():
                return False

            # البحث بالإيميل
            if criteria['email'] and criteria['email'] not in user.get('email', '').lower():
                return False

            # البحث بالتعليق
            if criteria['comment'] and criteria['comment'] not in user.get('comment', '').lower():
                return False

            # فلترة الحالة
            if criteria['status'] and criteria['status'] != "الكل":
                user_status = user.get('status', 'نشط')
                if criteria['status'] == "نشط" and user_status != 'نشط':
                    return False
                elif criteria['status'] == "غير نشط" and user_status != 'غير نشط':
                    return False

            return True

        except Exception as e:
            self.logger.error(f"خطأ في فحص معايير البحث: {str(e)}")
            return False

    def update_treeview_display(self):
        """تحديث عرض الجدول بالبيانات المفلترة"""
        try:
            # مسح البيانات الحالية
            self.cards_tree.delete(*self.cards_tree.get_children())

            # إضافة البيانات المفلترة
            for user in self.filtered_users_data:
                if self.system_type == 'user_manager':
                    values = (
                        user.get('username', ''),
                        user.get('password', ''),
                        user.get('profile', ''),
                        user.get('comment', ''),
                        user.get('location', ''),
                        user.get('email', ''),
                        user.get('status', 'نشط')
                    )
                else:  # hotspot
                    values = (
                        user.get('username', ''),
                        user.get('password', ''),
                        user.get('profile', ''),
                        user.get('mac', ''),
                        user.get('limit', ''),
                        user.get('days', ''),
                        user.get('email', ''),
                        user.get('status', 'نشط')
                    )

                # إضافة لون مختلف للمستخدمين غير النشطين
                item_id = self.cards_tree.insert("", "end", values=values)
                if user.get('status', 'نشط') == 'غير نشط':
                    self.cards_tree.set(item_id, "Status", "غير نشط")

            self.logger.info(f"تم تحديث الجدول بـ {len(self.filtered_users_data)} مستخدم")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الجدول: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تحديث الجدول: {str(e)}")

    def update_search_statistics(self):
        """تحديث إحصائيات البحث"""
        try:
            total_users = len(self.all_users_data)
            filtered_users = len(self.filtered_users_data)

            # حساب المستخدمين النشطين وغير النشطين
            active_count = sum(1 for user in self.filtered_users_data if user.get('status', 'نشط') == 'نشط')
            inactive_count = filtered_users - active_count

            # تحديث النص
            stats_text = f"الإحصائيات: إجمالي: {total_users} | معروض: {filtered_users} | نشط: {active_count} | غير نشط: {inactive_count}"
            self.stats_label.configure(text=stats_text)

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def save_search_to_history(self, criteria):
        """حفظ البحث في التاريخ"""
        try:
            # إزالة المعايير الفارغة
            non_empty_criteria = {k: v for k, v in criteria.items() if v}

            if non_empty_criteria:
                from datetime import datetime
                search_entry = {
                    'criteria': non_empty_criteria,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'results_count': len(self.filtered_users_data)
                }

                # إضافة للتاريخ (الاحتفاظ بآخر 10 عمليات بحث)
                self.search_history.insert(0, search_entry)
                if len(self.search_history) > 10:
                    self.search_history = self.search_history[:10]

        except Exception as e:
            self.logger.error(f"خطأ في حفظ تاريخ البحث: {str(e)}")

    def clear_search_fields(self):
        """مسح جميع حقول البحث"""
        try:
            self.search_username_entry.delete(0, tk.END)
            self.search_mac_entry.delete(0, tk.END)
            self.search_profile_combo.set("")
            self.search_email_entry.delete(0, tk.END)
            self.search_comment_entry.delete(0, tk.END)
            self.search_status_combo.set("الكل")

            # إعادة عرض جميع البيانات
            self.filtered_users_data = self.all_users_data.copy()
            self.update_treeview_display()
            self.update_search_statistics()

            self.logger.info("تم مسح حقول البحث")

        except Exception as e:
            self.logger.error(f"خطأ في مسح حقول البحث: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في مسح حقول البحث: {str(e)}")

    def fetch_profiles_for_search(self):
        """جلب قائمة البروفايلات من MikroTik لاستخدامها في البحث"""
        try:
            api = self.connect_api()
            if not api:
                return

            self.logger.info("بدء جلب البروفايلات للبحث")

            # تحديد المسار حسب نوع النظام والإصدار
            if self.system_type == 'user_manager':
                version = getattr(self, 'version_combo', None)
                if version and hasattr(version, 'get'):
                    version_value = version.get()
                else:
                    version_value = 'v7'  # افتراضي

                if version_value == "v6":
                    profiles = api.get_resource('/tool/user-manager/profile').get()
                else:
                    profiles = api.get_resource('/user-manager/profile').get()
            else:  # hotspot
                profiles = api.get_resource('/ip/hotspot/user/profile').get()

            # استخراج أسماء البروفايلات
            profile_names = [profile.get('name', '') for profile in profiles if profile.get('name')]
            profile_names.insert(0, "")  # إضافة خيار فارغ

            # تحديث القائمة المنسدلة
            self.search_profile_combo.configure(values=profile_names)

            messagebox.showinfo("نجح", f"تم جلب {len(profile_names)-1} بروفايل بنجاح")
            self.logger.info(f"تم جلب {len(profile_names)-1} بروفايل للبحث")

        except Exception as e:
            self.logger.error(f"خطأ في جلب البروفايلات للبحث: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في جلب البروفايلات: {str(e)}")

    def fetch_all_users(self):
        """جلب جميع المستخدمين من MikroTik"""
        try:
            api = self.connect_api()
            if not api:
                return

            self.logger.info("بدء جلب جميع المستخدمين")

            # إنشاء نافذة التقدم
            progress_window = tk.Toplevel(self.root)
            progress_window.title("جلب البيانات")
            progress_window.geometry("400x150")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # إعداد نافذة التقدم
            ttk.Label(progress_window, text="جاري جلب البيانات من MikroTik...",
                     font=self.fonts['arabic']).pack(pady=20)

            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=tk.X)
            progress_bar.start()

            status_label = ttk.Label(progress_window, text="الاتصال بالخادم...",
                                   font=self.fonts['arabic'])
            status_label.pack(pady=5)

            # تحديث الواجهة
            self.root.update()

            # جلب البيانات حسب نوع النظام
            if self.system_type == 'user_manager':
                users_data = self.fetch_user_manager_users(api, status_label)
            else:  # hotspot
                users_data = self.fetch_hotspot_users(api, status_label)

            # حفظ البيانات
            self.all_users_data = users_data
            self.filtered_users_data = users_data.copy()

            # تحديث الجدول والإحصائيات
            self.update_treeview_display()
            self.update_search_statistics()

            # إغلاق نافذة التقدم
            progress_window.destroy()

            messagebox.showinfo("نجح", f"تم جلب {len(users_data)} مستخدم بنجاح")
            self.logger.info(f"تم جلب {len(users_data)} مستخدم بنجاح")

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()
            self.logger.error(f"خطأ في جلب المستخدمين: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في جلب المستخدمين: {str(e)}")

    def fetch_user_manager_users(self, api, status_label):
        """جلب مستخدمي User Manager"""
        try:
            status_label.configure(text="جلب مستخدمي User Manager...")
            self.root.update()

            # تحديد المسار حسب الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'  # افتراضي

            if version_value == "v6":
                users = api.get_resource('/tool/user-manager/user').get()
            else:
                users = api.get_resource('/user-manager/user').get()

            users_data = []
            for user in users:
                user_data = {
                    'username': user.get('username', user.get('name', '')),
                    'password': user.get('password', ''),
                    'profile': user.get('profile', ''),
                    'comment': user.get('comment', ''),
                    'location': user.get('location', ''),
                    'email': user.get('email', ''),
                    'status': 'نشط' if user.get('disabled', 'false') == 'false' else 'غير نشط',
                    'mac': '',  # User Manager لا يحتوي على MAC عادة
                    'limit': '',
                    'days': ''
                }
                users_data.append(user_data)

            return users_data

        except Exception as e:
            self.logger.error(f"خطأ في جلب مستخدمي User Manager: {str(e)}")
            raise

    def fetch_hotspot_users(self, api, status_label):
        """جلب مستخدمي Hotspot"""
        try:
            status_label.configure(text="جلب مستخدمي Hotspot...")
            self.root.update()

            users = api.get_resource('/ip/hotspot/user').get()

            users_data = []
            for user in users:
                # معالجة حد البيانات
                limit_bytes = user.get('limit-bytes-total', '')
                if limit_bytes:
                    try:
                        limit_gb = int(limit_bytes) / (1024 * 1024 * 1024)
                        limit = f"{limit_gb:.1f} GB"
                    except:
                        limit = limit_bytes
                else:
                    limit = ''

                # معالجة الأيام من الإيميل
                email = user.get('email', '')
                days = ''
                if email and '@' in email:
                    days_part = email.split('@')[0]
                    if days_part.isdigit():
                        days = days_part

                # معالجة عنوان MAC
                mac_address = user.get('mac-address', '')

                user_data = {
                    'username': user.get('name', ''),
                    'password': user.get('password', ''),
                    'profile': user.get('profile', ''),
                    'comment': user.get('comment', ''),
                    'location': '',  # Hotspot لا يحتوي على location عادة
                    'email': email,
                    'status': 'نشط' if user.get('disabled', 'false') == 'false' else 'غير نشط',
                    'mac': mac_address,
                    'limit': limit,
                    'days': days
                }
                users_data.append(user_data)

            return users_data

        except Exception as e:
            self.logger.error(f"خطأ في جلب مستخدمي Hotspot: {str(e)}")
            raise

    def export_search_results(self):
        """تصدير نتائج البحث"""
        try:
            if not self.filtered_users_data:
                messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
                return

            # نافذة اختيار نوع التصدير
            export_window = tk.Toplevel(self.root)
            export_window.title("تصدير نتائج البحث")
            export_window.geometry("350x200")
            export_window.transient(self.root)
            export_window.grab_set()

            ttk.Label(export_window, text="اختر نوع الملف للتصدير:",
                     font=self.fonts['arabic_heading']).pack(pady=10)

            export_type = tk.StringVar(value="csv")

            # خيارات التصدير
            ttk.Radiobutton(export_window, text="CSV (Excel)",
                           variable=export_type, value="csv").pack(anchor="w", padx=20, pady=2)
            ttk.Radiobutton(export_window, text="JSON (البيانات المنظمة)",
                           variable=export_type, value="json").pack(anchor="w", padx=20, pady=2)
            ttk.Radiobutton(export_window, text="TXT (نص عادي)",
                           variable=export_type, value="txt").pack(anchor="w", padx=20, pady=2)

            # معلومات التصدير
            info_label = ttk.Label(export_window,
                                 text=f"سيتم تصدير {len(self.filtered_users_data)} مستخدم",
                                 font=self.fonts['arabic'])
            info_label.pack(pady=10)

            # أزرار التحكم
            button_frame = ttk.Frame(export_window)
            button_frame.pack(pady=20)

            ttk.Button(button_frame, text="تصدير",
                      command=lambda: self.perform_export_search_results(export_type.get(), export_window)).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="إلغاء",
                      command=export_window.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في نافذة التصدير: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التصدير: {str(e)}")

    def perform_export_search_results(self, export_type, window):
        """تنفيذ تصدير نتائج البحث"""
        try:
            window.destroy()

            # تحديد امتداد الملف
            if export_type == "csv":
                file_ext = ".csv"
                file_types = [("CSV files", "*.csv")]
            elif export_type == "json":
                file_ext = ".json"
                file_types = [("JSON files", "*.json")]
            else:  # txt
                file_ext = ".txt"
                file_types = [("Text files", "*.txt")]

            # اختيار مكان الحفظ
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            system_name = "user_manager" if self.system_type == 'user_manager' else "hotspot"
            default_name = f"{system_name}_search_results_{timestamp}{file_ext}"

            filename = filedialog.asksaveasfilename(
                defaultextension=file_ext,
                filetypes=file_types,
                initialfile=default_name
            )

            if not filename:
                return

            # تصدير البيانات
            if export_type == "csv":
                self.export_search_to_csv(filename)
            elif export_type == "json":
                self.export_search_to_json(filename)
            else:
                self.export_search_to_txt(filename)

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ التصدير: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في التصدير: {str(e)}")

    def export_search_to_csv(self, filename):
        """تصدير نتائج البحث إلى CSV"""
        try:
            import csv

            with open(filename, 'w', newline='', encoding='utf-8') as file:
                if self.system_type == 'user_manager':
                    fieldnames = ['username', 'password', 'profile', 'comment', 'location', 'email', 'status']
                else:  # hotspot
                    fieldnames = ['username', 'password', 'profile', 'mac', 'limit', 'days', 'email', 'status']

                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()

                for user in self.filtered_users_data:
                    row = {field: user.get(field, '') for field in fieldnames}
                    writer.writerow(row)

            messagebox.showinfo("نجح", f"تم تصدير {len(self.filtered_users_data)} مستخدم إلى CSV بنجاح")
            self.logger.info(f"تم تصدير {len(self.filtered_users_data)} مستخدم إلى CSV")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير CSV: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير CSV: {str(e)}")

    def export_search_to_json(self, filename):
        """تصدير نتائج البحث إلى JSON"""
        try:
            from datetime import datetime
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'system_type': self.system_type,
                    'total_users': len(self.filtered_users_data),
                    'search_criteria': getattr(self, 'last_search_criteria', {})
                },
                'users': self.filtered_users_data
            }

            with open(filename, 'w', encoding='utf-8') as file:
                json.dump(export_data, file, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", f"تم تصدير {len(self.filtered_users_data)} مستخدم إلى JSON بنجاح")
            self.logger.info(f"تم تصدير {len(self.filtered_users_data)} مستخدم إلى JSON")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير JSON: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير JSON: {str(e)}")

    def export_search_to_txt(self, filename):
        """تصدير نتائج البحث إلى TXT"""
        try:
            from datetime import datetime
            with open(filename, 'w', encoding='utf-8') as file:
                # كتابة معلومات التصدير
                file.write(f"تقرير المستخدمين - {self.system_type.upper()}\n")
                file.write(f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                file.write(f"عدد المستخدمين: {len(self.filtered_users_data)}\n")
                file.write("=" * 80 + "\n\n")

                # كتابة بيانات المستخدمين
                for i, user in enumerate(self.filtered_users_data, 1):
                    file.write(f"المستخدم #{i}:\n")
                    file.write(f"  اسم المستخدم: {user.get('username', '')}\n")
                    file.write(f"  كلمة السر: {user.get('password', '')}\n")
                    file.write(f"  البروفايل: {user.get('profile', '')}\n")

                    if self.system_type == 'user_manager':
                        file.write(f"  التعليق: {user.get('comment', '')}\n")
                        file.write(f"  الموقع: {user.get('location', '')}\n")
                    else:  # hotspot
                        file.write(f"  عنوان MAC: {user.get('mac', '')}\n")
                        file.write(f"  حد البيانات: {user.get('limit', '')}\n")
                        file.write(f"  عدد الأيام: {user.get('days', '')}\n")

                    file.write(f"  الإيميل: {user.get('email', '')}\n")
                    file.write(f"  الحالة: {user.get('status', '')}\n")
                    file.write("-" * 40 + "\n")

            messagebox.showinfo("نجح", f"تم تصدير {len(self.filtered_users_data)} مستخدم إلى TXT بنجاح")
            self.logger.info(f"تم تصدير {len(self.filtered_users_data)} مستخدم إلى TXT")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير TXT: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير TXT: {str(e)}")

    def on_user_double_click(self, event):
        """معالجة النقر المزدوج على مستخدم"""
        try:
            selection = self.cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.cards_tree.item(item, 'values')

            if not values:
                return

            # إنشاء نافذة تفاصيل المستخدم
            self.show_user_details(values)

        except Exception as e:
            self.logger.error(f"خطأ في معالجة النقر المزدوج: {str(e)}")

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        try:
            # تحديد العنصر المنقور عليه
            item = self.cards_tree.identify_row(event.y)
            if item:
                self.cards_tree.selection_set(item)

                # إنشاء القائمة السياقية
                context_menu = tk.Menu(self.root, tearoff=0)
                context_menu.add_command(label="عرض التفاصيل", command=self.show_selected_user_details)
                context_menu.add_separator()
                context_menu.add_command(label="تصدير هذا المستخدم", command=self.export_selected_user)
                context_menu.add_separator()
                context_menu.add_command(label="نسخ اسم المستخدم", command=self.copy_username)
                context_menu.add_command(label="نسخ كلمة السر", command=self.copy_password)

                # عرض القائمة
                context_menu.tk_popup(event.x_root, event.y_root)

        except Exception as e:
            self.logger.error(f"خطأ في عرض القائمة السياقية: {str(e)}")

    def show_user_details(self, values):
        """عرض تفاصيل المستخدم في نافذة منفصلة"""
        try:
            details_window = tk.Toplevel(self.root)
            details_window.title("تفاصيل المستخدم")
            details_window.geometry("500x400")
            details_window.transient(self.root)
            details_window.grab_set()

            # إطار التفاصيل
            details_frame = ttk.LabelFrame(details_window, text="معلومات المستخدم", padding=15)
            details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # عرض التفاصيل حسب نوع النظام
            if self.system_type == 'user_manager':
                labels = ["اسم المستخدم:", "كلمة السر:", "البروفايل:", "التعليق:", "الموقع:", "الإيميل:", "الحالة:"]
            else:  # hotspot
                labels = ["اسم المستخدم:", "كلمة السر:", "البروفايل:", "عنوان MAC:", "حد البيانات:", "عدد الأيام:", "الإيميل:", "الحالة:"]

            for i, (label, value) in enumerate(zip(labels, values)):
                ttk.Label(details_frame, text=label, font=self.fonts['arabic']).grid(
                    row=i, column=0, sticky="e", padx=5, pady=5)

                # إنشاء حقل نص قابل للتحديد
                value_entry = ttk.Entry(details_frame, width=40, font=self.fonts['arabic'])
                value_entry.grid(row=i, column=1, sticky="w", padx=5, pady=5)
                value_entry.insert(0, str(value))
                value_entry.configure(state="readonly")

            # أزرار التحكم
            button_frame = ttk.Frame(details_window)
            button_frame.pack(pady=10)

            ttk.Button(button_frame, text="إغلاق", command=details_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            self.logger.error(f"خطأ في عرض تفاصيل المستخدم: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في عرض التفاصيل: {str(e)}")

    def show_selected_user_details(self):
        """عرض تفاصيل المستخدم المحدد"""
        try:
            selection = self.cards_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى تحديد مستخدم أولاً")
                return

            item = selection[0]
            values = self.cards_tree.item(item, 'values')
            self.show_user_details(values)

        except Exception as e:
            self.logger.error(f"خطأ في عرض تفاصيل المستخدم المحدد: {str(e)}")

    def export_selected_user(self):
        """تصدير المستخدم المحدد"""
        try:
            selection = self.cards_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى تحديد مستخدم أولاً")
                return

            item = selection[0]
            values = self.cards_tree.item(item, 'values')

            # إنشاء بيانات المستخدم للتصدير
            if self.system_type == 'user_manager':
                user_data = {
                    'username': values[0],
                    'password': values[1],
                    'profile': values[2],
                    'comment': values[3],
                    'location': values[4],
                    'email': values[5],
                    'status': values[6]
                }
            else:  # hotspot
                user_data = {
                    'username': values[0],
                    'password': values[1],
                    'profile': values[2],
                    'mac': values[3],
                    'limit': values[4],
                    'days': values[5],
                    'email': values[6],
                    'status': values[7]
                }

            # حفظ البيانات المفلترة الأصلية
            original_filtered_data = self.filtered_users_data.copy()

            # تعيين المستخدم الواحد للتصدير
            self.filtered_users_data = [user_data]

            # تصدير المستخدم
            self.export_search_results()

            # استعادة البيانات الأصلية
            self.filtered_users_data = original_filtered_data

        except Exception as e:
            self.logger.error(f"خطأ في تصدير المستخدم المحدد: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في تصدير المستخدم: {str(e)}")

    def copy_username(self):
        """نسخ اسم المستخدم إلى الحافظة"""
        try:
            selection = self.cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.cards_tree.item(item, 'values')
            username = values[0]

            self.root.clipboard_clear()
            self.root.clipboard_append(username)
            messagebox.showinfo("نجح", f"تم نسخ اسم المستخدم: {username}")

        except Exception as e:
            self.logger.error(f"خطأ في نسخ اسم المستخدم: {str(e)}")

    def copy_password(self):
        """نسخ كلمة السر إلى الحافظة"""
        try:
            selection = self.cards_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.cards_tree.item(item, 'values')
            password = values[1]

            self.root.clipboard_clear()
            self.root.clipboard_append(password)
            messagebox.showinfo("نجح", f"تم نسخ كلمة السر: {password}")

        except Exception as e:
            self.logger.error(f"خطأ في نسخ كلمة السر: {str(e)}")

    def update_status_color(self, *args):
        status = self.connection_status_var.get()
        self.connection_status_label.configure(style="Connected.TLabel" if status == "متصل" else "Disconnected.TLabel")

    def save_as_txt(self):
        if not self.generated_credentials:
            messagebox.showwarning("تحذير", "يرجى توليد الحسابات أولاً")
            return

        script = self.output_text.get(1.0, tk.END).strip()
        if not script:
            messagebox.showwarning("تحذير", "لا يوجد سكريبت لحفظه")
            return

        date = datetime.now().strftime("%Y%m%d")
        prefix = self.prefix_entry.get().strip()
        system_prefix = "usermanager" if self.system_type == 'user_manager' else "hotspot"
        default_filename = f"{system_prefix}_script_{prefix}{len(self.generated_credentials)}_{date}.txt"

        filename = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("TXT files", "*.txt")], initialfile=default_filename)
        if filename:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(script)
            messagebox.showinfo("نجاح", f"تم حفظ الملف: {filename}")
        self.update_preview()

    def save_settings(self):
        settings = {
            "system_type": self.system_type,
            "customer": self.customer_entry.get() if self.system_type == 'user_manager' else "",
            "server": self.server_entry.get() if self.system_type == 'hotspot' else "",
            "version": self.version_combo.get(),
            "profile": self.profile_combo.get(),
            "comment": self.comment_entry.get(),
            "location": self.location_entry.get(),
            "limit_bytes": self.limit_bytes_entry.get() if self.system_type == 'hotspot' else "",
            "limit_unit": self.limit_unit_combo.get() if self.system_type == 'hotspot' else "",
            "days": self.days_entry.get() if self.system_type == 'hotspot' else "",
            "email_template": self.email_template_entry.get() if self.system_type == 'hotspot' else "",
            "cred_type": self.cred_type_combo.get(),
            "cred_match": self.cred_match_combo.get(),
            "length": self.length_entry.get(),
            "count": self.count_entry.get(),
            "prefix": self.prefix_entry.get(),
            "suffix": self.suffix_entry.get(),
            "pass_suffix": self.pass_suffix_entry.get(),
            "delay": self.delay_entry.get(),
            "columns": self.columns_entry.get(),
            "rows": self.rows_entry.get(),
            "spacing": self.spacing_entry.get(),
            "print_username": self.print_username_var.get(),
            "print_password": self.print_password_var.get(),
            "use_serial": self.use_serial_var.get(),
            "use_date": self.use_date_var.get(),
            "use_qr": self.use_qr_var.get(),
            "username_size": self.username_size_entry.get(),
            "username_color": self.username_color_entry.get(),
            "username_bold": self.username_bold_var.get(),
            "username_x": self.username_x_entry.get(),
            "username_y": self.username_y_entry.get(),
            "password_size": self.password_size_entry.get(),
            "password_color": self.password_color_entry.get(),
            "password_bold": self.password_bold_var.get(),
            "password_x": self.password_x_entry.get(),
            "password_y": self.password_y_entry.get(),
            "serial_start": self.serial_start_entry.get(),
            "serial_size": self.serial_size_entry.get(),
            "serial_color": self.serial_color_entry.get(),
            "serial_bold": self.serial_bold_var.get(),
            "serial_x": self.serial_x_entry.get(),
            "serial_y": self.serial_y_entry.get(),
            "date_size": self.date_size_entry.get(),
            "date_color": self.date_color_entry.get(),
            "date_bold": self.date_bold_var.get(),
            "date_x": self.date_x_entry.get(),
            "date_y": self.date_y_entry.get(),
            "qr_size": self.qr_size_entry.get(),
            "qr_x": self.qr_x_entry.get(),
            "qr_y": self.qr_y_entry.get(),
            "background_image_path": self.background_image_path or "",
            "api_ip": self.api_ip_entry.get(),
            "api_username": self.api_username_entry.get(),
            "api_password": self.encrypt_password(self.api_password_entry.get()),  # تشفير كلمة المرور للحفظ
            "api_port": self.api_port_entry.get(),
            "last_serial": self.last_serial,
            "user_email": self.user_email.get(),
            "caller_id_bind": self.caller_id_bind_var.get(),
            "use_price": self.use_price_var.get(),
            "price_value": self.price_value_entry.get(),
            "price_size": self.price_size_entry.get(),
            "price_color": self.price_color_entry.get(),
            "price_bold": self.price_bold_var.get(),
            "price_x": self.price_x_entry.get(),
            "price_y": self.price_y_entry.get(),
            "price": self.price_entry.get() if hasattr(self, 'price_entry') else "",
            "run_script_before_cleanup": self.run_script_before_cleanup_var.get(),
            "script_to_run": self.script_to_run_entry.get()
        }
        with open(self.settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

    def load_settings(self):
        default_settings = {
            "system_type": None, "customer": "admin", "server": "all", "version": "v6", "profile": "default",
            "comment": "", "location": "", "limit_bytes": "", "limit_unit": "GB", "days": "", "email_template": "@sa.sa",
            "cred_type": "أرقام وحروف", "cred_match": "مختلفة", "length": "8", "count": "720", "prefix": "",
            "suffix": "", "pass_suffix": "", "delay": "100", "columns": "4", "rows": "18", "spacing": "2",
            "print_username": True, "print_password": True, "use_serial": False, "use_date": False, "use_qr": False,
            "username_size": "8", "username_color": "#000000", "username_bold": False, "username_x": "30", "username_y": "5",
            "password_size": "8", "password_color": "#000000", "password_bold": False, "password_x": "30", "password_y": "10",
            "serial_start": "1", "serial_size": "6", "serial_color": "#000000", "serial_bold": False, "serial_x": "5", "serial_y": "5",
            "date_size": "8", "date_color": "#000000", "date_bold": False, "date_x": "48", "date_y": "12",
            "qr_size": "10", "qr_x": "5", "qr_y": "5", "background_image_path": "", "api_ip": "***********",
            "api_username": "admin", "api_password": "", "api_port": "8728", "last_serial": 0, "user_email": "",
            "caller_id_bind": False, "use_price": False, "price_value": "", "price_size": "8", "price_color": "#000000",
            "price_bold": False, "price_x": "5", "price_y": "15", "price": "",
            "run_script_before_cleanup": False, "script_to_run": "اسم_السكربت"
        }

        try:
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
        except FileNotFoundError:
            settings = default_settings

        settings = {**default_settings, **settings}

        if self.system_type == 'user_manager':
            self.customer_entry.insert(0, settings["customer"])
            self.user_email.set(settings["user_email"])
            self.caller_id_bind_var.set(settings["caller_id_bind"])
            if hasattr(self, 'price_entry'):
                self.price_entry.insert(0, settings["price"])
        elif self.system_type == 'hotspot':
            self.server_entry.insert(0, settings["server"])
            self.limit_bytes_entry.insert(0, settings["limit_bytes"])
            self.limit_unit_combo.set(settings["limit_unit"])
            self.days_entry.insert(0, settings["days"])
            self.email_template_entry.insert(0, settings["email_template"])
            if hasattr(self, 'price_entry'):
                self.price_entry.insert(0, settings["price"])

        self.version_combo.set(settings["version"])
        self.profile_combo.set(settings["profile"])
        self.comment_entry.insert(0, settings["comment"])
        self.location_entry.insert(0, settings["location"])
        self.cred_type_combo.set(settings["cred_type"])
        self.cred_match_combo.set(settings["cred_match"])
        self.length_entry.insert(0, settings["length"])
        self.count_entry.insert(0, settings["count"])
        self.prefix_entry.insert(0, settings["prefix"])
        self.suffix_entry.insert(0, settings["suffix"])
        self.pass_suffix_entry.insert(0, settings["pass_suffix"])
        self.delay_entry.insert(0, settings["delay"])
        self.columns_entry.insert(0, settings["columns"])
        self.rows_entry.insert(0, settings["rows"])
        self.spacing_entry.insert(0, settings["spacing"])
        self.print_username_var.set(settings["print_username"])
        self.print_password_var.set(settings["print_password"])
        self.use_serial_var.set(settings["use_serial"])
        self.use_date_var.set(settings["use_date"])
        self.use_qr_var.set(settings["use_qr"])
        self.username_size_entry.insert(0, settings["username_size"])
        self.username_color_entry.insert(0, settings["username_color"])
        self.username_bold_var.set(settings["username_bold"])
        self.username_x_entry.insert(0, settings["username_x"])
        self.username_y_entry.insert(0, settings["username_y"])
        self.password_size_entry.insert(0, settings["password_size"])
        self.password_color_entry.insert(0, settings["password_color"])
        self.password_bold_var.set(settings["password_bold"])
        self.password_x_entry.insert(0, settings["password_x"])
        self.password_y_entry.insert(0, settings["password_y"])
        self.serial_start_entry.insert(0, settings["serial_start"])
        self.serial_size_entry.insert(0, settings["serial_size"])
        self.serial_color_entry.insert(0, settings["serial_color"])
        self.serial_bold_var.set(settings["serial_bold"])
        self.serial_x_entry.insert(0, settings["serial_x"])
        self.serial_y_entry.insert(0, settings["serial_y"])
        self.date_size_entry.insert(0, settings["date_size"])
        self.date_color_entry.insert(0, settings["date_color"])
        self.date_bold_var.set(settings["date_bold"])
        self.date_x_entry.insert(0, settings["date_x"])
        self.date_y_entry.insert(0, settings["date_y"])
        self.qr_size_entry.insert(0, settings["qr_size"])
        self.qr_x_entry.insert(0, settings["qr_x"])
        self.qr_y_entry.insert(0, settings["qr_y"])
        self.api_ip_entry.insert(0, settings["api_ip"])
        self.api_username_entry.insert(0, settings["api_username"])
        self.api_password_entry.insert(0, self.decrypt_password(settings["api_password"]))  # فك تشفير كلمة المرور
        self.api_port_entry.insert(0, settings["api_port"])
        self.last_serial = int(settings["last_serial"])
        self.use_price_var.set(settings["use_price"])
        self.price_value_entry.insert(0, settings["price_value"])
        self.price_size_entry.insert(0, settings["price_size"])
        self.price_color_entry.insert(0, settings["price_color"])
        self.price_bold_var.set(settings["price_bold"])
        self.price_x_entry.insert(0, settings["price_x"])
        self.price_y_entry.insert(0, settings["price_y"])
        self.run_script_before_cleanup_var.set(settings["run_script_before_cleanup"])
        self.script_to_run_entry.insert(0, settings["script_to_run"])

        if settings["background_image_path"] and os.path.exists(settings["background_image_path"]):
            self.background_image_path = settings["background_image_path"]
            self.background_image = Image.open(self.background_image_path)

        self.toggle_pass_suffix()
        self.toggle_username_inputs()
        self.toggle_password_inputs()
        self.toggle_serial_inputs()
        self.toggle_date_inputs()
        self.toggle_qr_inputs()
        self.toggle_price_inputs()
        self.update_preview()
        self.load_templates()

    def on_closing(self):
        """إغلاق التطبيق مع حفظ البيانات وتنظيف الموارد"""
        try:
            self.logger.info("بدء إغلاق التطبيق")

            # حفظ الإعدادات
            self.save_settings()

            # إنشاء نسخة احتياطية نهائية
            backup_file = self.create_backup()
            if backup_file:
                self.logger.info(f"تم إنشاء نسخة احتياطية نهائية: {backup_file}")

            # قطع الاتصال مع MikroTik
            self.disconnect_api()

            # تسجيل إغلاق التطبيق
            self.log_operation("إغلاق التطبيق", "تم إغلاق التطبيق بنجاح", "نجح")

            self.logger.info("تم إغلاق التطبيق بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ أثناء إغلاق التطبيق: {str(e)}")

        finally:
            # إغلاق النافذة
            self.root.destroy()

    def toggle_pass_suffix(self, event=None):
        if self.cred_match_combo.get() == "كلمة سر فارغة":
            self.pass_suffix_label.grid_remove()
            self.pass_suffix_entry.grid_remove()
        else:
            self.pass_suffix_label.grid()
            self.pass_suffix_entry.grid()
        self.update_preview()

    def toggle_username_inputs(self):
        if self.print_username_var.get():
            self.username_frame.grid()
        else:
            self.username_frame.grid_remove()
        self.update_preview()

    def toggle_password_inputs(self):
        if self.print_password_var.get():
            self.password_frame.grid()
        else:
            self.password_frame.grid_remove()
        self.update_preview()

    def toggle_serial_inputs(self):
        if self.use_serial_var.get():
            self.serial_frame.grid()
        else:
            self.serial_frame.grid_remove()
        self.update_preview()

    def toggle_date_inputs(self):
        if self.use_date_var.get():
            self.date_frame.grid()
        else:
            self.date_frame.grid_remove()
        self.update_preview()

    def toggle_qr_inputs(self):
        if self.use_qr_var.get():
            self.qr_frame.grid()
        else:
            self.qr_frame.grid_remove()
        self.update_preview()

    def toggle_price_inputs(self):
        if self.use_price_var.get():
            self.price_frame.grid()
        else:
            self.price_frame.grid_remove()
        self.update_preview()

    def load_background_image(self):
        file_path = filedialog.askopenfilename(filetypes=[("Image files", "*.png *.jpg *.jpeg")])
        if file_path:
            self.background_image_path = file_path
            self.background_image = Image.open(file_path)
            self.save_settings()
            self.update_preview()

    def on_profile_typing(self, event):
        self.profile_combo.configure(values=self.profiles)
        self.update_preview()

    def fetch_profiles(self):
        api = self.connect_api()
        if not api:
            return
        try:
            version = self.version_combo.get()
            if self.system_type == 'user_manager':
                if version == "v6":
                    profiles = api.get_resource('/tool/user-manager/profile').get()
                else:
                    profiles = api.get_resource('/user-manager/profile').get()
            elif self.system_type == 'hotspot':
                profiles = api.get_resource('/ip/hotspot/user/profile').get()

            self.profiles = [profile['name'] for profile in profiles]
            self.profile_combo.configure(values=self.profiles)
            messagebox.showinfo("نجاح", "تم جلب البروفايلات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل جلب البروفايلات: {str(e)}")
        self.update_preview()

    def fetch_user_count(self):
        api = self.connect_api()
        if not api:
            return
        try:
            version = self.version_combo.get()
            if self.system_type == 'user_manager':
                if version == "v6":
                    users = api.get_resource('/tool/user-manager/user').get()
                else:
                    users = api.get_resource('/user-manager/user').get()
            elif self.system_type == 'hotspot':
                users = api.get_resource('/ip/hotspot/user').get()

            user_count = len(users)
            messagebox.showinfo("عدد الكروت", f"عدد الكروت في السيرفر: {user_count}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل جلب عدد الكروت: {str(e)}")
        self.update_preview()

    def fetch_users(self, event=None):
        """دالة جلب المستخدمين الأصلية - تم تحديثها للتوافق مع النظام الجديد"""
        try:
            # استخدام الدالة الجديدة المحسنة
            self.fetch_all_users()
        except Exception as e:
            self.logger.error(f"خطأ في جلب المستخدمين (الدالة الأصلية): {str(e)}")
            messagebox.showerror("خطأ", f"فشل جلب المستخدمين: {str(e)}")

    def test_api_connection(self):
        """اختبار الاتصال مع MikroTik مع معالجة شاملة للأخطاء"""
        try:
            # التحقق من صحة المدخلات قبل المحاولة
            if not self.validate_api_inputs():
                return

            self.logger.info("بدء اختبار الاتصال مع MikroTik")
            api = self.connect_api()

            if api:
                try:
                    # اختبار إضافي للتأكد من صحة الاتصال
                    identity = api.get_resource('/system/identity').get()
                    router_name = identity[0].get('name', 'غير معروف') if identity else 'غير معروف'

                    messagebox.showinfo("نجاح", f"الاتصال بـ MikroTik ناجح\nاسم الجهاز: {router_name}")
                    self.log_operation("اختبار الاتصال", f"نجح الاتصال مع {router_name}", "نجح")

                except Exception as e:
                    self.logger.error(f"خطأ في اختبار الاتصال الإضافي: {str(e)}")
                    messagebox.showwarning("تحذير", "تم الاتصال ولكن فشل في الحصول على معلومات الجهاز")
            else:
                messagebox.showerror("خطأ", "فشل الاتصال")
                self.log_operation("اختبار الاتصال", "فشل الاتصال", "فشل")

        except Exception as e:
            self.logger.error(f"خطأ في اختبار الاتصال: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في اختبار الاتصال: {str(e)}")

        self.update_preview()

    def validate_api_inputs(self):
        """التحقق من صحة مدخلات API"""
        try:
            host = self.api_ip_entry.get().strip()
            username = self.api_username_entry.get().strip()
            password = self.api_password_entry.get().strip()
            port_str = self.api_port_entry.get().strip()

            # التحقق من الحقول المطلوبة
            if not self.validate_required_field(host, "عنوان IP"):
                return False
            if not self.validate_required_field(username, "اسم المستخدم"):
                return False
            if not self.validate_required_field(password, "كلمة المرور"):
                return False

            # التحقق من صحة عنوان IP
            if not self.validate_ip_address(host):
                self.show_validation_error(f"عنوان IP غير صحيح: {host}")
                return False

            # التحقق من صحة المنفذ
            port = int(port_str) if port_str else 8728
            if not self.validate_port(port):
                self.show_validation_error(f"رقم المنفذ غير صحيح: {port}")
                return False

            return True

        except ValueError:
            self.show_validation_error("رقم المنفذ يجب أن يكون رقماً صحيحاً")
            return False
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من مدخلات API: {str(e)}")
            self.show_validation_error(f"خطأ في التحقق من المدخلات: {str(e)}")
            return False

    def connect_api(self):
        """الاتصال بـ MikroTik مع معالجة شاملة للأخطاء"""
        try:
            if not ROUTEROS_AVAILABLE:
                self.logger.warning("مكتبة routeros_api غير متوفرة")
                messagebox.showerror("خطأ", "مكتبة routeros_api غير مثبتة")
                return None

            # التحقق من الاتصال الحالي
            if self.api_connection and self.api:
                try:
                    # اختبار الاتصال الحالي
                    self.api.get_resource('/system/identity').get()
                    self.logger.info("استخدام الاتصال الحالي")
                    return self.api
                except Exception as e:
                    self.logger.warning(f"الاتصال الحالي غير صالح: {str(e)}")
                    # إغلاق الاتصال القديم
                    self.disconnect_api()

            # الحصول على بيانات الاتصال
            host = self.api_ip_entry.get().strip()
            username = self.api_username_entry.get().strip()
            password = self.api_password_entry.get().strip()
            port = int(self.api_port_entry.get().strip() or 8728)

            self.logger.info(f"محاولة الاتصال بـ {host}:{port}")

            # إنشاء اتصال جديد
            self.api_connection = routeros_api.RouterOsApiPool(
                host=host,
                username=username,
                password=password,  # استخدام كلمة المرور كما هي
                port=port,
                plaintext_login=True
            )

            self.api = self.api_connection.get_api()

            # اختبار الاتصال
            identity = self.api.get_resource('/system/identity').get()

            self.connection_status_var.set("متصل")
            self.logger.info(f"نجح الاتصال مع {host}")

            return self.api

        except Exception as e:
            error_type = type(e).__name__
            error_msg = str(e)

            # تحديد نوع الخطأ وتقديم رسالة واضحة مع نصائح الحل
            if "RouterOsApiConnectionError" in error_type or "connection" in error_msg.lower():
                troubleshooting = self.get_connection_troubleshooting_tips(host, port)
                user_msg = f"فشل في الاتصال مع MikroTik:\n\nنصائح لحل المشكلة:\n{troubleshooting}\n\nتفاصيل الخطأ: {error_msg}"
            elif "login" in error_msg.lower() or "authentication" in error_msg.lower():
                user_msg = f"خطأ في المصادقة:\n• تأكد من صحة اسم المستخدم: {username}\n• تأكد من صحة كلمة المرور\n• تأكد من صلاحيات المستخدم للوصول إلى API\n• جرب تسجيل الدخول عبر Winbox أولاً\n\nتفاصيل الخطأ: {error_msg}"
            elif "timeout" in error_msg.lower():
                user_msg = f"انتهت مهلة الاتصال:\n• تأكد من أن الجهاز متصل بالشبكة\n• تأكد من عدم وجود جدار حماية يحجب الاتصال\n• جرب زيادة timeout في إعدادات الشبكة\n\nتفاصيل الخطأ: {error_msg}"
            else:
                troubleshooting = self.get_connection_troubleshooting_tips(host, port)
                user_msg = f"خطأ غير متوقع في الاتصال:\n\nنصائح لحل المشكلة:\n{troubleshooting}\n\nتفاصيل الخطأ: {error_msg}"

            self.logger.error(f"خطأ في الاتصال - النوع: {error_type}, الرسالة: {error_msg}")
            messagebox.showerror("خطأ في الاتصال", user_msg)
            self.connection_status_var.set("غير متصل")
            self.disconnect_api()
            return None

    def disconnect_api(self):
        """قطع الاتصال مع MikroTik"""
        try:
            if self.api_connection:
                self.api_connection.disconnect()
                self.logger.info("تم قطع الاتصال مع MikroTik")
        except Exception as e:
            self.logger.error(f"خطأ في قطع الاتصال: {str(e)}")
        finally:
            self.api_connection = None
            self.api = None
            self.connection_status_var.set("غير متصل")

    def check_mikrotik_api_service(self, host, port):
        """فحص ما إذا كانت خدمة API مفعلة في MikroTik"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)  # 5 ثوان timeout
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception as e:
            self.logger.error(f"خطأ في فحص خدمة API: {str(e)}")
            return False

    def get_connection_troubleshooting_tips(self, host, port):
        """الحصول على نصائح لحل مشاكل الاتصال"""
        tips = []

        # فحص إمكانية الوصول للمنفذ
        if not self.check_mikrotik_api_service(host, port):
            tips.extend([
                f"• المنفذ {port} غير متاح على {host}",
                "• تأكد من تفعيل خدمة API في MikroTik:",
                "  /ip service enable api",
                f"  /ip service set api port={port}",
                "• تأكد من عدم حجب المنفذ بواسطة Firewall"
            ])

        # نصائح عامة
        tips.extend([
            "• تأكد من صحة عنوان IP",
            "• تأكد من أن الجهاز متصل بالشبكة",
            "• جرب استخدام Winbox للتأكد من الاتصال",
            "• تأكد من صلاحيات المستخدم للوصول إلى API"
        ])

        return "\n".join(tips)

    def send_to_mikrotik_with_pdf(self):
        if not self.generated_credentials:
            messagebox.showwarning("تحذير", "يرجى توليد الحسابات أولاً")
            return

        pdf_filename = self.get_pdf_filename()
        if pdf_filename:
            self.save_pdf_to_file(pdf_filename)
            self.send_to_mikrotik()
        else:
            messagebox.showinfo("إلغاء", "تم إلغاء حفظ الملف، لن يتم إرسال السكربت.")

    def generate_and_send_fast_with_pdf(self):
        """توليد وإرسال سريع مع PDF محسن للأداء - يدعم جميع الأنظمة"""
        if not self.generate_credentials():
            return

        try:
            # توليد السكريبت حسب نوع النظام
            if self.system_type == 'user_manager':
                script_source = self.generate_user_manager_fast_script()
                if not script_source:
                    return
            elif self.system_type == 'hotspot':
                script_source = self.generate_hotspot_fast_script()
                if not script_source:
                    return
            else:
                messagebox.showerror("خطأ", "نوع النظام غير مدعوم")
                return

            # إضافة ميزات إضافية للسكريبت
            script_lines = script_source.split('\n')

            # إضافة تشغيل سكربت قبل التنظيف للهوت سبوت إذا تم تفعيل الخيار
            if self.system_type == 'hotspot' and self.run_script_before_cleanup_var.get():
                script_to_run = self.script_to_run_entry.get().strip()
                if script_to_run:
                    script_lines.extend([
                        '',
                        ':put "⏳ انتظار 3 ثواني قبل تشغيل السكريبت المحدد...";',
                        '/delay 3s;',
                        '',
                        f':put "🔧 تشغيل السكريبت: {script_to_run}";',
                        f':do {{ /sys script run "{script_to_run}"; }} on-error={{ :put "❌ خطأ في تشغيل السكريبت: {script_to_run}"; }};',
                        ':put "✅ تم تشغيل السكريبت بنجاح!";'
                    ])

            # إنشاء اسم السكريبت
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            script_name = f"fast_pdf_{self.system_type}_{timestamp}"

            # إضافة أوامر التنظيف التلقائي
            script_lines.extend([
                '',
                ':put "🧹 تنظيف السكريبت...";',
                f'/system script remove [/system script find where name="{script_name}"];',
                f'/system scheduler remove [/system scheduler find where name="{script_name}"];',
                ':put "🎉 تم الانتهاء من العملية بنجاح!";'
            ])

            final_script = '\n'.join(script_lines)

            # عرض السكريبت في النافذة
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(tk.END, final_script)

            # إنشاء PDF
            pdf_filename = self.get_pdf_filename()
            if pdf_filename:
                # إنشاء نافذة تقدم
                progress_window = self.create_progress_window("الوضع السريع + PDF", "جاري إنشاء PDF...")

                try:
                    # حفظ PDF
                    progress_window.update_status("📄 إنشاء ملف PDF...")
                    self.save_pdf_to_file(pdf_filename)

                    # الاتصال بـ MikroTik
                    progress_window.update_status("🌐 الاتصال بـ MikroTik...")
                    api = self.connect_api()
                    if not api:
                        progress_window.destroy()
                        return

                    # إضافة السكريبت
                    progress_window.update_status("📝 إضافة السكريبت إلى MikroTik...")
                    api.get_resource('/system/script').add(name=script_name, source=final_script)

                    # إعداد الجدولة للتشغيل المؤجل
                    progress_window.update_status("⏰ إعداد الجدولة...")
                    clock = api.get_resource('/system/clock').get()[0]
                    current_time_str = clock['time']
                    current_date_str = clock['date']
                    current_time = datetime.strptime(current_time_str, '%H:%M:%S')
                    current_date = datetime.strptime(current_date_str, '%b/%d/%Y')
                    current_datetime = datetime.combine(current_date, current_time.time())
                    start_time = current_datetime + timedelta(seconds=5)  # تأخير 5 ثوان
                    start_time_str = start_time.strftime('%H:%M:%S')
                    start_date_str = start_time.strftime('%b/%d/%Y')

                    api.get_resource('/system/scheduler').add(
                        name=script_name,
                        start_time=start_time_str,
                        start_date=start_date_str,
                        interval="00:00:00",
                        on_event=script_name
                    )

                    progress_window.destroy()

                    messagebox.showinfo("نجح",
                        f"🎉 تم إنشاء PDF وجدولة السكريبت بنجاح!\n\n"
                        f"✅ ملف PDF: تم الحفظ\n"
                        f"✅ السكريبت: '{script_name}'\n"
                        f"⏰ التشغيل: بعد 5 ثوان\n"
                        f"📊 المستخدمين: {len(self.generated_credentials)}\n"
                        f"🗂️ النظام: {self.system_type.upper()}")

                    self.logger.info(f"تم إنشاء PDF وجدولة السكريبت السريع لـ {len(self.generated_credentials)} مستخدم")

                except Exception as e:
                    progress_window.destroy()
                    self.logger.error(f"خطأ في العملية السريعة مع PDF: {str(e)}")

                    # محاولة تنظيف السكريبت في حالة الخطأ
                    try:
                        api.get_resource('/system/script').remove(id=script_name)
                        api.get_resource('/system/scheduler').remove(id=script_name)
                    except:
                        pass

                    messagebox.showerror("خطأ", f"فشل في العملية: {str(e)}")
            else:
                messagebox.showinfo("إلغاء", "تم إلغاء حفظ الملف، لن يتم إرسال السكربت.")

        except Exception as e:
            self.logger.error(f"خطأ في العملية السريعة مع PDF: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في العملية: {str(e)}")

        self.update_preview()
        self.save_settings()

    def get_pdf_filename(self):
        date = datetime.now().strftime("%Y%m%d")
        prefix = self.prefix_entry.get().strip()
        system_prefix = "usermanager" if self.system_type == 'user_manager' else "hotspot"
        default_filename = f"{system_prefix}_cards_{prefix}{len(self.generated_credentials)}_{date}.pdf"

        filename = filedialog.asksaveasfilename(defaultextension=".pdf", filetypes=[("PDF files", "*.pdf")], initialfile=default_filename)
        return filename

    def save_pdf_to_file(self, filename):
        try:
            columns = int(self.columns_entry.get())
            rows = int(self.rows_entry.get())
            spacing = float(self.spacing_entry.get())
            manual_serial = self.serial_start_entry.get().strip()
            serial_start = int(manual_serial) if manual_serial else (self.last_serial + 1)
            if serial_start <= self.last_serial:
                serial_start = self.last_serial + 1

            pdfmetrics.registerFont(TTFont('Arial', 'Arial.ttf'))
            pdfmetrics.registerFont(TTFont('Arial-Bold', 'Arialbd.ttf'))
            c = canvas.Canvas(filename, pagesize=A4)
            page_width, page_height = A4
            margin_x = 1 * mm
            margin_y = 5 * mm

            safe_columns = max(1, columns)
            safe_rows = max(1, rows)
            cards_per_page = safe_columns * safe_rows

            available_width = page_width - 2 * margin_x
            available_height = page_height - 2 * margin_y
            total_horizontal_spacing = spacing * mm * (safe_columns - 1)
            total_vertical_spacing = spacing * mm * (safe_rows - 1)

            box_width = (available_width - total_horizontal_spacing) / safe_columns
            box_height = (available_height - total_vertical_spacing) / safe_rows

            for i, card in enumerate(self.generated_credentials):
                page = i // cards_per_page
                index_on_page = i % cards_per_page
                col = index_on_page // safe_rows
                row = index_on_page % safe_rows

                if i % cards_per_page == 0 and i != 0:
                    c.showPage()

                x = margin_x + col * (box_width + spacing * mm)
                y = page_height - margin_y - (row + 1) * box_height - (row * spacing * mm)

                c.setStrokeColor("#000000")
                c.setLineWidth(0.5 * mm)
                c.rect(x, y, box_width, box_height)

                if self.background_image_path:
                    c.drawImage(self.background_image_path, x, y, box_width, box_height, preserveAspectRatio=True)

                if self.print_username_var.get():
                    size = float(self.username_size_entry.get() or 8)
                    x_pos = x + float(self.username_x_entry.get() or 30) * mm
                    y_pos = y + float(self.username_y_entry.get() or 5) * mm
                    color = self.username_color_entry.get() or "#000000"
                    bold = self.username_bold_var.get()
                    c.setFont("Arial" + ("-Bold" if bold else ""), size)
                    c.setFillColor(color)
                    c.drawString(x_pos, y_pos, card["username"])

                if self.print_password_var.get() and card["password"]:
                    size = float(self.password_size_entry.get() or 8)
                    x_pos = x + float(self.password_x_entry.get() or 30) * mm
                    y_pos = y + float(self.password_y_entry.get() or 10) * mm
                    color = self.password_color_entry.get() or "#000000"
                    bold = self.password_bold_var.get()
                    c.setFont("Arial" + ("-Bold" if bold else ""), size)
                    c.setFillColor(color)
                    c.drawString(x_pos, y_pos, card["password"])

                if self.use_serial_var.get():
                    size = float(self.serial_size_entry.get() or 6)
                    x_pos = x + float(self.serial_x_entry.get() or 5) * mm
                    y_pos = y + float(self.serial_y_entry.get() or 5) * mm
                    color = self.serial_color_entry.get() or "#000000"
                    bold = self.serial_bold_var.get()
                    serial = card["location"] if card["location"].isdigit() else str(serial_start + i)
                    c.setFont("Arial" + ("-Bold" if bold else ""), size)
                    c.setFillColor(color)
                    c.drawString(x_pos, y_pos, serial)

                if self.use_date_var.get():
                    size = float(self.date_size_entry.get() or 8)
                    x_pos = x + float(self.date_x_entry.get() or 48) * mm
                    y_pos = y + float(self.date_y_entry.get() or 12) * mm
                    color = self.date_color_entry.get() or "#000000"
                    bold = self.date_bold_var.get()
                    today = datetime.now().strftime("%Y-%m-%d")
                    c.setFont("Arial" + ("-Bold" if bold else ""), size)
                    c.setFillColor(color)
                    c.drawString(x_pos, y_pos, today)

                if self.use_qr_var.get():
                    qr_size_mm = float(self.qr_size_entry.get() or 10)
                    x_pos = x + float(self.qr_x_entry.get() or 5) * mm
                    y_pos = y + float(self.qr_y_entry.get() or 5) * mm
                    qr_data = card['username']
                    qr = qrcode.QRCode(version=1, box_size=10, border=1)
                    qr.add_data(qr_data)
                    qr.make(fit=True)
                    qr_img = qr.make_image(fill_color="black", back_color="white")
                    img_byte_arr = io.BytesIO()
                    qr_img.save(img_byte_arr, format='PNG')
                    img_byte_arr.seek(0)
                    c.drawImage(ImageReader(img_byte_arr), x_pos, y_pos, qr_size_mm * mm, qr_size_mm * mm)

                if self.use_price_var.get():
                    size = float(self.price_size_entry.get() or 8)
                    x_pos = x + float(self.price_x_entry.get() or 5) * mm
                    y_pos = y + float(self.price_y_entry.get() or 15) * mm
                    color = self.price_color_entry.get() or "#000000"
                    bold = self.price_bold_var.get()
                    price = self.price_value_entry.get()
                    c.setFont("Arial" + ("-Bold" if bold else ""), size)
                    c.setFillColor(color)
                    c.drawString(x_pos, y_pos, price)

            c.save()
            messagebox.showinfo("نجاح", f"تم حفظ ملف PDF: {filename}")
            self.save_settings()
        except ValueError as e:
            messagebox.showerror("خطأ", "يرجى إدخال قيم عددية صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ PDF: {str(e)}")
        self.update_preview()

    def send_to_mikrotik(self):
        if not ROUTEROS_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة routeros_api غير مثبتة.")
            return
        if not self.generated_credentials:
            messagebox.showwarning("تحذير", "يرجى توليد الحسابات أولاً")
            return

        api = self.connect_api()
        if not api:
            return

        try:
            version = self.version_combo.get()

            if self.system_type == 'user_manager':
                customer = self.customer_entry.get().strip()
                existing_users = api.get_resource('/tool/user-manager/user' if version == "v6" else '/user-manager/user').get()
                existing_usernames = {user['username'] for user in existing_users}
            elif self.system_type == 'hotspot':
                server = self.server_entry.get().strip()
                existing_users = api.get_resource('/ip/hotspot/user').get()
                existing_usernames = {user['name'] for user in existing_users}

            total = len(self.generated_credentials)
            self.progress_var.set(0)
            self.progress_bar.update()

            duplicates = []
            for i, cred in enumerate(self.generated_credentials):
                cred_username = cred["username"]
                if cred_username in existing_usernames:
                    duplicates.append(cred_username)
                else:
                    try:
                        if self.system_type == 'user_manager':
                            if version == "v6":
                                user_manager = api.get_resource('/tool/user-manager/user')
                                user_manager.add(customer=customer, username=cred_username, password=cred["password"],
                                                 comment=cred["comment"], location=cred["location"], email=self.user_email.get())
                                api.get_resource('/tool/user-manager/user').call('create-and-activate-profile',
                                    {'customer': customer, 'profile': cred["profile"], 'numbers': cred_username})
                            else:
                                user_manager = api.get_resource('/user-manager/user')
                                user_manager.add(name=cred_username, password=cred["password"], comment=cred["comment"],
                                                 location=cred["location"], email=self.user_email.get())
                                api.get_resource('/user-manager/user-profile').add(profile=cred["profile"], user=cred_username)
                        elif self.system_type == 'hotspot':
                            limit_bytes = cred.get("limit_bytes", "")
                            limit_unit = cred.get("limit_unit", "GB")
                            days = cred.get("days", "")
                            email_template = cred.get("email_template", "@sa.sa")

                            params = {'name': cred_username, 'password': cred["password"], 'profile': cred["profile"],
                                      'comment': cred["comment"], 'server': server}
                            if limit_bytes:
                                params['limit-bytes-total'] = str(int(float(limit_bytes) * (1073741824 if limit_unit == "GB" else 1048576)))
                            if days:
                                params['email'] = f"{days}{email_template}"
                            api.get_resource('/ip/hotspot/user').add(**params)
                    except routeros_api.exceptions.RouterOsApiConnectionError:
                        api = self.connect_api()
                        if not api:
                            return
                    except Exception as e:
                        messagebox.showerror("خطأ", f"فشل إرسال الحساب {cred_username}: {str(e)}")
                        return

                progress = ((i + 1) / total) * 100
                self.progress_var.set(progress)
                self.root.update()

            self.progress_var.set(0)
            self.connection_status_var.set("متصل")

            if duplicates:
                response = messagebox.askyesno("تحذير", f"تم العثور على أسماء مستخدمين مكررة: {', '.join(duplicates)}\nهل تريد الاستمرار وتخطي الأسماء المكررة؟")
                if not response:
                    messagebox.showinfo("إلغاء", "تم إلغاء العملية")
                    return
                else:
                    self.generated_credentials = [cred for cred in self.generated_credentials if cred["username"] not in duplicates]
                    messagebox.showinfo("نجاح", "تم إرسال الحسابات إلى MikroTik مع تخطي المكررات")
            else:
                messagebox.showinfo("نجاح", "تم إرسال الحسابات إلى MikroTik بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل الإرسال: {str(e)}")
            self.connection_status_var.set("غير متصل")
        self.update_preview()

    def generate_random_string(self, cred_type, length):
        numbers = string.digits
        letters = string.ascii_letters
        mixed = numbers + letters

        chars = {"أرقام وحروف": mixed, "أرقام فقط": numbers, "حروف فقط": letters}.get(cred_type, mixed)
        return ''.join(random.choice(chars) for _ in range(length))

    def generate_credentials(self):
        try:
            comment = self.comment_entry.get().strip()
            profile = self.profile_combo.get().strip()
            prefix = self.prefix_entry.get().strip()
            suffix = self.suffix_entry.get().strip()
            pass_suffix = self.pass_suffix_entry.get().strip()
            cred_type = self.cred_type_combo.get()
            cred_match = self.cred_match_combo.get()
            length = int(self.length_entry.get())
            count = int(self.count_entry.get())
            use_serial = self.use_serial_var.get()
            manual_serial = self.serial_start_entry.get().strip()
            serial_start = int(manual_serial) if manual_serial else (self.last_serial + 1)
            if serial_start <= self.last_serial:
                serial_start = self.last_serial + 1
            columns = int(self.columns_entry.get())
            rows = int(self.rows_entry.get())
            cards_per_page = max(1, columns) * max(1, rows)
            default_location = self.location_entry.get().strip()
            price = self.price_entry.get() if hasattr(self, 'price_entry') else ""

            if length < 5 or count < 1 or not profile:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للحقول المطلوبة")
                return False

            if self.system_type == 'user_manager' and not self.customer_entry.get().strip():
                messagebox.showerror("خطأ", "حقل 'العميل' مطلوب لـ User Manager")
                return False
            if self.system_type == 'hotspot' and not self.server_entry.get().strip():
                messagebox.showerror("خطأ", "حقل 'اسم السيرفر' مطلوب لـ Hotspot")
                return False

            self.generated_credentials = []
            usernames = set()

            for i in range(count):
                attempts = 0
                max_attempts = 100
                while attempts < max_attempts:
                    base = self.generate_random_string(cred_type, length)
                    username = prefix + base + suffix
                    if username not in usernames:
                        usernames.add(username)
                        password = "" if cred_match == "كلمة سر فارغة" else (prefix + base + pass_suffix if cred_match == "متشابهة" else self.generate_random_string(cred_type, length) + pass_suffix)

                        card_location = default_location
                        if use_serial:
                            page = i // cards_per_page
                            col = (i % cards_per_page) // max(1, rows)
                            row = (i % cards_per_page) % max(1, rows)
                            current_serial = serial_start + (page * cards_per_page) + (col * max(1, rows)) + row
                            card_location = str(current_serial)

                        cred = {"username": username, "password": password, "profile": profile, "comment": comment, "location": card_location, "price": price}

                        if self.system_type == 'user_manager':
                            cred["email"] = self.user_email.get()
                        elif self.system_type == 'hotspot':
                            cred.update({"limit_bytes": self.limit_bytes_entry.get().strip(), "limit_unit": self.limit_unit_combo.get(),
                                         "days": self.days_entry.get().strip(), "email_template": self.email_template_entry.get().strip()})

                        self.generated_credentials.append(cred)
                        break
                    attempts += 1
                if attempts >= max_attempts:
                    messagebox.showwarning("تحذير", f"تعذر توليد مستخدم فريد بعد {max_attempts} محاولة")
                    break

            if use_serial:
                self.last_serial = serial_start + len(self.generated_credentials) - 1

            return True
        except ValueError as e:
            messagebox.showerror("خطأ", "يرجى إدخال قيم عددية صحيحة")
            return False

    def generate_all(self):
        if not self.generate_credentials():
            return

        script = ""
        version = self.version_combo.get()
        delay = int(self.delay_entry.get())

        for i, cred in enumerate(self.generated_credentials):
            if self.system_type == 'user_manager':
                customer = self.customer_entry.get().strip()
                if version == "v6":
                    script += f'/tool user-manager user add customer="{customer}" username="{cred["username"]}" password="{cred["password"]}" comment="{cred["comment"]}" location="{cred["location"]}" email="{cred["email"]}";\n'
                else:
                    script += f'/user-manager user add name="{cred["username"]}" password="{cred["password"]}" comment="{cred["comment"]}" location="{cred["location"]}" email="{cred["email"]}";\n'
                    script += f'/user-manager user-profile add profile="{cred["profile"]}" user="{cred["username"]}";\n'
            elif self.system_type == 'hotspot':
                server = self.server_entry.get().strip()
                script_parts = [f'/ip hotspot user add name="{cred["username"]}"', f'password="{cred["password"]}"',
                                f'profile="{cred["profile"]}"', f'comment="{cred["comment"]}"', f'server="{server}"']
                if cred["limit_bytes"]:
                    bytes_limit = str(int(float(cred["limit_bytes"]) * (1073741824 if cred["limit_unit"] == "GB" else 1048576)))
                    script_parts.append(f'limit-bytes-total="{bytes_limit}"')
                if cred["days"]:
                    script_parts.append(f'email="{cred["days"]}{cred["email_template"]}"')
                script += ' '.join(script_parts) + ';\n'

            if delay > 0 and (i + 1) % 100 == 0 and i < len(self.generated_credentials) - 1:
                script += f'/delay {delay}ms;\n'

        if self.system_type == 'user_manager' and version == "v6":
            customer = self.customer_entry.get().strip()
            profile = self.profile_combo.get().strip()
            numbers = ",".join([cred["username"] for cred in self.generated_credentials])
            script += f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers}";\n'

        # إضافة تشغيل سكربت قبل التنظيف للهوت سبوت إذا تم تفعيل الخيار
        if self.system_type == 'hotspot' and self.run_script_before_cleanup_var.get():
            script_to_run = self.script_to_run_entry.get().strip()
            if script_to_run:
                script += f'/delay 3s;\n'
                script += f'/sys script run "{script_to_run}";\n'

        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, script)

        conn = sqlite3.connect('mikrotik_cards.db')
        cursor = conn.cursor()
        if self.system_type == 'user_manager':
            cursor.execute("DELETE FROM user_manager_credentials")
            for cred in self.generated_credentials:
                cursor.execute('INSERT INTO user_manager_credentials (username, password, profile, comment, location, email, price) VALUES (?, ?, ?, ?, ?, ?, ?)',
                              (cred["username"], cred["password"], cred["profile"], cred["comment"], cred["location"], cred["email"], cred["price"]))
        elif self.system_type == 'hotspot':
            cursor.execute("DELETE FROM hotspot_credentials")
            for cred in self.generated_credentials:
                cursor.execute('INSERT INTO hotspot_credentials (username, password, profile, comment, location, limit_bytes, limit_unit, days, email_template, price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                              (cred["username"], cred["password"], cred["profile"], cred["comment"], cred["location"],
                               cred["limit_bytes"], cred["limit_unit"], cred["days"], cred["email_template"], cred["price"]))
        conn.commit()
        conn.close()

        messagebox.showinfo("نجاح", f"تم توليد {len(self.generated_credentials)} حساب")



        self.update_preview()
        self.save_settings()

    def generate_very_fast(self):
        """توليد سكريبت سريع جداً محسن للأداء - يدعم جميع الأنظمة"""
        if not self.generate_credentials():
            return

        try:
            if self.system_type == 'user_manager':
                script_source = self.generate_user_manager_fast_script()
            elif self.system_type == 'hotspot':
                script_source = self.generate_hotspot_fast_script()
            else:
                messagebox.showerror("خطأ", "نوع النظام غير مدعوم")
                return

            if script_source:
                # إضافة تشغيل سكربت قبل التنظيف للهوت سبوت إذا تم تفعيل الخيار
                if self.system_type == 'hotspot' and self.run_script_before_cleanup_var.get():
                    script_to_run = self.script_to_run_entry.get().strip()
                    if script_to_run:
                        script_lines = script_source.split('\n')
                        # إضافة الأوامر قبل النهاية
                        script_lines.extend([
                            '',
                            ':put "⏳ انتظار 3 ثواني قبل تشغيل السكريبت المحدد...";',
                            '/delay 3s;',
                            '',
                            f':put "🔧 تشغيل السكريبت: {script_to_run}";',
                            f':do {{ /sys script run "{script_to_run}"; }} on-error={{ :put "❌ خطأ في تشغيل السكريبت: {script_to_run}"; }};',
                            ':put "✅ تم تشغيل السكريبت بنجاح!";'
                        ])
                        script_source = '\n'.join(script_lines)

                self.output_text.delete(1.0, tk.END)
                self.output_text.insert(tk.END, script_source)
                messagebox.showinfo("نجاح", f"تم توليد السكريبت السريع بنجاح لـ {len(self.generated_credentials)} مستخدم")
            else:
                messagebox.showerror("خطأ", "فشل في توليد السكريبت")

        except Exception as e:
            self.logger.error(f"خطأ في توليد السكريبت السريع: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في توليد السكريبت: {str(e)}")

        self.update_preview()
        self.save_settings()

    def generate_hotspot_fast_only(self):
        """توليد سكريبت سريع لـ Hotspot مع التوليد المرحلي للحسابات الكبيرة - محسن ومتقدم"""
        if not self.generate_credentials():
            return

        try:
            total_users = len(self.generated_credentials)
            batch_size = 100  # حجم المرحلة الواحدة

            # تحديد ما إذا كان التقسيم المرحلي مطلوب
            use_batching = total_users > batch_size

            if use_batching:
                # حساب عدد المراحل
                num_batches = (total_users + batch_size - 1) // batch_size

                # إنشاء نافذة تقدم للتوليد المرحلي
                progress_window = self.create_progress_window(
                    "التوليد السريع المرحلي",
                    f"جاري توليد {total_users} مستخدم في {num_batches} مراحل..."
                )

                # توليد السكريبت المرحلي
                script_source = self.generate_hotspot_batched_script(progress_window, batch_size)
                progress_window.destroy()
            else:
                # التوليد العادي للحسابات الصغيرة
                script_source = self.generate_hotspot_fast_script()

            if not script_source:
                messagebox.showerror("خطأ", "فشل في توليد السكريبت السريع")
                return

            # إضافة ميزة تشغيل سكربت قبل التنظيف إذا تم تفعيلها
            if self.run_script_before_cleanup_var.get():
                script_to_run = self.script_to_run_entry.get().strip()
                if script_to_run:
                    script_lines = script_source.split('\n')
                    # إضافة الأوامر قبل النهاية
                    script_lines.extend([
                        '',
                        ':put "⏳ انتظار 3 ثواني قبل تشغيل السكريبت المحدد...";',
                        '/delay 3s;',
                        '',
                        f':put "🔧 تشغيل السكريبت: {script_to_run}";',
                        f':do {{ /sys script run "{script_to_run}"; }} on-error={{ :put "❌ خطأ في تشغيل السكريبت: {script_to_run}"; }};',
                        ':put "✅ تم تشغيل السكريبت بنجاح!";'
                    ])
                    script_source = '\n'.join(script_lines)

            # عرض السكريبت في منطقة النص
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(tk.END, script_source)

            # حفظ البيانات في قاعدة البيانات
            conn = sqlite3.connect('mikrotik_cards.db')
            cursor = conn.cursor()
            cursor.execute("DELETE FROM hotspot_credentials")
            for cred in self.generated_credentials:
                cursor.execute('INSERT INTO hotspot_credentials (username, password, profile, comment, location, limit_bytes, limit_unit, days, email_template, price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
                              (cred["username"], cred["password"], cred["profile"], cred["comment"], cred["location"],
                               cred["limit_bytes"], cred["limit_unit"], cred["days"], cred["email_template"], cred["price"]))
            conn.commit()
            conn.close()

            # رسالة نجاح مع تفاصيل محسنة
            success_msg = f"🎉 تم توليد السكريبت السريع بنجاح!\n\n"
            success_msg += f"📊 العدد الإجمالي: {total_users} مستخدم\n"
            success_msg += f"🗂️ النظام: Hotspot\n"

            if use_batching:
                num_batches = (total_users + batch_size - 1) // batch_size
                success_msg += f"⚡ النوع: سكريبت مرحلي محسن ({num_batches} مراحل)\n"
                success_msg += f"📦 حجم المرحلة: {batch_size} مستخدم/مرحلة\n"
                success_msg += f"⏱️ تأخير بين المراحل: 3 ثواني\n"
            else:
                success_msg += f"⚡ النوع: سكريبت محسن بـ arrays\n"

            success_msg += f"🎯 البروفايل: {self.profile_combo.get()}\n"

            if self.server_entry.get().strip():
                success_msg += f"🖥️ الخادم: {self.server_entry.get().strip()}\n"

            if self.limit_bytes_entry.get().strip():
                success_msg += f"📊 حد البيانات: {self.limit_bytes_entry.get()} {self.limit_unit_combo.get()}\n"

            if self.days_entry.get().strip():
                success_msg += f"📅 الأيام: {self.days_entry.get()}\n"

            if self.run_script_before_cleanup_var.get() and self.script_to_run_entry.get().strip():
                success_msg += f"🔧 سكريبت إضافي: {self.script_to_run_entry.get().strip()}\n"

            success_msg += f"\n💡 السكريبت جاهز للنسخ أو الحفظ!"

            messagebox.showinfo("نجح", success_msg)
            self.logger.info(f"تم توليد السكريبت السريع لـ Hotspot: {total_users} مستخدم" +
                           (f" في {num_batches} مراحل" if use_batching else ""))

        except Exception as e:
            self.logger.error(f"خطأ في توليد السكريبت السريع لـ Hotspot: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في توليد السكريبت السريع: {str(e)}")

        self.update_preview()
        self.save_settings()

    def generate_hotspot_batched_script(self, progress_window, batch_size=100):
        """توليد سكريبت Hotspot مرحلي للحسابات الكبيرة مع تأخير بين المراحل"""
        try:
            profile = self.profile_combo.get().strip()
            if not profile:
                messagebox.showerror("خطأ", "يرجى تحديد البروفايل")
                return None

            server = self.server_entry.get().strip()
            total_users = len(self.generated_credentials)
            num_batches = (total_users + batch_size - 1) // batch_size

            # بداية السكريبت المرحلي
            script_lines = [
                f':put "🚀 بدء التوليد المرحلي لـ Hotspot - {total_users} مستخدم في {num_batches} مراحل";',
                f':local totalUsers {total_users};',
                f':local batchSize {batch_size};',
                f':local numBatches {num_batches};',
                ':local globalSuccess 0;',
                ':local globalErrors 0;',
                ':local currentBatch 0;',
                '',
                ':put "📊 إحصائيات التوليد:";',
                f':put "   📦 حجم المرحلة: $batchSize مستخدم";',
                f':put "   🔢 عدد المراحل: $numBatches مرحلة";',
                f':put "   📈 العدد الإجمالي: $totalUsers مستخدم";',
                '',
                ':put "⏱️ بدء المعالجة المرحلية...";',
                ''
            ]

            # توليد كل مرحلة
            for batch_num in range(num_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, total_users)
                batch_users = self.generated_credentials[start_idx:end_idx]
                current_batch_size = len(batch_users)

                # تحديث نافذة التقدم
                progress_window.update_status(f"📦 توليد المرحلة {batch_num + 1} من {num_batches} ({current_batch_size} مستخدم)...")

                # بداية المرحلة
                script_lines.extend([
                    f'# ===== المرحلة {batch_num + 1} من {num_batches} =====',
                    f':set currentBatch ($currentBatch + 1);',
                    f':put "📦 بدء المرحلة $currentBatch من $numBatches ({current_batch_size} مستخدم)...";',
                    '',
                    f':local batch{batch_num + 1}Users {{',
                ])

                # إضافة مستخدمي المرحلة
                for cred in batch_users:
                    # معالجة حد البيانات
                    limit_bytes = ""
                    if hasattr(self, 'limit_bytes_entry') and self.limit_bytes_entry.get():
                        try:
                            limit_value = float(self.limit_bytes_entry.get())
                            limit_unit = self.limit_unit_combo.get() if hasattr(self, 'limit_unit_combo') else "GB"

                            if limit_unit == "GB":
                                limit_bytes = str(int(limit_value * 1024 * 1024 * 1024))
                            elif limit_unit == "MB":
                                limit_bytes = str(int(limit_value * 1024 * 1024))
                            elif limit_unit == "KB":
                                limit_bytes = str(int(limit_value * 1024))
                            else:
                                limit_bytes = str(int(limit_value))
                        except (ValueError, AttributeError):
                            limit_bytes = ""

                    # معالجة الإيميل والأيام
                    email = ""
                    if hasattr(self, 'days_entry') and self.days_entry.get():
                        try:
                            days = self.days_entry.get()
                            email_template = self.email_template_entry.get() if hasattr(self, 'email_template_entry') else "@sa.sa"
                            email = f"{days}{email_template}"
                        except AttributeError:
                            email = ""

                    # معالجة التعليق
                    comment = self.comment_entry.get() if hasattr(self, 'comment_entry') else ""

                    # إنشاء سطر المستخدم
                    user_data = [
                        cred["password"],
                        profile,
                        limit_bytes if limit_bytes else "",
                        email if email else "",
                        comment if comment else "",
                        server if server else ""
                    ]

                    user_line = f'    "{cred["username"]}"="' + ",".join(user_data) + '" ;'
                    script_lines.append(user_line)

                # إنهاء array المرحلة
                if batch_users:
                    script_lines[-1] = script_lines[-1].rstrip(' ;')

                script_lines.extend([
                    '};',
                    '',
                    f':local batchCount 0;',
                    f':local batchSuccess 0;',
                    f':local batchErrors 0;',
                    f':local batchTotal [:len $batch{batch_num + 1}Users];',
                    '',
                    f':foreach user,data in=$batch{batch_num + 1}Users do={{',
                    '    :local userdata [:toarray $data];',
                    '    :local username $user;',
                    '    :local password [:pick $userdata 0];',
                    '    :local profile [:pick $userdata 1];',
                    '    :local limitbytes [:pick $userdata 2];',
                    '    :local email [:pick $userdata 3];',
                    '    :local comment [:pick $userdata 4];',
                    '    :local server [:pick $userdata 5];',
                    '',
                    '    :do {',
                    '        :local addcmd "/ip hotspot user add name=\\"$username\\" password=\\"$password\\" profile=\\"$profile\\"";',
                    '        :if ([:len $limitbytes] > 0) do={',
                    '            :set addcmd ($addcmd . " limit-bytes-total=$limitbytes");',
                    '        };',
                    '        :if ([:len $email] > 0) do={',
                    '            :set addcmd ($addcmd . " email=\\"$email\\"");',
                    '        };',
                    '        :if ([:len $comment] > 0) do={',
                    '            :set addcmd ($addcmd . " comment=\\"$comment\\"");',
                    '        };',
                    '        :if ([:len $server] > 0) do={',
                    '            :set addcmd ($addcmd . " server=\\"$server\\"");',
                    '        };',
                    '        [:parse $addcmd];',
                    '        :set batchCount ($batchCount + 1);',
                    '        :set batchSuccess ($batchSuccess + 1);',
                    '        :set globalSuccess ($globalSuccess + 1);',
                    '    } on-error={',
                    '        :set batchErrors ($batchErrors + 1);',
                    '        :set globalErrors ($globalErrors + 1);',
                    '        :put "❌ خطأ في إضافة المستخدم: $username";',
                    '    };',
                    '}',
                    '',
                    f':put "✅ انتهت المرحلة $currentBatch: نجح $batchSuccess، فشل $batchErrors من $batchTotal";',
                ])

                # إضافة تأخير بين المراحل (ما عدا المرحلة الأخيرة)
                if batch_num < num_batches - 1:
                    script_lines.extend([
                        '',
                        ':put "⏳ انتظار 3 ثواني قبل المرحلة التالية...";',
                        '/delay 3s;',
                        ''
                    ])

                # محاكاة تأخير قصير في نافذة التقدم
                import time
                time.sleep(0.1)

            # إضافة الإحصائيات النهائية
            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من جميع المراحل!";',
                ':put "📈 الإحصائيات النهائية:";',
                ':put "   ✅ نجح: $globalSuccess مستخدم";',
                ':put "   ❌ فشل: $globalErrors مستخدم";',
                ':put "   📊 الإجمالي: $totalUsers مستخدم";',
                f':put "   📦 المراحل: $numBatches مرحلة";',
                ':put "🏁 انتهى التوليد المرحلي بنجاح!";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            self.logger.error(f"خطأ في توليد السكريبت المرحلي لـ Hotspot: {str(e)}")
            return None

    def generate_user_manager_fast_script(self):
        """توليد سكريبت سريع محسن لـ User Manager"""
        try:
            version = self.version_combo.get()
            if version != 'v6':
                messagebox.showinfo("معلومات", "الوضع السريع جداً متاح فقط لـ User Manager إصدار 6")
                return None

            customer = self.customer_entry.get().strip()
            profile = self.profile_combo.get().strip()

            if not customer or not profile:
                messagebox.showerror("خطأ", "يرجى تحديد العميل والبروفايل")
                return None

            # إنشاء سكريبت محسن بـ array
            script_lines = [':local usr {']
            for cred in self.generated_credentials:
                script_lines.append(f'    "{cred["username"]}"="{cred["password"]}" ;')

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة المستخدمين...";',
                ':local count 0;',
                ':local total [:len $usr];',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach u,p in=$usr do={'
            ])

            # إعداد المعاملات الإضافية
            caller_id_param = " caller-id-bind-on-first-use=yes" if self.caller_id_bind_var.get() else ""
            email_param = f' email="{self.user_email.get()}"' if self.user_email.get() else ""

            add_user_cmd = f'/tool user-manager user add username=$u password=$p customer="{customer}"{caller_id_param} first-name="{profile}"{email_param}'

            script_lines.extend([
                '    :do {',
                f'        {add_user_cmd};',
                '        :set count ($count + 1);',
                '        :if (($count % 50) = 0) do={ :put "✅ تم إضافة $count من $total مستخدم..."; };',
                '    } on-error={ :put "❌ خطأ في إضافة المستخدم: $u"; };',
                '}',
                '',
                ':put "✅ تم إضافة $count مستخدم بنجاح";',
                '',
                ':put "🔄 بدء تفعيل البروفايلات...";'
            ])

            # تفعيل البروفايلات بشكل جماعي محسن
            usernames = [cred["username"] for cred in self.generated_credentials]

            # تقسيم المستخدمين إلى مجموعات لتجنب مشاكل الحد الأقصى
            batch_size = 100
            for i in range(0, len(usernames), batch_size):
                batch = usernames[i:i + batch_size]
                numbers_str = ",".join(batch)
                activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers="{numbers_str}"'

                script_lines.extend([
                    ':do {',
                    f'    {activate_profile_cmd};',
                    f'    :put "✅ تم تفعيل البروفايل للمجموعة {i//batch_size + 1}";',
                    '} on-error={ :put "❌ خطأ في تفعيل البروفايل للمجموعة"; };'
                ])

            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";',
                f':put "📈 تم إضافة وتفعيل {len(self.generated_credentials)} مستخدم";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            self.logger.error(f"خطأ في توليد سكريبت User Manager: {str(e)}")
            return None

    def generate_hotspot_fast_script(self):
        """توليد سكريبت سريع محسن لـ Hotspot - الجيل الجديد"""
        try:
            profile = self.profile_combo.get().strip()
            if not profile:
                messagebox.showerror("خطأ", "يرجى تحديد البروفايل")
                return None

            server = self.server_entry.get().strip()

            # إنشاء سكريبت محسن بـ array للـ Hotspot
            script_lines = [':local users {']

            for cred in self.generated_credentials:
                # معالجة حد البيانات بدقة عالية
                limit_bytes = ""
                if hasattr(self, 'limit_bytes_entry') and self.limit_bytes_entry.get():
                    try:
                        limit_value = float(self.limit_bytes_entry.get())
                        limit_unit = self.limit_unit_combo.get() if hasattr(self, 'limit_unit_combo') else "GB"

                        if limit_unit == "GB":
                            limit_bytes = str(int(limit_value * 1024 * 1024 * 1024))
                        elif limit_unit == "MB":
                            limit_bytes = str(int(limit_value * 1024 * 1024))
                        elif limit_unit == "KB":
                            limit_bytes = str(int(limit_value * 1024))
                        else:
                            limit_bytes = str(int(limit_value))
                    except (ValueError, AttributeError):
                        limit_bytes = ""

                # معالجة الإيميل والأيام
                email = ""
                if hasattr(self, 'days_entry') and self.days_entry.get():
                    try:
                        days = self.days_entry.get()
                        email_template = self.email_template_entry.get() if hasattr(self, 'email_template_entry') else "@sa.sa"
                        email = f"{days}{email_template}"
                    except AttributeError:
                        email = ""

                # معالجة التعليق
                comment = self.comment_entry.get() if hasattr(self, 'comment_entry') else ""

                # إنشاء سطر المستخدم بتنسيق محسن
                user_data = [
                    cred["password"],
                    profile,
                    limit_bytes if limit_bytes else "",
                    email if email else "",
                    comment if comment else "",
                    server if server else ""
                ]

                user_line = f'    "{cred["username"]}"="' + ",".join(user_data) + '" ;'
                script_lines.append(user_line)

            if self.generated_credentials:
                script_lines[-1] = script_lines[-1].rstrip(' ;')

            script_lines.extend([
                '};',
                '',
                ':put "🚀 بدء الوضع السريع - إضافة مستخدمي Hotspot...";',
                ':local count 0;',
                ':local total [:len $users];',
                ':local success 0;',
                ':local errors 0;',
                ':put "📊 العدد الإجمالي: $total مستخدم";',
                '',
                ':foreach user,data in=$users do={',
                '    :local userdata [:toarray $data];',
                '    :local username $user;',
                '    :local password [:pick $userdata 0];',
                '    :local profile [:pick $userdata 1];',
                '    :local limitbytes [:pick $userdata 2];',
                '    :local email [:pick $userdata 3];',
                '    :local comment [:pick $userdata 4];',
                '    :local server [:pick $userdata 5];',
                '',
                '    :do {',
                '        # بناء الأمر بشكل ديناميكي',
                '        :local addcmd "/ip hotspot user add name=\\"$username\\" password=\\"$password\\" profile=\\"$profile\\"";',
                '        ',
                '        # إضافة المعاملات الاختيارية',
                '        :if ([:len $limitbytes] > 0) do={',
                '            :set addcmd ($addcmd . " limit-bytes-total=$limitbytes");',
                '        };',
                '        :if ([:len $email] > 0) do={',
                '            :set addcmd ($addcmd . " email=\\"$email\\"");',
                '        };',
                '        :if ([:len $comment] > 0) do={',
                '            :set addcmd ($addcmd . " comment=\\"$comment\\"");',
                '        };',
                '        :if ([:len $server] > 0) do={',
                '            :set addcmd ($addcmd . " server=\\"$server\\"");',
                '        };',
                '        ',
                '        # تنفيذ الأمر',
                '        [:parse $addcmd];',
                '        :set count ($count + 1);',
                '        :set success ($success + 1);',
                '        ',
                '        # عرض التقدم كل 50 مستخدم',
                '        :if (($count % 50) = 0) do={',
                '            :put "✅ تم إضافة $count من $total مستخدم...";',
                '        };',
                '        ',
                '    } on-error={',
                '        :set errors ($errors + 1);',
                '        :put "❌ خطأ في إضافة المستخدم: $username";',
                '    };',
                '}',
                '',
                ':put "🎉 تم الانتهاء من إضافة الحسابات!";',
                ':put "📈 النتائج النهائية:";',
                ':put "   ✅ نجح: $success مستخدم";',
                ':put "   ❌ فشل: $errors مستخدم";',
                ':put "   📊 الإجمالي: $total مستخدم";'
            ])

            # إضافة تشغيل سكربت قبل التنظيف إذا تم تفعيل الخيار
            if self.run_script_before_cleanup_var.get():
                script_to_run = self.script_to_run_entry.get().strip()
                if script_to_run:
                    script_lines.extend([
                        '',
                        ':put "⏳ انتظار 3 ثواني قبل تشغيل السكريبت المحدد...";',
                        '/delay 3s;',
                        '',
                        f':put "🔧 تشغيل السكريبت: {script_to_run}";',
                        f':do {{ /sys script run "{script_to_run}"; }} on-error={{ :put "❌ خطأ في تشغيل السكريبت: {script_to_run}"; }};',
                        ':put "✅ تم تشغيل السكريبت بنجاح!";'
                    ])

            script_lines.extend([
                '',
                ':put "🎉 تم الانتهاء من الوضع السريع بنجاح!";'
            ])

            return '\n'.join(script_lines)

        except Exception as e:
            self.logger.error(f"خطأ في توليد سكريبت Hotspot: {str(e)}")
            return None

    def generate_and_send_fast(self):
        """توليد وإرسال سريع محسن للأداء - يدعم جميع الأنظمة"""
        if not ROUTEROS_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة routeros_api غير مثبتة.")
            return
        if not self.generated_credentials:
            messagebox.showwarning("تحذير", "يرجى توليد الحسابات أولاً")
            return

        try:
            # توليد السكريبت حسب نوع النظام
            if self.system_type == 'user_manager':
                script_source = self.generate_user_manager_fast_script()
                if not script_source:
                    return
            elif self.system_type == 'hotspot':
                script_source = self.generate_hotspot_fast_script()
                if not script_source:
                    return
            else:
                messagebox.showerror("خطأ", "نوع النظام غير مدعوم")
                return

            # الاتصال بـ MikroTik
            api = self.connect_api()
            if not api:
                return

            # إنشاء اسم السكريبت
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            script_name = f"fast_mode_{self.system_type}_{timestamp}"

            # إنشاء نافذة التقدم
            progress_window = self.create_progress_window("الإرسال السريع", "جاري إرسال السكريبت...")

            try:
                # إضافة السكريبت
                progress_window.update_status("إضافة السكريبت إلى MikroTik...")
                api.get_resource('/system/script').add(name=script_name, source=script_source)

                # تشغيل السكريبت
                progress_window.update_status("تشغيل السكريبت...")
                api.get_resource('/system/script').call('run', {'number': script_name})

                # حذف السكريبت
                progress_window.update_status("تنظيف السكريبت...")
                api.get_resource('/system/script').remove(id=script_name)

                progress_window.destroy()
                messagebox.showinfo("نجاح", f"تم إرسال {len(self.generated_credentials)} حساب إلى MikroTik بنجاح")
                self.logger.info(f"تم إرسال {len(self.generated_credentials)} حساب بالوضع السريع")

            except Exception as e:
                progress_window.destroy()
                self.logger.error(f"خطأ في الإرسال السريع: {str(e)}")

                # محاولة حذف السكريبت في حالة الخطأ
                try:
                    api.get_resource('/system/script').remove(id=script_name)
                except:
                    pass

                if "RouterOsApiConnectionError" in str(type(e)):
                    messagebox.showwarning("تحذير", "انقطع الاتصال بالسيرفر")
                else:
                    messagebox.showerror("خطأ", f"فشل الإرسال: {str(e)}")

        except Exception as e:
            self.logger.error(f"خطأ في العملية السريعة: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في العملية: {str(e)}")

    def create_progress_window(self, title, initial_message):
        """إنشاء نافذة تقدم محسنة"""
        try:
            progress_window = tk.Toplevel(self.root)
            progress_window.title(title)
            progress_window.geometry("400x150")
            progress_window.transient(self.root)
            progress_window.grab_set()
            progress_window.resizable(False, False)

            # إعداد النافذة في المنتصف
            progress_window.update_idletasks()
            x = (progress_window.winfo_screenwidth() // 2) - (400 // 2)
            y = (progress_window.winfo_screenheight() // 2) - (150 // 2)
            progress_window.geometry(f"400x150+{x}+{y}")

            # إطار المحتوى
            content_frame = ttk.Frame(progress_window, padding=20)
            content_frame.pack(fill=tk.BOTH, expand=True)

            # رسالة الحالة
            status_label = ttk.Label(content_frame, text=initial_message,
                                   font=self.fonts['arabic'], justify=tk.CENTER)
            status_label.pack(pady=10)

            # شريط التقدم
            progress_bar = ttk.Progressbar(content_frame, mode='indeterminate')
            progress_bar.pack(pady=10, fill=tk.X)
            progress_bar.start()

            # دالة تحديث الحالة
            def update_status(message):
                status_label.configure(text=message)
                progress_window.update()

            progress_window.update_status = update_status
            progress_window.update()

            return progress_window

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء نافذة التقدم: {str(e)}")
            return None

    def update_preview_event(self, event=None):
        self.update_preview()

    def start_drag(self, event):
        closest = self.preview_canvas.find_closest(event.x, event.y)
        if closest:
            tags = self.preview_canvas.gettags(closest[0])
            for tag in tags:
                if tag in ['username', 'password', 'serial', 'date', 'qr', 'price']:
                    self.dragging_element = tag
                    self.element_positions[tag] = (event.x, event.y)
                    break

    def on_drag(self, event):
        if self.dragging_element:
            dx = event.x - self.element_positions[self.dragging_element][0]
            dy = event.y - self.element_positions[self.dragging_element][1]

            mm_to_px = 3.77953
            scale = min(300 / (self.box_width_mm * mm_to_px), 150 / (self.box_height_mm * mm_to_px))
            dx_mm = dx / (mm_to_px * scale)
            dy_mm = dy / (mm_to_px * scale)

            if self.dragging_element == 'username':
                current_x = float(self.username_x_entry.get() or 0)
                current_y = float(self.username_y_entry.get() or 0)
                self.username_x_entry.delete(0, tk.END)
                self.username_y_entry.delete(0, tk.END)
                self.username_x_entry.insert(0, str(round(current_x + dx_mm, 2)))
                self.username_y_entry.insert(0, str(round(current_y - dy_mm, 2)))
            elif self.dragging_element == 'password':
                current_x = float(self.password_x_entry.get() or 0)
                current_y = float(self.password_y_entry.get() or 0)
                self.password_x_entry.delete(0, tk.END)
                self.password_y_entry.delete(0, tk.END)
                self.password_x_entry.insert(0, str(round(current_x + dx_mm, 2)))
                self.password_y_entry.insert(0, str(round(current_y - dy_mm, 2)))
            elif self.dragging_element == 'serial':
                current_x = float(self.serial_x_entry.get() or 0)
                current_y = float(self.serial_y_entry.get() or 0)
                self.serial_x_entry.delete(0, tk.END)
                self.serial_y_entry.delete(0, tk.END)
                self.serial_x_entry.insert(0, str(round(current_x + dx_mm, 2)))
                self.serial_y_entry.insert(0, str(round(current_y - dy_mm, 2)))
            elif self.dragging_element == 'date':
                current_x = float(self.date_x_entry.get() or 0)
                current_y = float(self.date_y_entry.get() or 0)
                self.date_x_entry.delete(0, tk.END)
                self.date_y_entry.delete(0, tk.END)
                self.date_x_entry.insert(0, str(round(current_x + dx_mm, 2)))
                self.date_y_entry.insert(0, str(round(current_y - dy_mm, 2)))
            elif self.dragging_element == 'qr':
                current_x = float(self.qr_x_entry.get() or 0)
                current_y = float(self.qr_y_entry.get() or 0)
                self.qr_x_entry.delete(0, tk.END)
                self.qr_y_entry.delete(0, tk.END)
                self.qr_x_entry.insert(0, str(round(current_x + dx_mm, 2)))
                self.qr_y_entry.insert(0, str(round(current_y - dy_mm, 2)))
            elif self.dragging_element == 'price':
                current_x = float(self.price_x_entry.get() or 0)
                current_y = float(self.price_y_entry.get() or 0)
                self.price_x_entry.delete(0, tk.END)
                self.price_y_entry.delete(0, tk.END)
                self.price_x_entry.insert(0, str(round(current_x + dx_mm, 2)))
                self.price_y_entry.insert(0, str(round(current_y - dy_mm, 2)))

            self.update_preview()
            self.element_positions[self.dragging_element] = (event.x, event.y)

    def stop_drag(self, event):
        self.dragging_element = None
        self.save_settings()

    def update_preview(self):
        self.preview_canvas.delete("all")

        card = {"username": "user123", "password": "pass123", "profile": "default", "comment": "",
                "location": str(self.last_serial + 1) if self.use_serial_var.get() else "1",
                "email": self.user_email.get() if self.system_type == 'user_manager' else "",
                "limit_bytes": self.limit_bytes_entry.get() if self.system_type == 'hotspot' else "",
                "limit_unit": self.limit_unit_combo.get() if self.system_type == 'hotspot' else "",
                "days": self.days_entry.get() if self.system_type == 'hotspot' else "",
                "email_template": self.email_template_entry.get() if self.system_type == 'hotspot' else "",
                "price": self.price_entry.get() if hasattr(self, 'price_entry') else ""} if not self.generated_credentials else self.generated_credentials[0]

        columns = int(self.columns_entry.get() or 4)
        rows = int(self.rows_entry.get() or 18)
        spacing = float(self.spacing_entry.get() or 2)

        page_width = 210
        page_height = 297
        margin_x = 1
        margin_y = 5

        safe_columns = max(1, columns)
        safe_rows = max(1, rows)

        available_width = page_width - 2 * margin_x
        available_height = page_height - 2 * margin_y
        total_horizontal_spacing = spacing * (safe_columns - 1)
        total_vertical_spacing = spacing * (safe_rows - 1)

        self.box_width_mm = (available_width - total_horizontal_spacing) / safe_columns
        self.box_height_mm = (available_height - total_vertical_spacing) / safe_rows

        mm_to_px = 3.77953
        scale = min(300 / (self.box_width_mm * mm_to_px), 150 / (self.box_height_mm * mm_to_px))
        box_width_px = self.box_width_mm * mm_to_px * scale
        box_height_px = self.box_height_mm * mm_to_px * scale

        self.preview_canvas.create_rectangle(0, 0, box_width_px, box_height_px, outline="black", tags="card")

        if self.background_image:
            resized_image = self.background_image.resize((int(box_width_px), int(box_height_px)), Image.LANCZOS)
            self.preview_photo = ImageTk.PhotoImage(resized_image)
            self.preview_canvas.create_image(0, 0, image=self.preview_photo, anchor="nw", tags="background")

        def add_text(text, x_mm, y_mm, size_pt, color, bold, tag):
            x_px = x_mm * mm_to_px * scale
            y_px = box_height_px - (y_mm * mm_to_px * scale)
            font_size = int(size_pt * scale * 1.333)
            font = ("Arial", font_size, "bold" if bold else "normal")
            item = self.preview_canvas.create_text(x_px, y_px, text=text, font=font, fill=color, anchor="nw", tags=tag)
            self.canvas_elements[tag] = item

        def add_image(image, x_mm, y_mm, size_mm, tag):
            x_px = x_mm * mm_to_px * scale
            y_px = box_height_px - ((y_mm + size_mm) * mm_to_px * scale)
            img = image.resize((int(size_mm * mm_to_px * scale), int(size_mm * mm_to_px * scale)), Image.LANCZOS)
            photo = ImageTk.PhotoImage(img)
            item = self.preview_canvas.create_image(x_px, y_px, image=photo, anchor="nw", tags=tag)
            self.canvas_elements[tag] = item
            self.canvas_elements[f"{tag}_photo"] = photo

        if self.print_username_var.get():
            size = float(self.username_size_entry.get() or 8)
            x = float(self.username_x_entry.get() or 30)
            y = float(self.username_y_entry.get() or 5)
            color = self.username_color_entry.get() or "#000000"
            bold = self.username_bold_var.get()
            add_text(card["username"], x, y, size, color, bold, "username")

        if self.print_password_var.get() and card["password"]:
            size = float(self.password_size_entry.get() or 8)
            x = float(self.password_x_entry.get() or 30)
            y = float(self.password_y_entry.get() or 10)
            color = self.password_color_entry.get() or "#000000"
            bold = self.password_bold_var.get()
            add_text(card["password"], x, y, size, color, bold, "password")

        if self.use_serial_var.get():
            size = float(self.serial_size_entry.get() or 6)
            x = float(self.serial_x_entry.get() or 5)
            y = float(self.serial_y_entry.get() or 5)
            color = self.serial_color_entry.get() or "#000000"
            bold = self.serial_bold_var.get()
            serial = card["location"] if card["location"].isdigit() else str(self.last_serial + 1)
            add_text(serial, x, y, size, color, bold, "serial")

        if self.use_date_var.get():
            size = float(self.date_size_entry.get() or 8)
            x = float(self.date_x_entry.get() or 48)
            y = float(self.date_y_entry.get() or 12)
            color = self.date_color_entry.get() or "#000000"
            bold = self.date_bold_var.get()
            today = datetime.now().strftime("%Y-%m-%d")
            add_text(today, x, y, size, color, bold, "date")

        if self.use_qr_var.get():
            qr_size_mm = float(self.qr_size_entry.get() or 10)
            x_mm = float(self.qr_x_entry.get() or 5)
            y_mm = float(self.qr_y_entry.get() or 5)
            qr_data = card['username']
            qr = qrcode.QRCode(version=1, box_size=10, border=1)
            qr.add_data(qr_data)
            qr.make(fit=True)
            qr_img = qr.make_image(fill_color="black", back_color="white")
            add_image(qr_img, x_mm, y_mm, qr_size_mm, "qr")

        if self.use_price_var.get():
            size = float(self.price_size_entry.get() or 8)
            x = float(self.price_x_entry.get() or 5)
            y = float(self.price_y_entry.get() or 15)
            color = self.price_color_entry.get() or "#000000"
            bold = self.price_bold_var.get()
            price = card.get("price", self.price_value_entry.get())
            add_text(price, x, y, size, color, bold, "price")

    def save_as_mikrotik(self):
        script = self.output_text.get(1.0, tk.END).strip()
        if not script:
            messagebox.showwarning("تحذير", "السكريبت فارغ")
            return

        date = datetime.now().strftime("%Y%m%d")
        prefix = self.prefix_entry.get().strip()
        system_prefix = "usermanager" if self.system_type == 'user_manager' else "hotspot"
        default_filename = f"{system_prefix}_script_{prefix}{len(self.generated_credentials)}_{date}.txt"

        filename = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Text files", "*.txt")], initialfile=default_filename)
        if filename:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(script)
            messagebox.showinfo("نجاح", f"تم حفظ الملف: {filename}")
        self.update_preview()

    def send_cards_as_script(self):
        """إرسال الكروت المولدة كسكربت إلى MikroTik مع جدولة تلقائية ودعم التقسيم المرحلي"""
        try:
            # التحقق من وجود حسابات مولدة
            if not self.generated_credentials:
                messagebox.showwarning("تحذير", "يرجى توليد الحسابات أولاً")
                return

            # التحقق من توفر مكتبة routeros_api
            if not ROUTEROS_AVAILABLE:
                messagebox.showerror("خطأ", "مكتبة routeros_api غير مثبتة.")
                return

            total_accounts = len(self.generated_credentials)

            # تحديد نوع المعالجة حسب عدد الحسابات
            if total_accounts <= 100:
                # استخدام الطريقة العادية للحسابات القليلة
                self.send_cards_as_single_script()
            else:
                # استخدام التقسيم المرحلي للحسابات الكبيرة
                self.send_cards_as_staged_scripts()

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"خطأ في إرسال الكروت كسكربت: {error_msg}")
            messagebox.showerror("خطأ", f"فشل في إرسال الكروت كسكربت: {error_msg}")

    def send_cards_as_single_script(self):
        """إرسال الكروت كسكربت واحد (للحسابات 100 أو أقل)"""
        try:
            total_accounts = len(self.generated_credentials)

            # نافذة تأكيد
            confirm_result = messagebox.askyesno(
                "تأكيد إرسال الكروت كسكربت",
                f"هل تريد إرسال {total_accounts} حساب كسكربت إلى MikroTik؟\n\n"
                "سيتم:\n"
                "1. 📝 إنشاء سكربت واحد في قائمة Scripts\n"
                "2. ⏰ إضافة مهمة في Scheduler لتشغيل السكربت بعد 5 ثواني\n"
                "3. ✅ تطبيق جميع الحسابات المولدة تلقائياً\n"
                "4. 🧹 حذف السكربت والمهمة تلقائياً بعد التنفيذ\n\n"
                "💡 هذا يضمن عدم تراكم السكربتات والمهام في النظام!\n\n"
                "هل تريد المتابعة؟"
            )

            if not confirm_result:
                return

            # الاتصال بـ MikroTik
            api = self.connect_api()
            if not api:
                return

            self.logger.info("بدء إرسال الكروت كسكربت واحد إلى MikroTik")

            # إنشاء نافذة التقدم
            progress_window = tk.Toplevel(self.root)
            progress_window.title("إرسال الكروت كسكربت")
            progress_window.geometry("500x200")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # إعداد نافذة التقدم
            ttk.Label(progress_window, text="جاري إرسال الكروت كسكربت إلى MikroTik...",
                     font=self.fonts['arabic']).pack(pady=20)

            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=tk.X)
            progress_bar.start()

            status_label = ttk.Label(progress_window, text="إنشاء السكربت...",
                                   font=self.fonts['arabic'])
            status_label.pack(pady=5)

            # تحديث الواجهة
            self.root.update()

            # تحديد الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            # إنشاء أسماء فريدة للسكربت والمهمة
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            script_name = f"hotspot_cards_{timestamp}"
            task_name = f"run_{script_name}"

            # إنشاء السكربت مع آلية الحذف التلقائي
            script_content = self.create_script_content_for_cards(version_value, status_label, script_name, task_name)

            # إضافة السكربت إلى MikroTik
            self.add_script_to_mikrotik(api, script_name, script_content, version_value, status_label)

            # إضافة مهمة الجدولة
            self.schedule_script_execution(api, script_name, task_name, version_value, status_label)

            # إغلاق نافذة التقدم
            progress_window.destroy()

            # رسالة النجاح
            messagebox.showinfo(
                "نجح الإرسال",
                f"تم إرسال الكروت كسكربت بنجاح!\n\n"
                f"📋 اسم السكربت: {script_name}\n"
                f"📊 عدد الحسابات: {total_accounts}\n"
                f"⏰ سيتم تشغيل السكربت تلقائياً بعد 5 ثواني\n"
                f"🧹 سيتم حذف السكربت والمهمة تلقائياً بعد التنفيذ\n\n"
                f"💡 يمكنك مراجعة السكربت في قائمة System > Scripts\n"
                f"📅 ومراجعة المهمة في قائمة System > Scheduler"
            )

            self.logger.info(f"تم إرسال {total_accounts} حساب كسكربت واحد بنجاح: {script_name}")

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()

            error_msg = str(e)
            self.logger.error(f"خطأ في إرسال الكروت كسكربت واحد: {error_msg}")
            messagebox.showerror("خطأ", f"فشل في إرسال الكروت كسكربت: {error_msg}")

    def send_cards_as_staged_scripts(self):
        """إرسال الكروت كسكربتات مرحلية (للحسابات أكثر من 100)"""
        try:
            total_accounts = len(self.generated_credentials)
            scripts_count = (total_accounts + 99) // 100  # حساب عدد السكربتات المطلوبة

            # نافذة تأكيد مع معلومات التقسيم المرحلي
            confirm_result = messagebox.askyesno(
                "تأكيد إرسال الكروت كسكربتات مرحلية",
                f"هل تريد إرسال {total_accounts} حساب كسكربتات مرحلية إلى MikroTik؟\n\n"
                f"🔄 سيتم التقسيم المرحلي:\n"
                f"   📊 عدد الحسابات: {total_accounts}\n"
                f"   📝 عدد السكربتات: {scripts_count}\n"
                f"   ⏱️ تأخير بين السكربتات: 5 ثواني\n\n"
                "سيتم:\n"
                "1. 📝 إنشاء عدة سكربتات (100 حساب لكل سكربت)\n"
                "2. ⏰ جدولة تشغيل متسلسل مع تأخير 5 ثواني\n"
                "3. ✅ تطبيق جميع الحسابات تلقائياً\n"
                "4. 🧹 حذف جميع السكربتات والمهام تلقائياً\n\n"
                "💡 هذا يضمن الأداء الأمثل وعدم تراكم الملفات!\n\n"
                "هل تريد المتابعة؟"
            )

            if not confirm_result:
                return

            # الاتصال بـ MikroTik
            api = self.connect_api()
            if not api:
                return

            self.logger.info(f"بدء إرسال الكروت كسكربتات مرحلية: {scripts_count} سكربت لـ {total_accounts} حساب")

            # إنشاء نافذة التقدم المحسنة
            progress_window = tk.Toplevel(self.root)
            progress_window.title("إرسال الكروت كسكربتات مرحلية")
            progress_window.geometry("600x300")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # إعداد نافذة التقدم
            ttk.Label(progress_window, text="جاري إرسال الكروت كسكربتات مرحلية إلى MikroTik...",
                     font=self.fonts['arabic_heading']).pack(pady=15)

            # معلومات التقسيم
            info_frame = ttk.Frame(progress_window)
            info_frame.pack(pady=10)

            ttk.Label(info_frame, text=f"📊 إجمالي الحسابات: {total_accounts}",
                     font=self.fonts['arabic']).pack()
            ttk.Label(info_frame, text=f"📝 عدد السكربتات: {scripts_count}",
                     font=self.fonts['arabic']).pack()
            ttk.Label(info_frame, text=f"⏱️ تأخير بين السكربتات: 5 ثواني",
                     font=self.fonts['arabic']).pack()

            # شريط التقدم
            progress_bar = ttk.Progressbar(progress_window, mode='determinate', maximum=scripts_count)
            progress_bar.pack(pady=15, padx=20, fill=tk.X)

            # تسمية الحالة
            status_label = ttk.Label(progress_window, text="بدء إنشاء السكربتات...",
                                   font=self.fonts['arabic'])
            status_label.pack(pady=5)

            # تسمية السكربت الحالي
            current_script_label = ttk.Label(progress_window, text="",
                                           font=self.fonts['arabic'])
            current_script_label.pack(pady=5)

            # تحديث الواجهة
            self.root.update()

            # تحديد الإصدار
            version = getattr(self, 'version_combo', None)
            if version and hasattr(version, 'get'):
                version_value = version.get()
            else:
                version_value = 'v7'

            # إنشاء السكربتات والمهام المرحلية
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            script_names = []
            task_names = []

            # تقسيم الحسابات إلى مجموعات
            for i in range(scripts_count):
                start_idx = i * 100
                end_idx = min((i + 1) * 100, total_accounts)
                accounts_batch = self.generated_credentials[start_idx:end_idx]

                # تحديث حالة التقدم
                progress_bar['value'] = i
                status_label.configure(text=f"إنشاء السكربت {i+1} من {scripts_count}...")
                current_script_label.configure(text=f"معالجة الحسابات {start_idx+1}-{end_idx} من {total_accounts}")
                self.root.update()

                # إنشاء أسماء فريدة للسكربت والمهمة
                script_name = f"hotspot_cards_{timestamp}_{i+1}"
                task_name = f"run_{script_name}"

                script_names.append(script_name)
                task_names.append(task_name)

                # إنشاء السكربت للمجموعة الحالية
                script_content = self.create_staged_script_content(
                    accounts_batch, version_value, script_name, task_name,
                    i+1, scripts_count, script_names, task_names
                )

                # إضافة السكربت إلى MikroTik
                self.add_script_to_mikrotik(api, script_name, script_content, version_value, status_label)

                self.logger.info(f"تم إنشاء السكربت {i+1}/{scripts_count}: {script_name} ({len(accounts_batch)} حساب)")

            # إنشاء الجدولة المتسلسلة
            status_label.configure(text="إنشاء الجدولة المتسلسلة...")
            current_script_label.configure(text="جدولة تشغيل جميع السكربتات...")
            self.root.update()

            self.create_staged_scheduling(api, script_names, task_names, version_value)

            # إكمال شريط التقدم
            progress_bar['value'] = scripts_count
            status_label.configure(text="تم الانتهاء بنجاح!")
            current_script_label.configure(text=f"تم إنشاء {scripts_count} سكربت وجدولة التشغيل")
            self.root.update()

            # انتظار قصير قبل إغلاق النافذة
            import time
            time.sleep(2)

            # إغلاق نافذة التقدم
            progress_window.destroy()

            # رسالة النجاح المفصلة
            messagebox.showinfo(
                "نجح الإرسال المرحلي",
                f"تم إرسال الكروت كسكربتات مرحلية بنجاح!\n\n"
                f"📊 إجمالي الحسابات: {total_accounts}\n"
                f"📝 عدد السكربتات: {scripts_count}\n"
                f"⏰ بدء التشغيل: فوراً\n"
                f"⏱️ تأخير بين السكربتات: 5 ثواني\n"
                f"🧹 حذف تلقائي: نعم\n\n"
                f"💡 يمكنك مراجعة السكربتات في قائمة System > Scripts\n"
                f"📅 ومراجعة المهام في قائمة System > Scheduler\n\n"
                f"🎯 سيتم تشغيل السكربتات تلقائياً وحذفها بعد الانتهاء!"
            )

            self.logger.info(f"تم إرسال {total_accounts} حساب كسكربتات مرحلية بنجاح: {scripts_count} سكربت")

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()

            error_msg = str(e)
            self.logger.error(f"خطأ في إرسال الكروت كسكربتات مرحلية: {error_msg}")
            messagebox.showerror("خطأ", f"فشل في إرسال الكروت كسكربتات مرحلية: {error_msg}")

    def create_staged_script_content(self, accounts_batch, version_value, script_name, task_name,
                                   current_stage, total_stages, all_script_names, all_task_names):
        """إنشاء محتوى السكربت المرحلي"""
        try:
            import datetime

            script_lines = []
            script_lines.append("# سكربت إضافة حسابات Hotspot مرحلي مع حذف تلقائي")
            script_lines.append(f"# تم إنشاؤه تلقائياً في {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            script_lines.append(f"# المرحلة: {current_stage} من {total_stages}")
            script_lines.append(f"# عدد الحسابات في هذه المرحلة: {len(accounts_batch)}")
            script_lines.append(f"# اسم السكربت: {script_name}")
            script_lines.append(f"# اسم المهمة: {task_name}")
            script_lines.append("")

            # إضافة رسائل التقدم المرحلية
            script_lines.append(f':put "🚀 بدء المرحلة {current_stage} من {total_stages}...";')
            script_lines.append(f':put "📊 حسابات هذه المرحلة: {len(accounts_batch)}";')
            script_lines.append(':local success 0;')
            script_lines.append(':local errors 0;')
            script_lines.append("")

            # الحصول على الإعدادات من الواجهة
            profile = self.profile_combo.get() if hasattr(self, 'profile_combo') else ""
            server = self.server_entry.get() if hasattr(self, 'server_entry') else "all"
            comment = self.comment_entry.get() if hasattr(self, 'comment_entry') else ""

            # إضافة معلومات الإعدادات في السكربت
            script_lines.append(f':put "⚙️ الإعدادات المطبقة للمرحلة {current_stage}:";')
            if profile:
                script_lines.append(f':put "   📋 البروفايل: {profile}";')
            if server and server != "all":
                script_lines.append(f':put "   🖥️ الخادم: {server}";')
            if comment:
                script_lines.append(f':put "   💬 التعليق: {comment}";')

            # فحص وجود إعدادات الحد والإيميل في هذه المجموعة
            has_limit = any(cred.get("limit_bytes") and cred.get("limit_unit") for cred in accounts_batch)
            has_email = any(cred.get("days") and cred.get("email_template") for cred in accounts_batch)

            if has_limit:
                script_lines.append(f':put "   📊 حد البيانات: مفعل";')
            if has_email:
                script_lines.append(f':put "   📧 الإيميل: مفعل";')

            script_lines.append(':put "";')

            # إضافة الحسابات مع معالجة الأخطاء وجميع الخصائص
            for i, cred in enumerate(accounts_batch):
                username = cred['username']
                password = cred['password']

                # بناء قائمة أجزاء الأمر
                if version_value == "v6":
                    script_parts = [
                        f'/ip hotspot user add name="{username}"',
                        f'password="{password}"'
                    ]
                else:
                    script_parts = [
                        f'/ip/hotspot/user/add name="{username}"',
                        f'password="{password}"'
                    ]

                # إضافة البروفايل
                if profile:
                    script_parts.append(f'profile="{profile}"')

                # إضافة الخادم
                if server and server != "all":
                    script_parts.append(f'server="{server}"')

                # إضافة التعليق
                if comment:
                    script_parts.append(f'comment="{comment}"')

                # إضافة حد البيانات (الجيجات)
                if cred.get("limit_bytes") and cred.get("limit_unit"):
                    try:
                        limit_bytes = float(cred["limit_bytes"])
                        if cred["limit_unit"] == "GB":
                            bytes_limit = str(int(limit_bytes * 1073741824))  # GB to bytes
                        else:  # MB
                            bytes_limit = str(int(limit_bytes * 1048576))    # MB to bytes
                        script_parts.append(f'limit-bytes-total="{bytes_limit}"')
                    except (ValueError, TypeError):
                        pass  # تجاهل إذا كانت القيمة غير صحيحة

                # إضافة الإيميل (مكون من الأيام + قالب الإيميل)
                if cred.get("days") and cred.get("email_template"):
                    email = f'{cred["days"]}{cred["email_template"]}'
                    script_parts.append(f'email="{email}"')

                # بناء الأمر النهائي
                cmd = ' '.join(script_parts)

                # إضافة الأمر مع معالجة الأخطاء ورسائل مفصلة
                script_lines.append(':do {')
                script_lines.append(f'    {cmd};')
                script_lines.append('    :set success ($success + 1);')

                # رسائل تقدم مفصلة (كل 25 حساب في المرحلة)
                script_lines.append(f'    :if (($success % 25) = 0) do={{')
                script_lines.append(f'        :put "✅ المرحلة {current_stage}: تم إضافة $success من {len(accounts_batch)} حساب...";')
                script_lines.append(f'        :put "   👤 آخر حساب: {username}";')
                script_lines.append('    };')
                script_lines.append('} on-error={')
                script_lines.append('    :set errors ($errors + 1);')
                script_lines.append(f'    :put "❌ خطأ في إضافة المستخدم: {username}";')
                script_lines.append('};')
                script_lines.append("")

            # إضافة تشغيل سكربت قبل التنظيف إذا تم تفعيل الخيار (فقط في المرحلة الأخيرة)
            if current_stage == total_stages and self.run_script_before_cleanup_var.get():
                script_to_run = self.script_to_run_entry.get().strip()
                if script_to_run:
                    script_lines.extend([
                        ':put "⏳ انتظار 3 ثواني قبل تشغيل السكريبت المحدد...";',
                        '/delay 3s;',
                        '',
                        f':put "🔧 تشغيل السكريبت: {script_to_run}";',
                        f':do {{ /sys script run "{script_to_run}"; }} on-error={{ :put "❌ خطأ في تشغيل السكريبت: {script_to_run}"; }};',
                        ':put "✅ تم تشغيل السكريبت بنجاح!";',
                        ''
                    ])

            # إضافة النتائج النهائية للمرحلة
            script_lines.extend([
                f':put "🎉 تم الانتهاء من المرحلة {current_stage} من {total_stages}!";',
                ':put "📈 نتائج هذه المرحلة:";',
                ':put "   ✅ نجح: $success حساب";',
                ':put "   ❌ فشل: $errors حساب";',
                f':put "   📊 إجمالي المرحلة: {len(accounts_batch)} حساب";',
                ''
            ])

            # إضافة أوامر الحذف التلقائي (فقط في المرحلة الأخيرة)
            if current_stage == total_stages:
                script_lines.extend([
                    ':put "🧹 بدء عملية التنظيف التلقائي الشامل...";',
                    ''
                ])

                # حذف جميع السكربتات
                for i, script_name_to_delete in enumerate(all_script_names):
                    script_lines.extend([
                        ':do {',
                        f'    /system script remove [find name="{script_name_to_delete}"];',
                        f'    :put "✅ تم حذف السكربت {i+1}: {script_name_to_delete}";',
                        '} on-error={',
                        f'    :put "⚠️ تعذر حذف السكربت: {script_name_to_delete}";',
                        '};'
                    ])

                script_lines.append('')

                # حذف جميع المهام
                for i, task_name_to_delete in enumerate(all_task_names):
                    script_lines.extend([
                        ':do {',
                        f'    /system scheduler remove [find name="{task_name_to_delete}"];',
                        f'    :put "✅ تم حذف المهمة {i+1}: {task_name_to_delete}";',
                        '} on-error={',
                        f'    :put "⚠️ تعذر حذف المهمة: {task_name_to_delete}";',
                        '};'
                    ])

                script_lines.extend([
                    '',
                    ':put "🎯 تم الانتهاء من التنظيف التلقائي الشامل بنجاح!";',
                    f':put "📋 تمت إضافة جميع الحسابات وتنظيف {total_stages} سكربت و {total_stages} مهمة تلقائياً";'
                ])
            else:
                # في المراحل الوسطية، فقط رسالة إكمال
                script_lines.extend([
                    f':put "➡️ المرحلة {current_stage} مكتملة، انتظار المرحلة التالية...";'
                ])

            script_content = "\n".join(script_lines)
            self.logger.info(f"تم إنشاء سكربت المرحلة {current_stage} يحتوي على {len(accounts_batch)} حساب")

            return script_content

        except Exception as e:
            error_msg = f"خطأ في إنشاء محتوى السكربت المرحلي: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def create_staged_scheduling(self, api, script_names, task_names, version_value):
        """إنشاء الجدولة المتسلسلة للسكربتات المرحلية"""
        try:
            # تحديد المسار حسب الإصدار
            if version_value == "v6":
                scheduler_path = '/system/scheduler'
            else:
                scheduler_path = '/system/scheduler'

            scheduler_resource = api.get_resource(scheduler_path)

            # حساب أوقات التشغيل المتسلسلة
            import datetime
            base_time = datetime.datetime.now() + datetime.timedelta(seconds=5)  # بدء بعد 5 ثواني

            for i, (script_name, task_name) in enumerate(zip(script_names, task_names)):
                # حساب وقت التشغيل لكل سكربت (تأخير 5 ثواني بين كل سكربت)
                execution_time = base_time + datetime.timedelta(seconds=i * 5)
                start_time = execution_time.strftime("%H:%M:%S")
                start_date = execution_time.strftime("%b/%d/%Y")

                # إضافة مهمة الجدولة
                scheduler_resource.add(
                    name=task_name,
                    start_time=start_time,
                    start_date=start_date,
                    interval="00:00:00",  # تشغيل مرة واحدة فقط
                    on_event=f'/system script run "{script_name}"',
                    comment=f"تشغيل سكربت مرحلي {i+1} - {script_name} مع حذف تلقائي"
                )

                self.logger.info(f"تم جدولة السكربت {i+1}: {task_name} في {start_time} ({start_date})")

            self.logger.info(f"تم إنشاء الجدولة المتسلسلة لـ {len(script_names)} سكربت مرحلي")

        except Exception as e:
            error_msg = f"خطأ في إنشاء الجدولة المتسلسلة: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def create_script_content_for_cards(self, version_value, status_label, script_name, task_name):
        """إنشاء محتوى السكربت للكروت المولدة مع آلية الحذف التلقائي"""
        try:
            import datetime

            status_label.configure(text="إنشاء محتوى السكربت...")
            self.root.update()

            script_lines = []
            script_lines.append("# سكربت إضافة حسابات Hotspot مع حذف تلقائي")
            script_lines.append(f"# تم إنشاؤه تلقائياً في {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            script_lines.append(f"# عدد الحسابات: {len(self.generated_credentials)}")
            script_lines.append(f"# اسم السكربت: {script_name}")
            script_lines.append(f"# اسم المهمة: {task_name}")
            script_lines.append("")

            # إضافة رسائل التقدم
            script_lines.append(':put "🚀 بدء إضافة حسابات Hotspot...";')
            script_lines.append(f':put "📊 العدد الإجمالي: {len(self.generated_credentials)} حساب";')
            script_lines.append(':local success 0;')
            script_lines.append(':local errors 0;')
            script_lines.append("")

            # الحصول على الإعدادات من الواجهة
            profile = self.profile_combo.get() if hasattr(self, 'profile_combo') else ""
            server = self.server_entry.get() if hasattr(self, 'server_entry') else "all"
            comment = self.comment_entry.get() if hasattr(self, 'comment_entry') else ""

            # إضافة معلومات الإعدادات في السكربت
            script_lines.append(f':put "⚙️ الإعدادات المطبقة:";')
            if profile:
                script_lines.append(f':put "   📋 البروفايل: {profile}";')
            if server and server != "all":
                script_lines.append(f':put "   🖥️ الخادم: {server}";')
            if comment:
                script_lines.append(f':put "   💬 التعليق: {comment}";')

            # فحص وجود إعدادات الحد والإيميل
            has_limit = any(cred.get("limit_bytes") and cred.get("limit_unit") for cred in self.generated_credentials)
            has_email = any(cred.get("days") and cred.get("email_template") for cred in self.generated_credentials)

            if has_limit:
                script_lines.append(f':put "   📊 حد البيانات: مفعل";')
            if has_email:
                script_lines.append(f':put "   📧 الإيميل: مفعل";')

            script_lines.append(':put "";')

            # إضافة الحسابات مع معالجة الأخطاء وجميع الخصائص
            for i, cred in enumerate(self.generated_credentials):
                username = cred['username']
                password = cred['password']

                # بناء قائمة أجزاء الأمر
                if version_value == "v6":
                    script_parts = [
                        f'/ip hotspot user add name="{username}"',
                        f'password="{password}"'
                    ]
                else:
                    script_parts = [
                        f'/ip/hotspot/user/add name="{username}"',
                        f'password="{password}"'
                    ]

                # إضافة البروفايل
                if profile:
                    script_parts.append(f'profile="{profile}"')

                # إضافة الخادم
                if server and server != "all":
                    script_parts.append(f'server="{server}"')

                # إضافة التعليق
                if comment:
                    script_parts.append(f'comment="{comment}"')

                # إضافة حد البيانات (الجيجات)
                if cred.get("limit_bytes") and cred.get("limit_unit"):
                    try:
                        limit_bytes = float(cred["limit_bytes"])
                        if cred["limit_unit"] == "GB":
                            bytes_limit = str(int(limit_bytes * 1073741824))  # GB to bytes
                        else:  # MB
                            bytes_limit = str(int(limit_bytes * 1048576))    # MB to bytes
                        script_parts.append(f'limit-bytes-total="{bytes_limit}"')
                    except (ValueError, TypeError):
                        pass  # تجاهل إذا كانت القيمة غير صحيحة

                # إضافة الإيميل (مكون من الأيام + قالب الإيميل)
                if cred.get("days") and cred.get("email_template"):
                    email = f'{cred["days"]}{cred["email_template"]}'
                    script_parts.append(f'email="{email}"')

                # بناء الأمر النهائي
                cmd = ' '.join(script_parts)

                # إضافة الأمر مع معالجة الأخطاء ورسائل مفصلة
                script_lines.append(':do {')
                script_lines.append(f'    {cmd};')
                script_lines.append('    :set success ($success + 1);')

                # رسائل تقدم مفصلة
                script_lines.append(f'    :if (($success % 50) = 0) do={{')
                script_lines.append(f'        :put "✅ تم إضافة $success من {len(self.generated_credentials)} حساب...";')

                # إضافة تفاصيل الحساب الحالي في رسائل التقدم
                details = []
                if cred.get("limit_bytes") and cred.get("limit_unit"):
                    details.append(f'حد البيانات: {cred["limit_bytes"]} {cred["limit_unit"]}')
                if cred.get("days") and cred.get("email_template"):
                    details.append(f'إيميل: {cred["days"]}{cred["email_template"]}')

                if details:
                    script_lines.append(f'        :put "   📊 آخر حساب: {username} ({", ".join(details)})";')
                else:
                    script_lines.append(f'        :put "   👤 آخر حساب: {username}";')

                script_lines.append('    };')
                script_lines.append('} on-error={')
                script_lines.append('    :set errors ($errors + 1);')
                script_lines.append(f'    :put "❌ خطأ في إضافة المستخدم: {username}";')

                # إضافة تفاصيل الخطأ
                if cred.get("limit_bytes") or cred.get("days"):
                    script_lines.append(f'    :put "   🔍 تحقق من صحة البروفايل والإعدادات للمستخدم: {username}";')

                script_lines.append('};')
                script_lines.append("")

                # تحديث التقدم كل 10 حسابات
                if i % 10 == 0:
                    status_label.configure(text=f"إنشاء السكربت... {i+1}/{len(self.generated_credentials)}")
                    self.root.update()

            # إضافة تشغيل سكربت قبل التنظيف إذا تم تفعيل الخيار
            if self.run_script_before_cleanup_var.get():
                script_to_run = self.script_to_run_entry.get().strip()
                if script_to_run:
                    script_lines.extend([
                        ':put "⏳ انتظار 3 ثواني قبل تشغيل السكريبت المحدد...";',
                        '/delay 3s;',
                        '',
                        f':put "🔧 تشغيل السكريبت: {script_to_run}";',
                        f':do {{ /sys script run "{script_to_run}"; }} on-error={{ :put "❌ خطأ في تشغيل السكريبت: {script_to_run}"; }};',
                        ':put "✅ تم تشغيل السكريبت بنجاح!";',
                        ''
                    ])

            # إضافة النتائج النهائية مع تفاصيل الخصائص
            script_lines.extend([
                ':put "🎉 تم الانتهاء من إضافة الحسابات!";',
                ':put "📈 النتائج النهائية:";',
                ':put "   ✅ نجح: $success حساب";',
                ':put "   ❌ فشل: $errors حساب";',
                f':put "   📊 الإجمالي: {len(self.generated_credentials)} حساب";',
                '',
                ':put "🔍 الخصائص المطبقة:";'
            ])

            # إضافة تفاصيل الخصائص المطبقة
            if profile:
                script_lines.append(f':put "   📋 البروفايل: {profile}";')
            if server and server != "all":
                script_lines.append(f':put "   🖥️ الخادم: {server}";')
            if comment:
                script_lines.append(f':put "   💬 التعليق: {comment}";')

            # إحصائيات الخصائص الإضافية
            accounts_with_limit = sum(1 for cred in self.generated_credentials if cred.get("limit_bytes") and cred.get("limit_unit"))
            accounts_with_email = sum(1 for cred in self.generated_credentials if cred.get("days") and cred.get("email_template"))

            if accounts_with_limit > 0:
                script_lines.append(f':put "   📊 حسابات بحد بيانات: {accounts_with_limit}";')
            if accounts_with_email > 0:
                script_lines.append(f':put "   📧 حسابات بإيميل: {accounts_with_email}";')

            script_lines.extend([
                '',
                ':put "🧹 بدء عملية التنظيف التلقائي...";',
                ''
            ])

            # إضافة أوامر الحذف التلقائي
            script_lines.extend([
                '# حذف السكربت والمهمة المجدولة تلقائياً',
                ':do {',
                f'    /system script remove [find name="{script_name}"];',
                f'    :put "✅ تم حذف السكربت: {script_name}";',
                '} on-error={',
                f'    :put "⚠️ تعذر حذف السكربت: {script_name}";',
                '};',
                '',
                ':do {',
                f'    /system scheduler remove [find name="{task_name}"];',
                f'    :put "✅ تم حذف المهمة المجدولة: {task_name}";',
                '} on-error={',
                f'    :put "⚠️ تعذر حذف المهمة المجدولة: {task_name}";',
                '};',
                '',
                ':put "🎯 تم الانتهاء من التنظيف التلقائي بنجاح!";',
                f':put "📋 تمت إضافة {len(self.generated_credentials)} حساب Hotspot وتنظيف النظام تلقائياً";'
            ])

            script_content = "\n".join(script_lines)
            self.logger.info(f"تم إنشاء سكربت محسن يحتوي على {len(self.generated_credentials)} حساب مع حذف تلقائي")

            return script_content

        except Exception as e:
            error_msg = f"خطأ في إنشاء محتوى السكربت: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def add_script_to_mikrotik(self, api, script_name, script_content, version_value, status_label):
        """إضافة السكربت إلى MikroTik"""
        try:
            status_label.configure(text="إضافة السكربت إلى MikroTik...")
            self.root.update()

            # تحديد المسار حسب الإصدار
            if version_value == "v6":
                scripts_path = '/system/script'
            else:
                scripts_path = '/system/script'

            scripts_resource = api.get_resource(scripts_path)

            # إضافة السكربت
            scripts_resource.add(
                name=script_name,
                source=script_content,
                comment=f"سكربت حسابات Hotspot - {len(self.generated_credentials)} حساب"
            )

            self.logger.info(f"تم إضافة السكربت إلى MikroTik: {script_name}")

        except Exception as e:
            error_msg = f"خطأ في إضافة السكربت إلى MikroTik: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def schedule_script_execution(self, api, script_name, task_name, version_value, status_label):
        """جدولة تشغيل السكربت بعد 5 ثواني مع حذف تلقائي"""
        try:
            status_label.configure(text="إضافة مهمة الجدولة...")
            self.root.update()

            # تحديد المسار حسب الإصدار
            if version_value == "v6":
                scheduler_path = '/system/scheduler'
            else:
                scheduler_path = '/system/scheduler'

            scheduler_resource = api.get_resource(scheduler_path)

            # حساب الوقت بعد 5 ثواني
            import datetime
            future_time = datetime.datetime.now() + datetime.timedelta(seconds=5)
            start_time = future_time.strftime("%H:%M:%S")
            start_date = future_time.strftime("%b/%d/%Y")

            # إضافة مهمة الجدولة
            scheduler_resource.add(
                name=task_name,
                start_time=start_time,
                start_date=start_date,
                interval="00:00:00",  # تشغيل مرة واحدة فقط
                on_event=f'/system script run "{script_name}"',
                comment=f"تشغيل سكربت {script_name} مع حذف تلقائي - مرة واحدة"
            )

            self.logger.info(f"تم جدولة تشغيل السكربت: {task_name} في {start_time} مع حذف تلقائي")

        except Exception as e:
            error_msg = f"خطأ في جدولة تشغيل السكربت: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)

    def save_as_pdf(self):
        if not self.generated_credentials:
            messagebox.showwarning("تحذير", "يرجى توليد الحسابات أولاً")
            return

        pdf_filename = self.get_pdf_filename()
        if pdf_filename:
            self.save_pdf_to_file(pdf_filename)
        else:
            messagebox.showinfo("إلغاء", "تم إلغاء حفظ الملف.")

    def save_template(self):
        template_name = self.template_combo.get().strip()
        if not template_name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم للقالب")
            return

        template = {
            "system_type": self.system_type,
            "customer": self.customer_entry.get() if self.system_type == 'user_manager' else "",
            "server": self.server_entry.get() if self.system_type == 'hotspot' else "",
            "version": self.version_combo.get(),
            "profile": self.profile_combo.get(),
            "comment": self.comment_entry.get(),
            "location": self.location_entry.get(),
            "limit_bytes": self.limit_bytes_entry.get() if self.system_type == 'hotspot' else "",
            "limit_unit": self.limit_unit_combo.get() if self.system_type == 'hotspot' else "",
            "days": self.days_entry.get() if self.system_type == 'hotspot' else "",
            "email_template": self.email_template_entry.get() if self.system_type == 'hotspot' else "",
            "cred_type": self.cred_type_combo.get(),
            "cred_match": self.cred_match_combo.get(),
            "length": self.length_entry.get(),
            "count": self.count_entry.get(),
            "prefix": self.prefix_entry.get(),
            "suffix": self.suffix_entry.get(),
            "pass_suffix": self.pass_suffix_entry.get(),
            "delay": self.delay_entry.get(),
            "columns": self.columns_entry.get(),
            "rows": self.rows_entry.get(),
            "spacing": self.spacing_entry.get(),
            "print_username": self.print_username_var.get(),
            "print_password": self.print_password_var.get(),
            "use_serial": self.use_serial_var.get(),
            "use_date": self.use_date_var.get(),
            "use_qr": self.use_qr_var.get(),
            "username_size": self.username_size_entry.get(),
            "username_color": self.username_color_entry.get(),
            "username_bold": self.username_bold_var.get(),
            "username_x": self.username_x_entry.get(),
            "username_y": self.username_y_entry.get(),
            "password_size": self.password_size_entry.get(),
            "password_color": self.password_color_entry.get(),
            "password_bold": self.password_bold_var.get(),
            "password_x": self.password_x_entry.get(),
            "password_y": self.password_y_entry.get(),
            "serial_start": self.serial_start_entry.get(),
            "serial_size": self.serial_size_entry.get(),
            "serial_color": self.serial_color_entry.get(),
            "serial_bold": self.serial_bold_var.get(),
            "serial_x": self.serial_x_entry.get(),
            "serial_y": self.serial_y_entry.get(),
            "date_size": self.date_size_entry.get(),
            "date_color": self.date_color_entry.get(),
            "date_bold": self.date_bold_var.get(),
            "date_x": self.date_x_entry.get(),
            "date_y": self.date_y_entry.get(),
            "qr_size": self.qr_size_entry.get(),
            "qr_x": self.qr_x_entry.get(),
            "qr_y": self.qr_y_entry.get(),
            "background_image_path": self.background_image_path or "",
            "user_email": self.user_email.get(),
            "caller_id_bind": self.caller_id_bind_var.get(),
            "use_price": self.use_price_var.get(),
            "price_value": self.price_value_entry.get(),
            "price_size": self.price_size_entry.get(),
            "price_color": self.price_color_entry.get(),
            "price_bold": self.price_bold_var.get(),
            "price_x": self.price_x_entry.get(),
            "price_y": self.price_y_entry.get(),
            "price": self.price_entry.get() if hasattr(self, 'price_entry') else "",
            "run_script_before_cleanup": self.run_script_before_cleanup_var.get(),
            "script_to_run": self.script_to_run_entry.get()
        }

        try:
            if self.system_type == 'user_manager':
                templates_file = self.user_manager_templates_file
            else:
                templates_file = self.hotspot_templates_file

            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
        except FileNotFoundError:
            templates = {}

        templates[template_name] = template

        with open(templates_file, 'w', encoding='utf-8') as f:
            json.dump(templates, f, ensure_ascii=False, indent=2)

        self.load_templates()
        messagebox.showinfo("نجاح", f"تم حفظ القالب: {template_name}")

    def load_templates(self):
        try:
            if self.system_type == 'user_manager':
                templates_file = self.user_manager_templates_file
            else:
                templates_file = self.hotspot_templates_file

            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
            self.template_combo['values'] = list(templates.keys())
            if templates:
                self.template_combo.set(list(templates.keys())[0])
        except FileNotFoundError:
            self.template_combo['values'] = []

    def load_template(self, event=None):
        template_name = self.template_combo.get().strip()
        if not template_name:
            return

        try:
            if self.system_type == 'user_manager':
                templates_file = self.user_manager_templates_file
            else:
                templates_file = self.hotspot_templates_file

            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
            template = templates.get(template_name)
            if template:
                # Set system type if different
                if template.get("system_type") and template["system_type"] != self.system_type:
                    self.system_type = template["system_type"]
                    for widget in self.root.winfo_children():
                        widget.destroy()
                    self.setup_gui()

                # Load common settings
                self.version_combo.set(template["version"])
                self.profile_combo.set(template["profile"])
                self.comment_entry.delete(0, tk.END); self.comment_entry.insert(0, template["comment"])
                self.location_entry.delete(0, tk.END); self.location_entry.insert(0, template["location"])
                self.cred_type_combo.set(template["cred_type"])
                self.cred_match_combo.set(template["cred_match"])
                self.length_entry.delete(0, tk.END); self.length_entry.insert(0, template["length"])
                self.count_entry.delete(0, tk.END); self.count_entry.insert(0, template["count"])
                self.prefix_entry.delete(0, tk.END); self.prefix_entry.insert(0, template["prefix"])
                self.suffix_entry.delete(0, tk.END); self.suffix_entry.insert(0, template["suffix"])
                self.pass_suffix_entry.delete(0, tk.END); self.pass_suffix_entry.insert(0, template["pass_suffix"])
                self.delay_entry.delete(0, tk.END); self.delay_entry.insert(0, template["delay"])

                # Load system-specific settings
                if self.system_type == 'user_manager':
                    self.customer_entry.delete(0, tk.END); self.customer_entry.insert(0, template["customer"])
                    self.user_email.set(template["user_email"])
                    self.caller_id_bind_var.set(template["caller_id_bind"])
                    if hasattr(self, 'price_entry'):
                        self.price_entry.delete(0, tk.END); self.price_entry.insert(0, template.get("price", ""))
                elif self.system_type == 'hotspot':
                    self.server_entry.delete(0, tk.END); self.server_entry.insert(0, template["server"])
                    self.limit_bytes_entry.delete(0, tk.END); self.limit_bytes_entry.insert(0, template["limit_bytes"])
                    self.limit_unit_combo.set(template["limit_unit"])
                    self.days_entry.delete(0, tk.END); self.days_entry.insert(0, template["days"])
                    self.email_template_entry.delete(0, tk.END); self.email_template_entry.insert(0, template["email_template"])
                    if hasattr(self, 'price_entry'):
                        self.price_entry.delete(0, tk.END); self.price_entry.insert(0, template.get("price", ""))

                # Load PDF settings
                self.columns_entry.delete(0, tk.END); self.columns_entry.insert(0, template["columns"])
                self.rows_entry.delete(0, tk.END); self.rows_entry.insert(0, template["rows"])
                self.spacing_entry.delete(0, tk.END); self.spacing_entry.insert(0, template["spacing"])
                self.print_username_var.set(template["print_username"])
                self.print_password_var.set(template["print_password"])
                self.use_serial_var.set(template["use_serial"])
                self.use_date_var.set(template["use_date"])
                self.use_qr_var.set(template["use_qr"])
                self.username_size_entry.delete(0, tk.END); self.username_size_entry.insert(0, template["username_size"])
                self.username_color_entry.delete(0, tk.END); self.username_color_entry.insert(0, template["username_color"])
                self.username_bold_var.set(template["username_bold"])
                self.username_x_entry.delete(0, tk.END); self.username_x_entry.insert(0, template["username_x"])
                self.username_y_entry.delete(0, tk.END); self.username_y_entry.insert(0, template["username_y"])
                self.password_size_entry.delete(0, tk.END); self.password_size_entry.insert(0, template["password_size"])
                self.password_color_entry.delete(0, tk.END); self.password_color_entry.insert(0, template["password_color"])
                self.password_bold_var.set(template["password_bold"])
                self.password_x_entry.delete(0, tk.END); self.password_x_entry.insert(0, template["password_x"])
                self.password_y_entry.delete(0, tk.END); self.password_y_entry.insert(0, template["password_y"])
                self.serial_start_entry.delete(0, tk.END); self.serial_start_entry.insert(0, template["serial_start"])
                self.serial_size_entry.delete(0, tk.END); self.serial_size_entry.insert(0, template["serial_size"])
                self.serial_color_entry.delete(0, tk.END); self.serial_color_entry.insert(0, template["serial_color"])
                self.serial_bold_var.set(template["serial_bold"])
                self.serial_x_entry.delete(0, tk.END); self.serial_x_entry.insert(0, template["serial_x"])
                self.serial_y_entry.delete(0, tk.END); self.serial_y_entry.insert(0, template["serial_y"])
                self.date_size_entry.delete(0, tk.END); self.date_size_entry.insert(0, template["date_size"])
                self.date_color_entry.delete(0, tk.END); self.date_color_entry.insert(0, template["date_color"])
                self.date_bold_var.set(template["date_bold"])
                self.date_x_entry.delete(0, tk.END); self.date_x_entry.insert(0, template["date_x"])
                self.date_y_entry.delete(0, tk.END); self.date_y_entry.insert(0, template["date_y"])
                self.qr_size_entry.delete(0, tk.END); self.qr_size_entry.insert(0, template["qr_size"])
                self.qr_x_entry.delete(0, tk.END); self.qr_x_entry.insert(0, template["qr_x"])
                self.qr_y_entry.delete(0, tk.END); self.qr_y_entry.insert(0, template["qr_y"])
                self.use_price_var.set(template["use_price"])
                self.price_value_entry.delete(0, tk.END); self.price_value_entry.insert(0, template["price_value"])
                self.price_size_entry.delete(0, tk.END); self.price_size_entry.insert(0, template["price_size"])
                self.price_color_entry.delete(0, tk.END); self.price_color_entry.insert(0, template["price_color"])
                self.price_bold_var.set(template["price_bold"])
                self.price_x_entry.delete(0, tk.END); self.price_x_entry.insert(0, template["price_x"])
                self.price_y_entry.delete(0, tk.END); self.price_y_entry.insert(0, template["price_y"])
                self.run_script_before_cleanup_var.set(template["run_script_before_cleanup"])
                self.script_to_run_entry.delete(0, tk.END); self.script_to_run_entry.insert(0, template["script_to_run"])

                if template["background_image_path"] and os.path.exists(template["background_image_path"]):
                    self.background_image_path = template["background_image_path"]
                    self.background_image = Image.open(self.background_image_path)
                else:
                    self.background_image_path = None
                    self.background_image = None

                self.toggle_pass_suffix()
                self.toggle_username_inputs()
                self.toggle_password_inputs()
                self.toggle_serial_inputs()
                self.toggle_date_inputs()
                self.toggle_qr_inputs()
                self.toggle_price_inputs()
                self.update_preview()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل تحميل القالب: {str(e)}")

    def delete_template(self):
        template_name = self.template_combo.get().strip()
        if not template_name:
            messagebox.showwarning("تحذير", "يرجى اختيار قالب لحذفه")
            return

        try:
            if self.system_type == 'user_manager':
                templates_file = self.user_manager_templates_file
            else:
                templates_file = self.hotspot_templates_file

            with open(templates_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
            if template_name in templates:
                del templates[template_name]
                with open(templates_file, 'w', encoding='utf-8') as f:
                    json.dump(templates, f, ensure_ascii=False, indent=2)
                self.load_templates()
                messagebox.showinfo("نجاح", f"تم حذف القالب: {template_name}")
            else:
                messagebox.showwarning("تحذير", "القالب غير موجود")
        except FileNotFoundError:
            messagebox.showwarning("تحذير", "لا توجد قوالب محفوظة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل حذف القالب: {str(e)}")

def main():
    """تشغيل التطبيق الرئيسي"""
    try:
        # إعداد النافذة الرئيسية
        root = tk.Tk()

        # تعيين أيقونة التطبيق إذا كانت متوفرة
        try:
            root.iconbitmap('icon.ico')
        except:
            pass  # تجاهل إذا لم تكن الأيقونة متوفرة

        # تعيين الحد الأدنى لحجم النافذة
        root.minsize(800, 600)

        # توسيط النافذة على الشاشة
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f"{width}x{height}+{x}+{y}")

        # إنشاء التطبيق
        app = MikroTikCardGenerator(root)

        # تشغيل التطبيق
        root.mainloop()

    except Exception as e:
        # في حالة حدوث خطأ في التشغيل
        import traceback
        error_msg = f"خطأ في تشغيل التطبيق:\n{str(e)}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}"

        try:
            import tkinter.messagebox as mb
            mb.showerror("خطأ في التطبيق", error_msg)
        except:
            print(error_msg)

if __name__ == "__main__":
    main()