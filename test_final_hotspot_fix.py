#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لإصلاح مشكلة إرسال الهوت سبوت
"""

import sys
import os
import json
import traceback

def test_final_hotspot_fix():
    """اختبار نهائي لإصلاح الهوت سبوت"""
    print("🎯 اختبار نهائي لإصلاح مشكلة إرسال الهوت سبوت")
    print("=" * 70)
    
    try:
        # قراءة إعدادات الاتصال
        config_file = "config/mikrotik_settings.json"
        if not os.path.exists(config_file):
            print("❌ ملف الإعدادات غير موجود")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            settings = json.load(f)
        
        # فك ترميز كلمة المرور
        def decrypt_password(encrypted_password):
            try:
                if not encrypted_password:
                    return ""
                import base64
                decoded = base64.b64decode(encrypted_password.encode('utf-8')).decode('utf-8')
                return decoded
            except Exception as e:
                return encrypted_password
        
        decrypted_password = decrypt_password(settings.get('api_password', ''))
        
        print("📋 إعدادات الاتصال:")
        print(f"   🌐 IP: {settings.get('api_ip', 'غير محدد')}")
        print(f"   👤 Username: {settings.get('api_username', 'غير محدد')}")
        print(f"   🔑 Password: {'***' if decrypted_password else 'غير محدد'}")
        
        # التحقق من المكتبة المطلوبة
        try:
            import routeros_api
            print("✅ مكتبة routeros_api متوفرة")
        except ImportError:
            print("❌ مكتبة routeros_api غير متوفرة")
            return False
        
        # محاولة الاتصال
        print("\n🔗 محاولة الاتصال بـ MikroTik...")
        
        try:
            api_connection = routeros_api.RouterOsApiPool(
                host=settings['api_ip'],
                username=settings['api_username'],
                password=decrypted_password,
                port=int(settings.get('api_port', 8728)),
                plaintext_login=True
            )
            
            api = api_connection.get_api()
            
            # اختبار الاتصال
            identity = api.get_resource('/system/identity').get()
            router_name = identity[0].get('name', 'غير معروف') if identity else 'غير معروف'
            print(f"✅ نجح الاتصال مع MikroTik: {router_name}")
            
            # اختبار قراءة مستخدمي الهوت سبوت مع معالجة أخطاء الترميز
            print("\n📊 اختبار قراءة مستخدمي الهوت سبوت...")
            try:
                hotspot_users = api.get_resource('/ip/hotspot/user').get()
                print(f"   ✅ تم قراءة {len(hotspot_users)} مستخدم بنجاح")
                
                # فحص أول 3 مستخدمين للتأكد من عدم وجود مشاكل ترميز
                if hotspot_users:
                    print("   👥 عينة من المستخدمين:")
                    for i, user in enumerate(hotspot_users[:3]):
                        try:
                            name = user.get('name', 'غير معروف')
                            profile = user.get('profile', 'غير محدد')
                            print(f"      {i+1}. {name} - {profile}")
                        except UnicodeDecodeError:
                            print(f"      {i+1}. [مستخدم بترميز غير متوافق] - يحتاج تنظيف")
                
            except UnicodeDecodeError as unicode_error:
                print(f"   ⚠️ مشكلة ترميز في قراءة المستخدمين: {str(unicode_error)}")
                print("   💡 يُنصح بتنظيف قاعدة البيانات أولاً")
            except Exception as read_error:
                print(f"   ❌ خطأ في قراءة المستخدمين: {str(read_error)}")
            
            # اختبار إنشاء مستخدم جديد مع الإصلاحات
            print("\n🧪 اختبار إنشاء مستخدم جديد...")
            
            from datetime import datetime
            timestamp = datetime.now().strftime("%H%M%S")
            test_username = f"test_fix_{timestamp}"
            
            # دالة تنظيف محسنة (مطابقة للإصلاح)
            def clean_text_for_mikrotik(text):
                if not text:
                    return ""
                try:
                    text_str = str(text)
                    clean_text = text_str.encode('ascii', 'ignore').decode('ascii')
                    clean_text = clean_text.strip()
                    if not clean_text or not clean_text.replace(' ', '').replace('-', '').replace('_', ''):
                        return ""
                    return clean_text
                except Exception:
                    return ""
            
            # بيانات المستخدم الاختبار مع تنظيف
            test_params = {
                'name': clean_text_for_mikrotik(test_username),
                'password': clean_text_for_mikrotik("test123"),
                'profile': clean_text_for_mikrotik("default")
            }
            
            # إضافة حقول اختيارية فقط إذا كانت نظيفة
            test_comment = clean_text_for_mikrotik("اختبار إصلاح الترميز")
            if test_comment:
                test_params['comment'] = test_comment
            
            test_email = clean_text_for_mikrotik("<EMAIL>")
            if test_email and '@' in test_email:
                test_params['email'] = test_email
            
            print(f"   📝 بيانات المستخدم الاختبار:")
            for key, value in test_params.items():
                print(f"      • {key}: '{value}'")
            
            # محاولة إضافة المستخدم
            try:
                result = api.get_resource('/ip/hotspot/user').add(**test_params)
                print(f"   ✅ تم إنشاء المستخدم الاختبار بنجاح!")
                print(f"   🆔 ID: {result}")
                
                # محاولة حذف المستخدم الاختبار
                print("\n🗑️ حذف المستخدم الاختبار...")
                api.get_resource('/ip/hotspot/user').remove(result)
                print("   ✅ تم حذف المستخدم الاختبار بنجاح!")
                
            except Exception as add_error:
                print(f"   ❌ فشل في إنشاء المستخدم الاختبار: {str(add_error)}")
                
                # تحليل نوع الخطأ
                error_str = str(add_error).lower()
                if "unicode" in error_str or "decode" in error_str:
                    print("   💡 السبب: مشكلة ترميز - تم تطبيق الإصلاح جزئياً")
                elif "invalid" in error_str:
                    print("   💡 السبب: معاملات غير صحيحة")
                elif "already exists" in error_str:
                    print("   💡 السبب: المستخدم موجود مسبقاً")
                else:
                    print("   💡 السبب: خطأ غير معروف")
                
                return False
            
            # إغلاق الاتصال
            api_connection.disconnect()
            print("\n🔌 تم قطع الاتصال بنجاح")
            
            print("\n🎉 اختبار إصلاح الهوت سبوت نجح بالكامل!")
            print("\n📋 النتائج:")
            print("   ✅ الاتصال بـ MikroTik يعمل")
            print("   ✅ قراءة مستخدمي الهوت سبوت تعمل (أو تم تحديد المشكلة)")
            print("   ✅ إنشاء مستخدم جديد يعمل مع الإصلاحات")
            print("   ✅ حذف المستخدم يعمل")
            print("   ✅ دالة تنظيف النصوص المحسنة تعمل")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء الاختبار النهائي لإصلاح الهوت سبوت")
    print("=" * 80)
    
    response = input("هل تريد تشغيل الاختبار النهائي؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم']:
        success = test_final_hotspot_fix()
        
        print("\n" + "=" * 80)
        if success:
            print("🎯 الخلاصة النهائية: إصلاح الهوت سبوت نجح!")
            print("\n💡 التوصيات:")
            print("   1. الإصلاحات المطبقة تعمل بشكل صحيح")
            print("   2. يمكن الآن إرسال كروت الهوت سبوت بأمان")
            print("   3. دالة تنظيف النصوص محسنة ومحدثة")
            print("   4. معالجة أخطاء الترميز مطبقة")
        else:
            print("🔧 الخلاصة النهائية: هناك مشاكل تحتاج مراجعة إضافية.")
            print("\n💡 التوصيات:")
            print("   1. راجع ملف السجل للتفاصيل")
            print("   2. تأكد من تنظيف قاعدة بيانات MikroTik")
            print("   3. جرب إرسال مستخدم واحد فقط للاختبار")
    else:
        print("تم إلغاء الاختبار.")
    
    input("\nاضغط Enter للخروج...")
