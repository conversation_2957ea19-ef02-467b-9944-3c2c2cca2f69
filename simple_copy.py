import os
import sys

try:
    # قراءة الملف الأصلي
    with open('الاصلي.py', 'r', encoding='utf-8') as source:
        content = source.read()
    
    # تطبيق التعديلات
    content = content.replace(
        'الإصدار 2.0',
        'الإصدار المعدل'
    )
    
    content = content.replace(
        'self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت',
        '''self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت
        
        # إضافة متغير لفتح PDF تلقائياً (التعديل الجديد)
        self.auto_open_pdf_var = tk.BooleanVar(value=True)'''
    )
    
    # كتابة الملف المعدل
    with open('المعدل.py', 'w', encoding='utf-8') as target:
        target.write(content)
    
    print("تم نسخ الملف بنجاح")
    
    # التحقق
    if os.path.exists('المعدل.py'):
        size = os.path.getsize('المعدل.py')
        print(f"حجم الملف: {size} بايت")
    
except Exception as e:
    print(f"خطأ: {e}")
    sys.exit(1)
