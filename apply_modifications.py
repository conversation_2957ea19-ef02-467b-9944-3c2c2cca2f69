#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق التعديلات المطلوبة على الملف الأصلي لإنشاء الملف المعدل
"""

import re
import os

def apply_modifications():
    """تطبيق التعديلات المطلوبة"""
    try:
        # قراءة الملف الأصلي
        with open('الاصلي.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📖 تم قراءة الملف الأصلي")
        
        # التعديل 1: تغيير العنوان
        content = content.replace(
            'self.root.title("🚀 مولد كروت وسكريبتات MikroTik - الإصدار 2.0")',
            'self.root.title("🚀 مولد كروت وسكريبتات MikroTik - الإصدار المعدل")'
        )
        print("✅ تم تعديل العنوان")
        
        # التعديل 2: إضافة متغير فتح PDF تلقائياً
        content = content.replace(
            'self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت',
            '''self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت
        
        # إضافة متغير لفتح PDF تلقائياً (التعديل الجديد)
        self.auto_open_pdf_var = tk.BooleanVar(value=True)'''
        )
        print("✅ تم إضافة متغير فتح PDF تلقائياً")
        
        # التعديل 3: تعديل دالة generate_enhanced_email لاستخدام .pro للهوت سبوت
        # البحث عن الدالة وتعديلها
        enhanced_email_pattern = r'(def generate_enhanced_email.*?return enhanced_email)'
        match = re.search(enhanced_email_pattern, content, re.DOTALL)
        if match:
            old_function = match.group(1)
            # تعديل الدالة لاستخدام .pro للهوت سبوت
            new_function = old_function.replace(
                'email_suffix = ".bot"',
                '''# تحديد نهاية الإيميل حسب النظام
            if self.system_type == 'hotspot':
                email_suffix = ".pro"  # للهوت سبوت
            else:
                email_suffix = ".bot"  # للأنظمة الأخرى'''
            )
            content = content.replace(old_function, new_function)
            print("✅ تم تعديل دالة الإيميل المحسن للهوت سبوت")
        
        # التعديل 4: تعديل دالة get_pdf_filename لتشمل السعر والتاريخ والوقت
        pdf_filename_pattern = r'(def get_pdf_filename\(self\):.*?return filename)'
        match = re.search(pdf_filename_pattern, content, re.DOTALL)
        if match:
            old_function = match.group(1)
            new_function = '''def get_pdf_filename(self):
        # إنشاء اسم ملف محسن مع السعر والتاريخ والوقت وعدد الكروت
        now = datetime.now()
        date = now.strftime("%Y-%m-%d")
        time = now.strftime("%H-%M-%S")
        prefix = self.prefix_entry.get().strip()
        count = len(self.generated_credentials)
        
        # الحصول على السعر
        price = ""
        if hasattr(self, 'price_entry') and self.price_entry.get().strip():
            price = f"_سعر{self.price_entry.get().strip()}"
        elif hasattr(self, 'price_value_entry') and self.price_value_entry.get().strip():
            price = f"_سعر{self.price_value_entry.get().strip()}"
        
        system_prefix = "usermanager" if self.system_type == 'user_manager' else "hotspot"
        default_filename = f"كروت_{system_prefix}_{prefix}_عدد{count}{price}_{date}_{time}.pdf"

        filename = filedialog.asksaveasfilename(
            defaultextension=".pdf", 
            filetypes=[("PDF files", "*.pdf")], 
            initialfile=default_filename
        )
        return filename'''
            content = content.replace(old_function, new_function)
            print("✅ تم تعديل دالة اسم ملف PDF")
        
        # التعديل 5: إضافة أرقام الأعمدة في PDF
        # البحث عن دالة save_pdf_to_file وتعديلها
        save_pdf_pattern = r'(# رسم الكروت.*?c\.save\(\))'
        match = re.search(save_pdf_pattern, content, re.DOTALL)
        if match:
            old_section = match.group(1)
            # إضافة كود أرقام الأعمدة قبل رسم الكروت
            new_section = old_section.replace(
                '# رسم الكروت',
                '''# رسم أرقام الأعمدة فوق الكروت (التعديل الجديد)
            for col in range(columns):
                x = margin_x + col * (box_width + spacing)
                y = page_height - margin_y + 5  # فوق الكروت بـ 5mm
                c.setFont("Arial", 8)
                c.setFillColor("black")
                c.drawCentredText(x + box_width/2, y, str(col + 1))
            
            # رسم الكروت'''
            )
            content = content.replace(old_section, new_section)
            print("✅ تم إضافة أرقام الأعمدة في PDF")
        
        # التعديل 6: إضافة زر علامة صح لفتح PDF تلقائياً في setup_pdf_tab
        pdf_tab_pattern = r'(def setup_pdf_tab\(self\):.*?)(def [^(]*\(self.*?:)'
        match = re.search(pdf_tab_pattern, content, re.DOTALL)
        if match:
            old_function = match.group(1)
            next_function = match.group(2)
            
            # إضافة زر علامة صح قبل نهاية الدالة
            checkbox_code = '''
        # إضافة زر علامة صح لفتح PDF تلقائياً (التعديل الجديد)
        auto_open_frame = ttk.Frame(pdf_settings_frame)
        auto_open_frame.pack(fill=tk.X, pady=5)
        
        self.auto_open_checkbox = ttk.Checkbutton(
            auto_open_frame,
            text="فتح PDF تلقائياً بعد الإنشاء",
            variable=self.auto_open_pdf_var,
            style="TCheckbutton"
        )
        self.auto_open_checkbox.pack(anchor="w")

    '''
            new_function = old_function + checkbox_code
            content = content.replace(old_function + next_function, new_function + next_function)
            print("✅ تم إضافة زر علامة صح لفتح PDF تلقائياً")
        
        # التعديل 7: تعديل دالة save_as_pdf لفتح PDF تلقائياً
        save_as_pdf_pattern = r'(messagebox\.showinfo\("نجاح", f"تم حفظ ملف PDF: \{filename\}"\))'
        if save_as_pdf_pattern in content:
            content = content.replace(
                'messagebox.showinfo("نجاح", f"تم حفظ ملف PDF: {filename}")',
                '''messagebox.showinfo("نجاح", f"تم حفظ ملف PDF: {filename}")
                
                # فتح PDF تلقائياً إذا كان الخيار مفعل (التعديل الجديد)
                if self.auto_open_pdf_var.get():
                    try:
                        import subprocess
                        import platform
                        
                        if platform.system() == 'Windows':
                            os.startfile(filename)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.run(['open', filename])
                        else:  # Linux
                            subprocess.run(['xdg-open', filename])
                        
                        self.logger.info(f"تم فتح PDF تلقائياً: {filename}")
                    except Exception as e:
                        self.logger.error(f"خطأ في فتح PDF تلقائياً: {str(e)}")'''
            )
            print("✅ تم تعديل دالة save_as_pdf لفتح PDF تلقائياً")
        
        # كتابة الملف المعدل
        with open('المعدل.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("🎉 تم إنشاء الملف المعدل بنجاح!")
        
        # التحقق من حجم الملف
        original_size = os.path.getsize('الاصلي.py')
        modified_size = os.path.getsize('المعدل.py')
        
        print(f"📊 حجم الملف الأصلي: {original_size:,} بايت")
        print(f"📊 حجم الملف المعدل: {modified_size:,} بايت")
        print(f"📈 الفرق: {modified_size - original_size:+,} بايت")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق التعديلات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء تطبيق التعديلات على الملف الأصلي")
    print("=" * 60)
    
    success = apply_modifications()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 تم تطبيق جميع التعديلات بنجاح!")
        print("\n📋 التعديلات المطبقة:")
        print("   ✅ تغيير العنوان إلى 'الإصدار المعدل'")
        print("   ✅ إضافة متغير فتح PDF تلقائياً")
        print("   ✅ تعديل الإيميل للهوت سبوت (.pro)")
        print("   ✅ تحسين اسم ملف PDF (سعر + تاريخ + وقت + عدد)")
        print("   ✅ إضافة أرقام الأعمدة في PDF")
        print("   ✅ إضافة زر علامة صح لفتح PDF تلقائياً")
        print("   ✅ تفعيل فتح PDF تلقائياً بعد الإنشاء")
        print("\n🎊 الملف 'المعدل.py' جاهز للاستخدام!")
    else:
        print("🔧 فشل في تطبيق بعض التعديلات")
    
    input("\nاضغط Enter للخروج...")
