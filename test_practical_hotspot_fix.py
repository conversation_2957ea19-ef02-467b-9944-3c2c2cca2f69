#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عملي لإصلاح مشكلة إرسال الهوت سبوت إلى MikroTik
"""

import sys
import os
import traceback
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_practical_hotspot_send():
    """اختبار عملي لإرسال الهوت سبوت"""
    print("🔧 اختبار عملي لإرسال الهوت سبوت إلى MikroTik")
    print("=" * 60)

    try:
        # استيراد البرنامج الرئيسي
        from card import MikroTikCardGenerator

        print("✅ تم استيراد البرنامج الرئيسي بنجاح")

        # إنشاء نافذة مؤقتة للاختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية

        # إنشاء مثيل من البرنامج
        app = MikroTikCardGenerator(root)
        print("✅ تم إنشاء مثيل من البرنامج بنجاح")

        # اختبار دالة تنظيف النصوص المحسنة
        test_clean_function_enhanced(app)

        # اختبار إعدادات الاتصال
        test_connection_settings(app)

        # اختبار توليد بيانات الهوت سبوت
        test_hotspot_data_generation_enhanced(app)

        # اختبار دالة الإرسال (محاكاة)
        test_send_function_simulation_enhanced(app)

        # اختبار الاتصال الفعلي (إذا كان متاحاً)
        test_actual_connection(app)

        root.destroy()
        print("\n🎉 جميع الاختبارات العملية نجحت!")
        return True

    except Exception as e:
        print(f"\n❌ خطأ في الاختبار العملي: {str(e)}")
        print(f"📍 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_clean_function_enhanced(app):
    """اختبار دالة تنظيف النصوص المحسن"""
    print("\n🧹 اختبار دالة تنظيف النصوص المحسن...")

    test_cases = [
        ("test123", "test123"),  # نص عادي
        ("مستخدم عربي", ""),  # نص عربي يجب أن يصبح فارغ
        ("<EMAIL>", "<EMAIL>"),  # إيميل
        ("", ""),  # نص فارغ
        (None, ""),  # قيمة فارغة
        ("test🚀emoji", "testemoji"),  # رموز خاصة
        ("user123", "user123"),  # اسم مستخدم عادي
        ("pass@123", "pass@123"),  # كلمة مرور
        ("default", "default"),  # بروفايل
        ("تعليق عربي", ""),  # تعليق عربي
        ("Mixed نص مختلط", "Mixed "),  # نص مختلط
    ]

    passed = 0
    total = len(test_cases)

    for input_text, expected in test_cases:
        try:
            result = app.clean_text_for_mikrotik(input_text)
            if result == expected:
                print(f"   ✅ '{input_text}' -> '{result}'")
                passed += 1
            else:
                print(f"   ⚠️ '{input_text}' -> '{result}' (متوقع: '{expected}')")
        except Exception as e:
            print(f"   ❌ خطأ في تنظيف '{input_text}': {str(e)}")

    print(f"   📊 نتيجة اختبار التنظيف: {passed}/{total} نجح")
    return passed == total

def test_connection_settings(app):
    """اختبار إعدادات الاتصال"""
    print("\n🌐 اختبار إعدادات الاتصال...")
    
    # فحص وجود حقول الاتصال
    required_fields = ['api_ip_entry', 'api_username_entry', 'api_password_entry', 'api_port_entry']
    
    for field in required_fields:
        if hasattr(app, field):
            print(f"   ✅ حقل {field} موجود")
        else:
            print(f"   ❌ حقل {field} مفقود")
    
    # فحص دالة الاتصال
    if hasattr(app, 'connect_api'):
        print("   ✅ دالة connect_api موجودة")
    else:
        print("   ❌ دالة connect_api مفقودة")

def test_hotspot_data_generation_enhanced(app):
    """اختبار توليد بيانات الهوت سبوت المحسن"""
    print("\n📊 اختبار توليد بيانات الهوت سبوت المحسن...")

    try:
        # تعيين نوع النظام إلى هوت سبوت
        app.system_type = 'hotspot'
        print("   ✅ تم تعيين نوع النظام إلى hotspot")

        # محاولة الوصول للحقول بطريقة آمنة
        fields_set = 0
        total_fields = 4

        # البحث عن الحقول في جميع الإطارات
        for widget in app.root.winfo_children():
            if hasattr(widget, 'winfo_children'):
                for child in widget.winfo_children():
                    if hasattr(child, 'winfo_children'):
                        for grandchild in child.winfo_children():
                            widget_name = str(grandchild)
                            if 'profile' in widget_name and hasattr(grandchild, 'set'):
                                try:
                                    grandchild.set("default")
                                    print("   ✅ تم تعيين البروفايل")
                                    fields_set += 1
                                    break
                                except:
                                    pass

        # إنشاء بيانات اختبار يدوياً
        test_credentials = [
            {
                "username": "test001",
                "password": "pass001",
                "profile": "default",
                "comment": "test user",
                "location": "1",
                "limit_bytes": "1",
                "limit_unit": "GB",
                "days": "7",
                "email_template": "@test.com",
                "price": "10"
            },
            {
                "username": "test002",
                "password": "pass002",
                "profile": "default",
                "comment": "test user 2",
                "location": "2",
                "limit_bytes": "2",
                "limit_unit": "GB",
                "days": "14",
                "email_template": "@test.com",
                "price": "20"
            }
        ]

        # تعيين البيانات المولدة للاختبار
        app.generated_credentials = test_credentials
        print(f"   ✅ تم إنشاء {len(test_credentials)} حساب اختبار")

        # فحص بيانات الحساب الأول
        if app.generated_credentials:
            first_cred = app.generated_credentials[0]
            print(f"   📋 بيانات الحساب الأول:")
            for key, value in first_cred.items():
                print(f"      • {key}: {value}")

        return True

    except Exception as e:
        print(f"   ❌ خطأ في توليد البيانات: {str(e)}")
        return False

def test_send_function_simulation_enhanced(app):
    """اختبار محاكاة دالة الإرسال المحسن"""
    print("\n📤 اختبار محاكاة دالة الإرسال المحسن...")

    try:
        # فحص وجود دالة الإرسال
        if hasattr(app, 'send_to_mikrotik'):
            print("   ✅ دالة send_to_mikrotik موجودة")

            # فحص وجود البيانات المولدة
            if hasattr(app, 'generated_credentials') and app.generated_credentials:
                print(f"   ✅ يوجد {len(app.generated_credentials)} حساب جاهز للإرسال")

                # محاكاة معالجة البيانات قبل الإرسال
                for i, cred in enumerate(app.generated_credentials):
                    print(f"   🔍 معالجة الحساب {i+1}:")

                    # تنظيف البيانات
                    clean_username = app.clean_text_for_mikrotik(cred["username"])
                    clean_password = app.clean_text_for_mikrotik(cred["password"])
                    clean_profile = app.clean_text_for_mikrotik(cred["profile"])
                    clean_comment = app.clean_text_for_mikrotik(cred.get("comment", ""))

                    print(f"      • اسم المستخدم: '{cred['username']}' -> '{clean_username}'")
                    print(f"      • كلمة المرور: '{cred['password']}' -> '{clean_password}'")
                    print(f"      • البروفايل: '{cred['profile']}' -> '{clean_profile}'")
                    print(f"      • التعليق: '{cred.get('comment', '')}' -> '{clean_comment}'")

                    # بناء معاملات الإرسال الكاملة
                    params = {
                        'name': clean_username,
                        'password': clean_password,
                        'profile': clean_profile,
                        'comment': clean_comment
                    }

                    # إضافة حد البيانات إذا كان موجود
                    if cred.get("limit_bytes"):
                        try:
                            limit_value = float(cred["limit_bytes"])
                            limit_unit = cred.get("limit_unit", "GB")
                            if limit_unit == "GB":
                                bytes_value = int(limit_value * 1073741824)
                            else:
                                bytes_value = int(limit_value * 1048576)
                            params['limit-bytes-total'] = str(bytes_value)
                            print(f"      • حد البيانات: {limit_value} {limit_unit} -> {bytes_value} bytes")
                        except:
                            pass

                    # إضافة الإيميل إذا كان موجود
                    if cred.get("days") and cred.get("email_template"):
                        email_value = f"{cred['days']}{cred['email_template']}"
                        clean_email = app.clean_text_for_mikrotik(email_value)
                        if clean_email and '@' in clean_email:
                            params['email'] = clean_email
                            print(f"      • الإيميل: '{email_value}' -> '{clean_email}'")

                    print(f"      ✅ معاملات الإرسال جاهزة: {len(params)} معامل")

                    # محاكاة استدعاء API
                    print(f"      🌐 محاكاة: api.get_resource('/ip/hotspot/user').add(**params)")

            else:
                print("   ⚠️ لا توجد بيانات مولدة للاختبار")
        else:
            print("   ❌ دالة send_to_mikrotik مفقودة")

        return True

    except Exception as e:
        print(f"   ❌ خطأ في محاكاة الإرسال: {str(e)}")
        return False

def test_actual_connection(app):
    """اختبار الاتصال الفعلي (إذا كان متاحاً)"""
    print("\n🌐 اختبار الاتصال الفعلي...")

    try:
        # فحص إعدادات الاتصال في ملف الإعدادات
        import os
        config_file = "config/mikrotik_settings.json"

        if os.path.exists(config_file):
            print("   ✅ ملف إعدادات MikroTik موجود")

            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            if 'api_ip' in settings and settings['api_ip']:
                print(f"   📍 عنوان IP: {settings['api_ip']}")
                print(f"   🔌 المنفذ: {settings.get('api_port', 8728)}")
                print(f"   👤 المستخدم: {settings.get('api_username', 'غير محدد')}")

                # محاولة اختبار الاتصال (بدون كلمة مرور)
                print("   ⚠️ لاختبار الاتصال الفعلي، يرجى تشغيل البرنامج والاتصال يدوياً")

            else:
                print("   ⚠️ إعدادات الاتصال غير مكتملة")
        else:
            print("   ⚠️ ملف إعدادات MikroTik غير موجود")

        return True

    except Exception as e:
        print(f"   ❌ خطأ في فحص الاتصال: {str(e)}")
        return False

def run_practical_test():
    """تشغيل الاختبار العملي"""
    print("🚀 بدء الاختبار العملي لإصلاح إرسال الهوت سبوت")
    print("=" * 70)
    
    success = test_practical_hotspot_send()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 الاختبار العملي نجح! البرنامج يبدو أنه يعمل بشكل صحيح.")
        print("\n💡 خطوات التشخيص التالية:")
        print("   1. تأكد من إعدادات الاتصال بـ MikroTik")
        print("   2. تحقق من صلاحيات المستخدم")
        print("   3. اختبر الاتصال بـ MikroTik أولاً")
        print("   4. جرب إرسال حساب واحد فقط للاختبار")
    else:
        print("❌ الاختبار العملي فشل! هناك مشاكل في البرنامج.")
        print("\n🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها.")
    
    return success

if __name__ == "__main__":
    success = run_practical_test()
    sys.exit(0 if success else 1)
