#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🎯 اختبار بسيط للطرق المطبقة")
print("=" * 50)

# محاكاة بيانات اختبار
credentials = [
    {"username": "test_user_01", "password": "pass01"},
    {"username": "test_user_02", "password": "pass02"},
    {"username": "test_user_03", "password": "pass03"}
]

print(f"✅ تم توليد {len(credentials)} بيانات اختبار")

# اختبار الطريقة السريعة من بوت التلجرام
print("\n🚀 اختبار الطريقة السريعة من بوت التلجرام...")

customer = "test_customer"
profile = "default"

# استخدام الطريقة الصحيحة من بوت التلجرام
script_lines = [':local usr {']

for i, cred in enumerate(credentials):
    # تنسيق بسيط: username=password (مطابق لبوت التلجرام)
    script_lines.append(f'    "{cred["username"]}"="{cred["password"]}" ;')

if credentials:
    script_lines[-1] = script_lines[-1].rstrip(' ;')

script_lines.extend([
    '};',
    ':foreach u,p in=$usr do={'
])

# استخدام الطريقة الصحيحة من بوت التلجرام
add_user_cmd = f'/tool user-manager user add username=$u password=$p customer="{customer}" first-name="{profile}"'
script_lines.append(f'    :do {{ {add_user_cmd}; }} on-error={{ :put "no user" }};')

activate_profile_cmd = f'/tool user-manager user create-and-activate-profile customer="{customer}" profile="{profile}" numbers=$u'
script_lines.append(f'    :do {{ {activate_profile_cmd}; }} on-error={{ :put "no profile" }};')

script_lines.append('}')

script = '\n'.join(script_lines)

print("✅ تم توليد السكريبت السريع بنجاح!")
print("\n📝 السكريبت المولد:")
print(script)

print("\n🎉 الاختبار البسيط نجح!")
print("💡 السكريبت يستخدم الطريقة الصحيحة من بوت التلجرام")

input("\nاضغط Enter للخروج...")
