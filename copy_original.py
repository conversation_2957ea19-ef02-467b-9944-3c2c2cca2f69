#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def copy_original_file():
    """نسخ الملف الأصلي بالكامل"""
    try:
        print("📖 قراءة الملف الأصلي...")
        
        # قراءة الملف الأصلي
        with open('الاصلي.py', 'r', encoding='utf-8') as source:
            content = source.read()
        
        print(f"📊 تم قراءة {len(content):,} حرف")
        
        # كتابة الملف المعدل
        print("💾 كتابة الملف المعدل...")
        with open('المعدل.py', 'w', encoding='utf-8') as target:
            target.write(content)
        
        print("✅ تم نسخ الملف بنجاح!")
        
        # التحقق
        import os
        if os.path.exists('المعدل.py'):
            size = os.path.getsize('المعدل.py')
            print(f"📊 حجم الملف المنسوخ: {size:,} بايت")
            
            with open('المعدل.py', 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
            print(f"📋 عدد الأسطر: {lines:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    copy_original_file()
